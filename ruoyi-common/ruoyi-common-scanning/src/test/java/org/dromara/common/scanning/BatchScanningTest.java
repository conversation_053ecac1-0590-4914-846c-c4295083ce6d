package org.dromara.common.scanning;

import org.dromara.common.scanning.biz.thread.EventThreadPool;
import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.commons.config.BatchConfig;
import org.dromara.common.scanning.commons.config.rpcinit.impl.EthRpcInit;
import org.dromara.common.scanning.monitor.EthMonitorEvent;
import org.dromara.common.scanning.monitor.filter.EthMonitorFilter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;

/**
 * 批量扫描功能测试
 */
public class BatchScanningTest {

    private static final Logger logger = LoggerFactory.getLogger(BatchScanningTest.class);

    public static void main(String[] args) {
        BatchScanningTest test = new BatchScanningTest();
        test.testBatchConfigCreation();
        test.testBatchConfigValidation();
        test.testMagicianBlockchainScanWithBatch();
        logger.info("All batch scanning tests completed successfully!");
    }

    public void testBatchConfigCreation() {
        // 测试批量配置创建
        BatchConfig config = BatchConfig.builder()
            .setEnabled(true)
            .setBatchSize(20)
            .setConcurrentThreads(5)
            .setBatchTimeoutMs(30000)
            .setAdaptiveBatchSize(true)
            .setMinBatchSize(5)
            .setMaxBatchSize(50)
            .setFailureRateThreshold(0.1);

        // 验证配置
        assert config.isEnabled();
        assert config.getBatchSize() == 20;
        assert config.getConcurrentThreads() == 5;
        assert config.getBatchTimeoutMs() == 30000;
        assert config.isAdaptiveBatchSize();
        assert config.getMinBatchSize() == 5;
        assert config.getMaxBatchSize() == 50;
        assert config.getFailureRateThreshold() == 0.1;

        logger.info("Batch config test passed: {}", config);
    }

    public void testBatchConfigValidation() {
        BatchConfig config = BatchConfig.getDefault();

        try {
            config.validate();
            logger.info("Default batch config validation passed");
        } catch (Exception e) {
            logger.error("Batch config validation failed", e);
            throw e;
        }
    }

    public void testMagicianBlockchainScanWithBatch() {
        try {
            // 初始化线程池
            EventThreadPool.init(1);

            // 创建测试监控事件
            TestEthMonitorEvent testEvent = new TestEthMonitorEvent();

            // 创建批量扫描配置
            MagicianBlockchainScan scan = MagicianBlockchainScan.create()
                .setRpcUrl(
                    EthRpcInit.create()
                        .addRpcUrl("https://rpc.ankr.com/eth") // 使用免费的RPC节点进行测试
                )
                .setScanPeriod(10000) // 10秒扫描周期，避免频繁请求
                .setBeginBlockNumber(BigInteger.valueOf(18000000))
                .setEndBlockNumber(BigInteger.valueOf(18000005)) // 只扫描5个区块进行测试
                .enableBatchProcessing(3, 2) // 批量大小3，并发数2
                .addEthMonitorEvent(testEvent);

            logger.info("Batch scanning configuration created successfully");

            // 注意：这里不实际启动扫描，只测试配置创建
             scan.start(); // 在实际测试中可以启动

        } catch (Exception e) {
            logger.error("Failed to create batch scanning configuration", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试用的以太坊监控事件
     */
    public static class TestEthMonitorEvent implements EthMonitorEvent {

        private static final Logger logger = LoggerFactory.getLogger(TestEthMonitorEvent.class);

        @Override
        public EthMonitorFilter ethMonitorFilter() {
            // 简单的过滤器，监控大额转账
            return EthMonitorFilter.builder()
                .setMinValue(BigInteger.valueOf(1000000000000000000L)); // 最小1 ETH
        }

        @Override
        public void call(TransactionModel transactionModel) {
            try {
                String txHash = transactionModel.getEthTransactionModel().getTransactionObject().getHash();
                BigInteger blockNumber = transactionModel.getEthTransactionModel().getTransactionObject().getBlockNumber();
                BigInteger value = transactionModel.getEthTransactionModel().getTransactionObject().getValue();

                logger.info("Test event triggered - Block: {}, Hash: {}, Value: {}",
                           blockNumber, txHash, value);

            } catch (Exception e) {
                logger.error("Error in test monitor event", e);
            }
        }
    }
}
