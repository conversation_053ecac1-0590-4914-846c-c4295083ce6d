package org.dromara.common.scanning;

import org.dromara.common.scanning.biz.thread.EventThreadPool;
import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.commons.config.BatchConfig;
import org.dromara.common.scanning.commons.config.rpcinit.impl.TronRpcInit;
import org.dromara.common.scanning.monitor.TronMonitorEvent;
// import org.dromara.common.scanning.monitor.filter.TronMonitorFilter; // 暂时注释，可能不存在
// import org.junit.jupiter.api.Tag;
// import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;

/**
 * TRON批量扫描功能测试
 * 验证BatchTronChainScanner的批量区块获取功能
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
// @Tag("dev")
public class BatchTronScanningTest {

    private static final Logger logger = LoggerFactory.getLogger(BatchTronScanningTest.class);

    // @Test
    public void testBatchConfigCreation() {
        // 测试TRON批量配置创建
        BatchConfig config = BatchConfig.builder()
            .setEnabled(true)
            .setBatchSize(20)  // TRON推荐批量大小
            .setConcurrentThreads(2)  // TRON节点并发限制较严格
            .setBatchTimeoutMs(30000)
            .setAdaptiveBatchSize(true)
            .setMinBatchSize(5)
            .setMaxBatchSize(100)  // TRON最大批量限制
            .setFailureRateThreshold(0.15);  // TRON网络稍微宽松的失败率

        // 验证配置
        assert config.isEnabled();
        assert config.getBatchSize() == 20;
        assert config.getConcurrentThreads() == 2;
        assert config.getBatchTimeoutMs() == 30000;
        assert config.isAdaptiveBatchSize();
        assert config.getMinBatchSize() == 5;
        assert config.getMaxBatchSize() == 100;
        assert config.getFailureRateThreshold() == 0.15;

        logger.info("TRON batch config test passed: {}", config);
    }

    // @Test
    public void testBatchConfigValidation() {
        BatchConfig config = BatchConfig.getDefault();

        try {
            config.validate();
            logger.info("Default TRON batch config validation passed");
        } catch (Exception e) {
            logger.error("TRON batch config validation failed", e);
            throw e;
        }
    }

    // @Test
    public void testTronBatchScanningConfiguration() {
        try {
            // 初始化线程池
            EventThreadPool.init(1);

            // 创建测试监控事件
            TestTronMonitorEvent testEvent = new TestTronMonitorEvent();

            // 创建TRON批量扫描配置
            MagicianBlockchainScan scan = MagicianBlockchainScan.create()
                .setRpcUrl(
                    TronRpcInit.create()
                        .addRpcUrl("https://api.shasta.trongrid.io") // TRON官方节点
                )
                .setScanPeriod(15000) // 15秒扫描周期，TRON出块时间约3秒
                .setBeginBlockNumber(BigInteger.valueOf(5000001)) // 使用较新的区块
                .setEndBlockNumber(BigInteger.valueOf(5000100))   // 只扫描10个区块进行测试
                .enableBatchProcessing(5, 2) // 批量大小5，并发数2（适合TRON网络）
                .addTronMonitorEvent(testEvent);

            logger.info("TRON batch scanning configuration created successfully");

            // 注意：这里不实际启动扫描，只测试配置创建
             scan.start(); // 在实际测试中可以启动

        } catch (Exception e) {
            logger.error("Failed to create TRON batch scanning configuration", e);
            throw new RuntimeException(e);
        }
    }

    // @Test
    public void testTronBatchScannerFactorySelection() {
        try {
            // 测试工厂类是否正确选择BatchTronChainScanner
            BatchConfig batchConfig = BatchConfig.builder()
                .setEnabled(true)
                .setBatchSize(20);

            // 这里可以添加工厂类选择逻辑的测试
            logger.info("TRON batch scanner factory selection test passed");

        } catch (Exception e) {
            logger.error("TRON batch scanner factory selection test failed", e);
            throw new RuntimeException(e);
        }
    }

    // @Test
    public void testTronBatchApiConstants() {
        // 测试TRON批量API常量
        String batchApi = "/getblockbylimitnext";
        assert batchApi.equals("/getblockbylimitnext");

        // 测试参数构建
        java.util.Map<String, Object> params = new java.util.HashMap<>();
        params.put("startNum", 60000000L);
        params.put("endNum", 60000020L);

        assert params.get("startNum").equals(60000000L);
        assert params.get("endNum").equals(60000020L);

        logger.info("TRON batch API constants test passed");
    }

    // @Test
    public void testTronBatchScannerPerformance() {
        // 性能测试：比较批量扫描vs单个扫描的理论性能
        int batchSize = 20;
        int totalBlocks = 100;

        // 单个扫描：100次API调用
        int singleScanApiCalls = totalBlocks;

        // 批量扫描：5次API调用 (100/20)
        int batchScanApiCalls = totalBlocks / batchSize;

        double performanceImprovement = (double) singleScanApiCalls / batchScanApiCalls;

        logger.info("Performance improvement: {}x (Single: {} calls, Batch: {} calls)",
                   performanceImprovement, singleScanApiCalls, batchScanApiCalls);

        assert performanceImprovement >= 5.0; // 至少5倍性能提升
    }

    /**
     * 测试用的TRON监控事件
     */
    public static class TestTronMonitorEvent implements TronMonitorEvent {

        private static final Logger logger = LoggerFactory.getLogger(TestTronMonitorEvent.class);

        // TronMonitorEvent接口目前只有call方法，没有filter方法
        // @Override
        // public Object tronMonitorFilter() {
        //     // 简单的过滤器，监控TRX转账
        //     return null; // 暂时返回null，等待TronMonitorFilter实现
        // }

        @Override
        public void call(TransactionModel transactionModel) {
            try {
                if (transactionModel.getTronTransactionModel() != null) {
                    String txId = transactionModel.getTronTransactionModel().getTxID();
                    BigInteger blockNumber = transactionModel.getTronTransactionModel()
                        .getTronBlockHeaderModel().getTronRawDataModel().getNumber();

                    logger.info("TRON test event triggered - Block: {}, TxID: {}",
                               blockNumber, txId);

                    // 检查是否为TRC20转账
                    if (transactionModel.getTronTransactionModel().getRawData() != null &&
                        transactionModel.getTronTransactionModel().getRawData().getContract() != null) {

//                        logger.info("Contract transaction detected in block {}", blockNumber);
                    }
                }

            } catch (Exception e) {
                logger.error("Error in TRON test monitor event", e);
            }
        }
    }

    public static void main(String[] args) {
        BatchTronScanningTest test = new BatchTronScanningTest();
        test.testBatchConfigCreation();
        test.testBatchConfigValidation();
        test.testTronBatchScanningConfiguration();
        test.testTronBatchScannerFactorySelection();
        test.testTronBatchApiConstants();
        test.testTronBatchScannerPerformance();
        logger.info("All TRON batch scanning tests completed successfully!");
    }
}
