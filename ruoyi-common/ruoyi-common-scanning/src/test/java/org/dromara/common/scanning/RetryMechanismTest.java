package org.dromara.common.scanning;

import org.dromara.common.scanning.biz.thread.EventThreadPool;
import org.dromara.common.scanning.biz.thread.RetryStrategyQueue;
import org.dromara.common.scanning.chain.RetryStrategy;
import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.commons.config.rpcinit.impl.TronRpcInit;
import org.dromara.common.scanning.monitor.TronMonitorEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 重试机制功能测试
 * 用于验证和跟踪重试机制的修复情况
 */
public class RetryMechanismTest {

    private static final Logger logger = LoggerFactory.getLogger(RetryMechanismTest.class);

    public static void main(String[] args) {
        MagicianBlockchainScan scan = null;
        try {
            RetryMechanismTest test = new RetryMechanismTest();

            // 测试重试策略实现
            test.testRetryStrategyImplementation();

            // 测试重试队列功能
            test.testRetryQueueFunctionality();

            // 测试重试机制集成
            test.testRetryMechanismIntegration();

            logger.info("All retry mechanism tests completed successfully!");

        } catch (Exception e) {
            logger.error("重试机制测试过程中发生异常", e);
        } finally {
            gracefulShutdown(scan);
        }
    }

    /**
     * 测试重试策略实现
     */
    public void testRetryStrategyImplementation() {
        logger.info("=== 测试重试策略实现 ===");

        try {
            // 创建测试用的重试策略
            TestRetryStrategy retryStrategy = new TestRetryStrategy();

            // 测试重试逻辑
            BigInteger testBlock = BigInteger.valueOf(1000);

            logger.info("开始测试重试计数逻辑...");
            retryStrategy.retry(testBlock);
            logger.info("第1次重试后，当前计数: {}", retryStrategy.getRetryCount(testBlock));

            retryStrategy.retry(testBlock); // 第二次重试
            logger.info("第2次重试后，当前计数: {}", retryStrategy.getRetryCount(testBlock));

            retryStrategy.retry(testBlock); // 第三次重试
            logger.info("第3次重试后，当前计数: {}", retryStrategy.getRetryCount(testBlock));

            retryStrategy.retry(testBlock); // 第四次重试（应该超过限制）
            logger.info("第4次重试后，当前计数: {}", retryStrategy.getRetryCount(testBlock));

            // 验证重试机制：如果区块还在重试队列中，说明未达到最大次数或未成功
            // 如果区块不在重试队列中，说明要么成功了，要么超过了最大重试次数
            logger.info("总重试次数: {}", retryStrategy.getTotalRetries());
            logger.info("当前区块 {} 的重试状态: {}", testBlock,
                       retryStrategy.getRetryCount(testBlock) > 0 ? "仍在重试中" : "已完成或已放弃");

            logger.info("重试策略实现测试通过");

        } catch (Exception e) {
            logger.error("重试策略实现测试失败", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试重试队列功能
     */
    public void testRetryQueueFunctionality() {
        logger.info("=== 测试重试队列功能 ===");

        try {
            RetryStrategyQueue queue = new RetryStrategyQueue();

            // 添加测试区块
            BigInteger block1 = BigInteger.valueOf(1001);
            BigInteger block2 = BigInteger.valueOf(1002);
            BigInteger block3 = BigInteger.valueOf(1003);

            queue.add(block1);
            queue.add(block2);
            queue.add(block3);

            // 验证队列大小
            assert queue.getLinkedBlockingQueue().size() == 3;

            // 验证队列顺序（FIFO）
            BigInteger polled1 = queue.getLinkedBlockingQueue().poll();
            BigInteger polled2 = queue.getLinkedBlockingQueue().poll();
            BigInteger polled3 = queue.getLinkedBlockingQueue().poll();

            assert block1.equals(polled1);
            assert block2.equals(polled2);
            assert block3.equals(polled3);

            logger.info("重试队列功能测试通过");

        } catch (Exception e) {
            logger.error("重试队列功能测试失败", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试重试机制集成
     */
    public void testRetryMechanismIntegration() {
        logger.info("=== 测试重试机制集成 ===");

        try {
            // 初始化线程池
            EventThreadPool.init(1);

            // 创建测试监控事件
            TestTronRetryMonitorEvent testEvent = new TestTronRetryMonitorEvent();

            // 创建带重试策略的扫描配置
            MagicianBlockchainScan scan = MagicianBlockchainScan.create()
                .setRpcUrl(
                    TronRpcInit.create()
                        .addRpcUrl("https://api.shasta.trongrid.io") // TRON测试网
                )
                .setScanPeriod(10000)
                .setBeginBlockNumber(BigInteger.valueOf(5000001))
                .setEndBlockNumber(BigInteger.valueOf(5000010))
                .setRetryStrategy(new TestRetryStrategy()) // 设置测试重试策略
                .addTronMonitorEvent(testEvent);

            logger.info("重试机制集成配置创建成功");

            // 注意：这里不实际启动扫描，只测试配置创建
            // 如果要实际测试，可以取消注释下面的代码
            /*
            logger.info("开始重试机制集成测试...");
            scan.start();

            // 运行20秒后停止
            Thread.sleep(20000);
            scan.shutdown();
            logger.info("重试机制集成测试完成");
            */

        } catch (Exception e) {
            logger.error("重试机制集成测试失败", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试用的重试策略实现
     */
    public static class TestRetryStrategy implements RetryStrategy {

        private static final Logger logger = LoggerFactory.getLogger(TestRetryStrategy.class);
        private final ConcurrentHashMap<BigInteger, Integer> retryCount = new ConcurrentHashMap<>();
        private final int maxRetries = 3;
        private final AtomicInteger totalRetries = new AtomicInteger(0);

        @Override
        public void retry(BigInteger blockNumber) {
            int count = retryCount.getOrDefault(blockNumber, 0) + 1;
            totalRetries.incrementAndGet();

            if (count > maxRetries) {
                logger.warn("区块 {} 超过最大重试次数 {}，放弃重试", blockNumber, maxRetries);
                retryCount.remove(blockNumber);
                return;
            }

            retryCount.put(blockNumber, count);
            logger.info("重试区块 {} - 第 {} 次尝试", blockNumber, count);

            // 模拟重试逻辑（实际应该调用扫描器重新扫描）
            simulateRetryLogic(blockNumber, count);
        }

        private void simulateRetryLogic(BigInteger blockNumber, int retryCount) {
            // 模拟重试延迟（指数退避）
            try {
                long delay = (long) (1000 * Math.pow(2, retryCount - 1)); // 1s, 2s, 4s
                logger.info("重试延迟 {} ms", delay);
                Thread.sleep(Math.min(delay, 5000)); // 最大延迟5秒
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            // 模拟重试执行结果
            boolean success = Math.random() > 0.5; // 50%成功率
            if (success) {
                logger.info("区块 {} 第 {} 次重试执行成功，区块处理完成", blockNumber, retryCount);
                // 只有在区块真正处理完成时才清除计数器
                this.retryCount.remove(blockNumber);
            } else {
                logger.warn("区块 {} 第 {} 次重试执行失败，等待下次重试", blockNumber, retryCount);
                // 重试失败时不清除计数器，保持当前计数以便下次重试时继续累加
            }
        }

        public int getRetryCount(BigInteger blockNumber) {
            return retryCount.getOrDefault(blockNumber, 0);
        }

        public int getTotalRetries() {
            return totalRetries.get();
        }
    }

    /**
     * 测试用的TRON监控事件
     */
    public static class TestTronRetryMonitorEvent implements TronMonitorEvent {

        private static final Logger logger = LoggerFactory.getLogger(TestTronRetryMonitorEvent.class);
        private final AtomicInteger processedTransactions = new AtomicInteger(0);

        @Override
        public void call(TransactionModel transactionModel) {
            try {
                int count = processedTransactions.incrementAndGet();

                if (transactionModel.getTronTransactionModel() != null) {
                    String txId = transactionModel.getTronTransactionModel().getTxID();
                    logger.info("处理TRON交易 #{} - TxID: {}", count, txId);

                    // 模拟处理异常（用于触发重试）
                    if (count % 5 == 0) { // 每5个交易模拟一次异常
                        throw new RuntimeException("模拟处理异常，触发重试机制");
                    }
                }

            } catch (Exception e) {
                logger.error("TRON监控事件处理异常", e);
                throw e; // 重新抛出异常以触发重试
            }
        }

        public int getProcessedCount() {
            return processedTransactions.get();
        }
    }

    /**
     * 优雅关闭扫描服务
     */
    public static void gracefulShutdown(MagicianBlockchainScan scan) {
        try {
            logger.info("=== 开始优雅关闭重试机制测试 ===");

            if (scan != null) {
                scan.shutdown();
                logger.info("扫描服务已关闭");
            }

            Thread.sleep(2000);
            MagicianBlockchainScan.shutdownAll();
            logger.info("所有扫描任务和线程池已关闭");

            Thread.sleep(1000);
            System.gc();

            logger.info("=== 重试机制测试优雅关闭完成 ===");

        } catch (Exception e) {
            logger.error("优雅关闭过程中发生异常", e);
            System.exit(0);
        }
    }
}
