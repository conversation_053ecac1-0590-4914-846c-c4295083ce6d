package org.dromara.common.scanning;

import org.dromara.common.scanning.biz.thread.EventThreadPool;
import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.commons.config.BatchConfig;
import org.dromara.common.scanning.commons.config.rpcinit.impl.EthRpcInit;
import org.dromara.common.scanning.monitor.EthMonitorEvent;
import org.dromara.common.scanning.monitor.filter.EthMonitorFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.web3j.protocol.core.methods.response.Log;

import java.math.BigInteger;

/**
 * ETH日志扫描功能测试
 * 测试新集成的EthLogScanner功能
 */
public class EthLogScanningTest {

    private static final Logger logger = LoggerFactory.getLogger(EthLogScanningTest.class);

    public static void main(String[] args) {
        MagicianBlockchainScan scan = null;
        try {
            EthLogScanningTest test = new EthLogScanningTest();
            test.testLogScanConfigCreation();

            // 如果要运行实际测试，取消注释下面的代码
             runRealTest();

            test.testMagicianBlockchainScanWithLogScanning();
            logger.info("All ETH log scanning tests completed successfully!");

        } catch (Exception e) {
            logger.error("测试过程中发生异常", e);
        } finally {
            // 确保资源被清理
            gracefulShutdown(scan);
        }
    }

    /**
     * 测试日志扫描配置创建
     */
    public void testLogScanConfigCreation() {
        // 测试日志扫描配置创建
        BatchConfig config = BatchConfig.builder()
            .setLogScanEnabled(true)  // 启用日志扫描
            .setBatchSize(100)        // 日志扫描可以使用更大的批量
            .setConcurrentThreads(3)
            .setBatchTimeoutMs(30000);

        // 验证配置
        assert config.isLogScanEnabled();
        assert config.getBatchSize() == 100;
        assert config.getConcurrentThreads() == 3;

        logger.info("Log scan config test passed: {}", config);
    }

    /**
     * 测试使用日志扫描模式的MagicianBlockchainScan
     */
    public void testMagicianBlockchainScanWithLogScanning() {
        try {
            // 初始化线程池
            EventThreadPool.init(1);

            // 创建测试监控事件
            TestEthLogMonitorEvent testEvent = new TestEthLogMonitorEvent();

            // 创建日志扫描配置
            MagicianBlockchainScan scan = MagicianBlockchainScan.create()
                .setRpcUrl(
                    EthRpcInit.create()
                        .addRpcUrl("https://capable-sleek-sponge.bsc-testnet.quiknode.pro/e0946ba9546e5a1119b2b904ed407c0e7447bc5b/") // 使用免费的RPC节点进行测试
                )
                .setScanPeriod(10000) // 15秒扫描周期
                .setBeginBlockNumber(BigInteger.valueOf(19000101)) // 使用较新的区块
                .setEndBlockNumber(BigInteger.valueOf(19000200))   // 只扫描10个区块进行测试
                .enableLogScanning(10, 1) // 启用日志扫描：批量大小50，并发数2
                .addEthMonitorEvent(testEvent);

            logger.info("ETH log scanning configuration created successfully");
            logger.info("Configuration: Log scanning enabled, batch size: 50, concurrent threads: 2");

            // 注意：这里不实际启动扫描，只测试配置创建
            // 如果要实际测试，可以取消注释下面的代码
            /*
            logger.info("Starting ETH log scanning test...");
            scan.start();

            // 运行30秒后停止
            Thread.sleep(30000);
            scan.shutdown();
            logger.info("ETH log scanning test completed");
            */

        } catch (Exception e) {
            logger.error("Failed to create ETH log scanning configuration", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试用的以太坊日志监控事件
     * 专门用于处理Log对象
     */
    public static class TestEthLogMonitorEvent implements EthMonitorEvent {

        private static final Logger logger = LoggerFactory.getLogger(TestEthLogMonitorEvent.class);

        @Override
        public EthMonitorFilter ethMonitorFilter() {
            // 简单的过滤器，可以根据需要设置
            return EthMonitorFilter.builder()
                .setMinValue(BigInteger.ZERO); // 接受所有交易
        }

        @Override
        public void call(TransactionModel transactionModel) {
            try {
                // 检查是否是日志扫描的数据
                if (transactionModel.getEthTransactionModel().getLog() != null) {
                    // 处理Log对象
                    Log log = transactionModel.getEthTransactionModel().getLog();

//                    logger.info("ETH Log detected - Block: {}, TxHash: {}, Contract: {}, Topics: {}",
//                               log.getBlockNumber(),
//                               log.getTransactionHash(),
//                               log.getAddress(),
//                               log.getTopics().size());

                    // 如果是Transfer事件（ERC20代币转账）
                    if (isTransferEvent(log)) {
                        logger.info("Transfer event detected: {}", log.getTransactionHash());
                    }

                } else if (transactionModel.getEthTransactionModel().getTransactionObject() != null) {
                    // 处理传统的Transaction对象（区块扫描模式）
                    String txHash = transactionModel.getEthTransactionModel().getTransactionObject().getHash();
                    BigInteger blockNumber = transactionModel.getEthTransactionModel().getTransactionObject().getBlockNumber();

                    logger.info("ETH Transaction detected - Block: {}, Hash: {}",
                        blockNumber, txHash);
                }

            } catch (Exception e) {
                logger.error("Error in ETH log monitor event", e);
            }
        }

        /**
         * 检查是否是Transfer事件
         */
        private boolean isTransferEvent(Log log) {
            if (log.getTopics() == null || log.getTopics().isEmpty()) {
                return false;
            }

            // Transfer事件的签名：Transfer(address,address,uint256)
            String transferEventSignature = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef";
            return transferEventSignature.equals(log.getTopics().get(0));
        }
    }

    /**
     * 运行实际测试的方法（需要真实的RPC连接）
     */
    public static void runRealTest() {
        try {
            logger.info("=== 开始ETH日志扫描实际测试 ===");

            EventThreadPool.init(2);

            MagicianBlockchainScan scan = MagicianBlockchainScan.create()
                .setRpcUrl(
                    EthRpcInit.create()
                        .addRpcUrl("https://capable-sleek-sponge.bsc-testnet.quiknode.pro/e0946ba9546e5a1119b2b904ed407c0e7447bc5b/")
//                        .addRpcUrl("https://cloudflare-eth.com")
                )
                .setScanPeriod(10000)
                .setBeginBlockNumber(BigInteger.valueOf(19000001))
                .setEndBlockNumber(BigInteger.valueOf(19000100))
                .enableLogScanning(10, 1)
                .addEthMonitorEvent(new TestEthLogMonitorEvent());

            scan.start();

            // 运行20秒
            Thread.sleep(20000);

            logger.info("=== 开始关闭ETH日志扫描测试 ===");

            // 1. 关闭扫描服务
            scan.shutdown();

            // 2. 等待扫描服务完全关闭
            Thread.sleep(2000);

            // 3. 关闭事件线程池
            EventThreadPool.shutdown();

            // 4. 等待所有资源清理完成
            Thread.sleep(1000);

            logger.info("=== ETH日志扫描实际测试完成 ===");

        } catch (Exception e) {
            logger.error("ETH日志扫描实际测试失败", e);
        }
    }

    /**
     * 优雅关闭扫描服务的工具方法
     * 确保所有资源都被正确清理，程序能够正常退出
     */
    public static void gracefulShutdown(MagicianBlockchainScan scan) {
        try {
            logger.info("=== 开始优雅关闭扫描服务 ===");

            // 1. 关闭扫描服务
            if (scan != null) {
                scan.shutdown();
                logger.info("扫描服务已关闭");
            }

            // 2. 等待扫描服务完全关闭
            Thread.sleep(3000);

            // 3. 关闭所有扫描任务和线程池
            MagicianBlockchainScan.shutdownAll();
            logger.info("所有扫描任务和线程池已关闭");

            // 4. 等待所有资源清理完成
            Thread.sleep(2000);

            // 5. 强制垃圾回收，帮助清理资源
            System.gc();

            logger.info("=== 优雅关闭完成，程序应该能够正常退出 ===");

        } catch (Exception e) {
            logger.error("优雅关闭过程中发生异常", e);
            // 如果优雅关闭失败，强制退出
            logger.warn("优雅关闭失败，将强制退出程序");
            System.exit(0);
        }
    }
}
