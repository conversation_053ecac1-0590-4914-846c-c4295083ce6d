package org.dromara.common.scanning.commons.config.rpcinit.impl;

import org.dromara.common.scanning.commons.config.rpcinit.RpcInit;

/**
 * TODO In development.......
 */
public class SolRpcInit extends RpcInit {

    public static SolRpcInit create() {
        return new SolRpcInit();
    }


    /**
     * set node url
     */
    public SolRpcInit addRpcUrl(String rpcUrl) throws Exception {
        blockChainConfig.addSolRpcUrls(rpcUrl);
        return this;
    }
}
