package org.dromara.common.scanning.chain.model.tron;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.scanning.utils.TronAddressUtils;

import java.util.List;

/**
 * TRON事件日志模型
 * 用于表示TRON交易中的事件日志信息
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@Slf4j
@JsonIgnoreProperties(ignoreUnknown = true)
public class TronLogModel {

    /**
     * 合约地址（触发事件的合约）
     */
    @JsonProperty("address")
    private String address;

    /**
     * 事件主题列表
     * topics[0] = 事件签名哈希
     * topics[1] = 第一个indexed参数
     * topics[2] = 第二个indexed参数
     * ...
     */
    @JsonProperty("topics")
    private List<String> topics;

    /**
     * 事件数据（非indexed参数的编码数据）
     */
    @JsonProperty("data")
    private String data;

    /**
     * 检查是否为Transfer事件
     * Transfer事件的签名：Transfer(address,address,uint256)
     * 对应的哈希：0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef
     */
    public boolean isTransferEvent() {
        return topics != null &&
               !topics.isEmpty() &&
               "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef".equalsIgnoreCase(topics.get(0));
    }

    /**
     * 获取Transfer事件的from地址
     * 仅在isTransferEvent()为true时有效
     * 返回TRON地址格式（T开头的Base58编码）
     */
    public String getTransferFrom() {
        if (!isTransferEvent() || topics.size() < 2) {
            return null;
        }
        // topics[1]是from地址，需要去掉前面的0x和前导0
        String topic = topics.get(1);
        if (topic.startsWith("0x")) {
            topic = topic.substring(2);
        }
        // 取后40位字符作为地址
        if (topic.length() >= 40) {
            String hexAddress = topic.substring(topic.length() - 40);
            try {
                // 转换为TRON地址格式
                return TronAddressUtils.hexToAddress(hexAddress);
            } catch (Exception e) {
                log.warn("转换TRON地址失败，返回十六进制格式: hexAddress={}, error={}", hexAddress, e.getMessage());
                // 如果转换失败，返回十六进制格式（兼容性处理）
                return "0x" + hexAddress;
            }
        }
        return null;
    }

    /**
     * 获取Transfer事件的to地址
     * 仅在isTransferEvent()为true时有效
     * 返回TRON地址格式（T开头的Base58编码）
     */
    public String getTransferTo() {
        if (!isTransferEvent() || topics.size() < 3) {
            return null;
        }
        // topics[2]是to地址，需要去掉前面的0x和前导0
        String topic = topics.get(2);
        if (topic.startsWith("0x")) {
            topic = topic.substring(2);
        }
        // 取后40位字符作为地址
        if (topic.length() >= 40) {
            String hexAddress = topic.substring(topic.length() - 40);
            try {
                // 转换为TRON地址格式
                return TronAddressUtils.hexToAddress(hexAddress);
            } catch (Exception e) {
                log.warn("转换TRON地址失败，返回十六进制格式: hexAddress={}, error={}", hexAddress, e.getMessage());
                // 如果转换失败，返回十六进制格式（兼容性处理）
                return "0x" + hexAddress;
            }
        }
        return null;
    }

    /**
     * 获取Transfer事件的金额
     * 仅在isTransferEvent()为true时有效
     * 注意：返回的是原始金额，需要根据代币精度进行转换
     */
    public String getTransferAmount() {
        if (!isTransferEvent() || data == null || data.isEmpty()) {
            return "0";
        }

        String hexData = data;
        if (hexData.startsWith("0x")) {
            hexData = hexData.substring(2);
        }

        // data字段包含amount（uint256），直接解析
        if (hexData.length() >= 64) {
            // 取前64位字符作为amount
            String amountHex = hexData.substring(0, 64);
            try {
                // 转换为十进制字符串
                return new java.math.BigInteger(amountHex, 16).toString();
            } catch (NumberFormatException e) {
                return "0";
            }
        }

        return "0";
    }

    /**
     * 获取事件签名哈希
     */
    public String getEventSignature() {
        return topics != null && !topics.isEmpty() ? topics.get(0) : null;
    }

    /**
     * 检查是否包含指定地址（作为from或to）
     */
    public boolean containsAddress(String address) {
        if (address == null || !isTransferEvent()) {
            return false;
        }

        String normalizedAddress = address.toLowerCase();
        String from = getTransferFrom();
        String to = getTransferTo();

        return (from != null && from.toLowerCase().equals(normalizedAddress)) ||
               (to != null && to.toLowerCase().equals(normalizedAddress));
    }

    /**
     * 获取事件的字符串表示（用于日志输出）
     */
    @Override
    public String toString() {
        if (isTransferEvent()) {
            return String.format("Transfer(from=%s, to=%s, amount=%s, contract=%s)",
                getTransferFrom(), getTransferTo(), getTransferAmount(), address);
        } else {
            return String.format("Event(signature=%s, contract=%s, topics=%d)",
                getEventSignature(), address, topics != null ? topics.size() : 0);
        }
    }

    // ============ 兼容性方法 ============

    /**
     * 获取交易哈希（需要从外部设置）
     */
    private String transactionHash;

    /**
     * 区块高度（需要从外部设置）
     */
    private java.math.BigInteger blockNumber;

    /**
     * 交易手续费（需要从外部设置）
     */
    private java.math.BigDecimal transactionFee;

    /**
     * 区块时间戳（需要从外部设置）
     * 单位：毫秒
     */
    private Long blockTimeStamp;

    /**
     * 设置交易哈希
     */
    public void setTransactionHash(String transactionHash) {
        this.transactionHash = transactionHash;
    }

    /**
     * 获取交易哈希
     */
    public String getTransactionHash() {
        return transactionHash;
    }

    /**
     * 获取from地址（兼容方法）
     */
    public String getFromAddress() {
        return getTransferFrom();
    }

    /**
     * 获取to地址（兼容方法）
     */
    public String getToAddress() {
        return getTransferTo();
    }

    /**
     * 获取金额（兼容方法）
     */
    public String getAmount() {
        return getTransferAmount();
    }

    /**
     * 设置区块高度
     */
    public void setBlockNumber(java.math.BigInteger blockNumber) {
        this.blockNumber = blockNumber;
    }

    /**
     * 获取区块高度
     */
    public java.math.BigInteger getBlockNumber() {
        return blockNumber;
    }

    /**
     * 设置交易手续费
     */
    public void setTransactionFee(java.math.BigDecimal transactionFee) {
        this.transactionFee = transactionFee;
    }

    /**
     * 获取交易手续费
     */
    public java.math.BigDecimal getTransactionFee() {
        return transactionFee;
    }

    /**
     * 设置区块时间戳
     */
    public void setBlockTimeStamp(Long blockTimeStamp) {
        this.blockTimeStamp = blockTimeStamp;
    }

    /**
     * 获取区块时间戳
     */
    public Long getBlockTimeStamp() {
        return blockTimeStamp;
    }
}
