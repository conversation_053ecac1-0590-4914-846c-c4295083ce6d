package org.dromara.common.scanning.chain.factory;

import org.dromara.common.scanning.chain.ChainScanner;
import org.dromara.common.scanning.chain.impl.BatchTronChainScanner;
import org.dromara.common.scanning.chain.impl.ETHChainScanner;
import org.dromara.common.scanning.chain.impl.EthLogScanner;
import org.dromara.common.scanning.chain.impl.SolChainScanner;
import org.dromara.common.scanning.chain.impl.TronChainScanner;
import org.dromara.common.scanning.commons.config.BlockChainConfig;
import org.dromara.common.scanning.commons.enums.ChainType;

/**
 * Factory class, get scanner
 */
public class ChainScannerFactory {

    /**
     * get scanner
     * @param chainType
     * @return
     */
    public static ChainScanner getChainScanner(ChainType chainType) {
        switch (chainType) {
            case ETH:
                return new ETHChainScanner();
            case SOL:
                return new SolChainScanner();
            case TRON:
                return new TronChainScanner();
        }

        return null;
    }

    /**
     * get scanner with batch configuration support
     * @param chainType
     * @param blockChainConfig
     * @return
     */
    public static ChainScanner getChainScanner(ChainType chainType, BlockChainConfig blockChainConfig) {
        switch (chainType) {
            case ETH:
                // 检查是否启用日志扫描模式
                if (blockChainConfig.getBatchConfig() != null && blockChainConfig.getBatchConfig().isLogScanEnabled()) {
                    return new EthLogScanner();
                }
                 else {
                    return new ETHChainScanner();
                }
            case SOL:
                return new SolChainScanner();
            case TRON:
                // 如果启用了批量处理，返回批量扫描器
                if (blockChainConfig.getBatchConfig() != null && blockChainConfig.getBatchConfig().isEnabled()) {
                    return new BatchTronChainScanner();
                } else {
                    return new TronChainScanner();
                }
        }

        return null;
    }
}
