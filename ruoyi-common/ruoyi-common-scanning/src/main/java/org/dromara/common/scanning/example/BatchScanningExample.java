package org.dromara.common.scanning.example;

import org.dromara.common.scanning.MagicianBlockchainScan;
import org.dromara.common.scanning.biz.thread.EventThreadPool;
import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.commons.config.BatchConfig;
import org.dromara.common.scanning.commons.config.rpcinit.impl.EthRpcInit;
import org.dromara.common.scanning.monitor.EthMonitorEvent;
import org.dromara.common.scanning.monitor.filter.EthMonitorFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;

/**
 * 批量扫描使用示例
 */
public class BatchScanningExample {

    private static final Logger logger = LoggerFactory.getLogger(BatchScanningExample.class);

    public static void main(String[] args) {
        try {
            // 示例1：使用默认批量配置
            startBatchScanWithDefaults();

            // 示例2：使用自定义批量配置
            // startBatchScanWithCustomConfig();

            // 示例3：使用高性能批量配置
            // startHighPerformanceBatchScan();

        } catch (Exception e) {
            logger.error("Failed to start batch scanning", e);
        }
    }

    /**
     * 示例1：使用默认批量配置启动扫描
     */
    public static void startBatchScanWithDefaults() throws Exception {
        logger.info("Starting batch scan with default configuration...");

        // 初始化线程池
        EventThreadPool.init(2);

        // 创建批量扫描任务
        MagicianBlockchainScan.create()
            .setRpcUrl(
                EthRpcInit.create()
                    .addRpcUrl("https://mainnet.infura.io/v3/YOUR_PROJECT_ID")
                    .addRpcUrl("https://eth-mainnet.alchemyapi.io/v2/YOUR_API_KEY")
                    .addRpcUrl("https://rpc.ankr.com/eth")
            )
            .setScanPeriod(3000)  // 3秒扫描周期
            .setBeginBlockNumber(BigInteger.valueOf(18000000))
            .enableBatchProcessing()  // 启用默认批量处理
            .addEthMonitorEvent(new BatchExampleMonitorEvent())
            .start();

        logger.info("Batch scanning started with default configuration");
    }

    /**
     * 示例2：使用自定义批量配置启动扫描
     */
    public static void startBatchScanWithCustomConfig() throws Exception {
        logger.info("Starting batch scan with custom configuration...");

        // 初始化线程池
        EventThreadPool.init(2);

        // 创建自定义批量配置
        BatchConfig batchConfig = BatchConfig.builder()
            .setEnabled(true)
            .setBatchSize(30)           // 每批处理30个区块
            .setConcurrentThreads(8)    // 8个并发线程
            .setBatchTimeoutMs(45000)   // 45秒超时
            .setAdaptiveBatchSize(true) // 启用自适应批量大小
            .setMinBatchSize(10)        // 最小批量10个区块
            .setMaxBatchSize(50);       // 最大批量50个区块

        // 创建批量扫描任务
        MagicianBlockchainScan.create()
            .setRpcUrl(
                EthRpcInit.create()
                    .addRpcUrl("https://mainnet.infura.io/v3/YOUR_PROJECT_ID")
                    .addRpcUrl("https://eth-mainnet.alchemyapi.io/v2/YOUR_API_KEY")
            )
            .setScanPeriod(2000)  // 2秒扫描周期
            .setBeginBlockNumber(BigInteger.valueOf(18000000))
            .setBatchConfig(batchConfig)  // 设置自定义批量配置
            .addEthMonitorEvent(new BatchExampleMonitorEvent())
            .start();

        logger.info("Batch scanning started with custom configuration: {}", batchConfig);
    }

    /**
     * 示例3：高性能批量扫描配置
     */
    public static void startHighPerformanceBatchScan() throws Exception {
        logger.info("Starting high-performance batch scan...");

        // 初始化更大的线程池
        EventThreadPool.init(4);

        // 创建高性能批量配置
        BatchConfig highPerfConfig = BatchConfig.builder()
            .setEnabled(true)
            .setBatchSize(50)           // 大批量处理
            .setConcurrentThreads(12)   // 高并发
            .setBatchTimeoutMs(60000)   // 更长超时时间
            .setAdaptiveBatchSize(true)
            .setMinBatchSize(20)
            .setMaxBatchSize(100)
            .setFailureRateThreshold(0.05); // 更严格的失败率阈值

        // 创建高性能扫描任务
        MagicianBlockchainScan.create()
            .setRpcUrl(
                EthRpcInit.create()
                    // 使用更多RPC节点实现更好的负载均衡
                    .addRpcUrl("https://mainnet.infura.io/v3/YOUR_PROJECT_ID")
                    .addRpcUrl("https://eth-mainnet.alchemyapi.io/v2/YOUR_API_KEY")
                    .addRpcUrl("https://rpc.ankr.com/eth")
                    .addRpcUrl("https://eth-rpc.gateway.pokt.network")
                    .addRpcUrl("https://cloudflare-eth.com")
            )
            .setScanPeriod(1000)  // 1秒快速扫描
            .setBeginBlockNumber(BigInteger.valueOf(18000000))
            .setBatchConfig(highPerfConfig)
            .addEthMonitorEvent(new BatchExampleMonitorEvent())
            .start();

        logger.info("High-performance batch scanning started: {}", highPerfConfig);
    }

    /**
     * 批量扫描监控事件示例
     */
    public static class BatchExampleMonitorEvent implements EthMonitorEvent {

        private static final Logger logger = LoggerFactory.getLogger(BatchExampleMonitorEvent.class);

        @Override
        public EthMonitorFilter ethMonitorFilter() {
            // 示例：监控USDT转账交易
            return EthMonitorFilter.builder()
                .setToAddress("******************************************") // USDT合约地址
                .setMinValue(BigInteger.valueOf(1000000000000000000L)); // 最小1 ETH
        }

        @Override
        public void call(TransactionModel transactionModel) {
            try {
                // 处理匹配的交易
                String txHash = transactionModel.getEthTransactionModel().getTransactionObject().getHash();
                String from = transactionModel.getEthTransactionModel().getTransactionObject().getFrom();
                String to = transactionModel.getEthTransactionModel().getTransactionObject().getTo();
                BigInteger value = transactionModel.getEthTransactionModel().getTransactionObject().getValue();
                BigInteger blockNumber = transactionModel.getEthTransactionModel().getTransactionObject().getBlockNumber();

                logger.info("Batch scan found transaction - Block: {}, Hash: {}, From: {}, To: {}, Value: {}",
                           blockNumber, txHash, from, to, value);

                // 这里可以添加具体的业务逻辑
                // 例如：保存到数据库、发送通知、触发其他业务流程等

            } catch (Exception e) {
                logger.error("Error processing transaction in batch scan", e);
            }
        }
    }

    /**
     * 性能对比示例
     */
    public static void performanceComparison() throws Exception {
        logger.info("Starting performance comparison...");

        // 传统单个区块扫描
        long startTime1 = System.currentTimeMillis();

        MagicianBlockchainScan traditionalScan = MagicianBlockchainScan.create()
            .setRpcUrl(EthRpcInit.create().addRpcUrl("https://mainnet.infura.io/v3/YOUR_PROJECT_ID"))
            .setScanPeriod(5000)
            .setBeginBlockNumber(BigInteger.valueOf(18000000))
            .setEndBlockNumber(BigInteger.valueOf(18000100))  // 扫描100个区块
            .addEthMonitorEvent(new BatchExampleMonitorEvent());

        // 批量扫描
        long startTime2 = System.currentTimeMillis();

        MagicianBlockchainScan batchScan = MagicianBlockchainScan.create()
            .setRpcUrl(EthRpcInit.create().addRpcUrl("https://mainnet.infura.io/v3/YOUR_PROJECT_ID"))
            .setScanPeriod(5000)
            .setBeginBlockNumber(BigInteger.valueOf(18000000))
            .setEndBlockNumber(BigInteger.valueOf(18000100))  // 扫描100个区块
            .enableBatchProcessing(20, 5)  // 批量大小20，并发数5
            .addEthMonitorEvent(new BatchExampleMonitorEvent());

        logger.info("Performance comparison setup completed");
        logger.info("Traditional scan: single block processing");
        logger.info("Batch scan: 20 blocks per batch, 5 concurrent threads");
    }
}
