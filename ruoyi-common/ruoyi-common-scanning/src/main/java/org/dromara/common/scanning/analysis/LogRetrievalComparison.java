package org.dromara.common.scanning.analysis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * eth_getLogs vs eth_newFilter 性能对比分析
 */
public class LogRetrievalComparison {

    private static final Logger logger = LoggerFactory.getLogger(LogRetrievalComparison.class);

    /**
     * 性能对比分析
     */
    public static void performanceAnalysis() {
        logger.info("=== ETH日志获取方式性能对比分析 ===");

        // 批量扫描场景分析
        analyzeBatchScanningScenario();

        // 实时监听场景分析
        analyzeRealTimeMonitoringScenario();

        // 资源消耗对比
        analyzeResourceConsumption();

        // 可靠性对比
        analyzeReliability();
    }

    /**
     * 批量扫描场景分析
     */
    private static void analyzeBatchScanningScenario() {
        logger.info("\n--- 批量扫描场景分析 ---");

        logger.info("eth_getLogs在批量扫描中的优势：");
        logger.info("1. 网络效率：一次RPC调用获取大量历史日志");
        logger.info("2. 精确控制：可以精确指定区块范围 [fromBlock, toBlock]");
        logger.info("3. 并发友好：多个并发请求互不干扰");
        logger.info("4. 无状态：不需要维护服务器端状态");

        logger.info("\neth_newFilter在批量扫描中的劣势：");
        logger.info("1. 双重开销：需要创建过滤器 + 获取日志两次RPC调用");
        logger.info("2. 状态管理：需要管理过滤器生命周期");
        logger.info("3. 资源占用：过滤器占用节点内存");
        logger.info("4. 并发限制：节点限制同时存在的过滤器数量");

        // 性能数据对比
        logger.info("\n性能数据对比（扫描1000个区块）：");
        logger.info("方式              | RPC调用次数 | 网络延迟 | 节点资源占用 | 并发能力");
        logger.info("eth_getLogs      | 10次        | 低      | 无          | 高");
        logger.info("eth_newFilter    | 20次        | 高      | 中等        | 受限");
    }

    /**
     * 实时监听场景分析
     */
    private static void analyzeRealTimeMonitoringScenario() {
        logger.info("\n--- 实时监听场景分析 ---");

        logger.info("eth_newFilter在实时监听中的优势：");
        logger.info("1. 设计目的：专为实时事件监听设计");
        logger.info("2. 增量获取：只返回自上次查询以来的新日志");
        logger.info("3. 效率高：避免重复获取已处理的日志");

        logger.info("\neth_getLogs在实时监听中的劣势：");
        logger.info("1. 重复数据：可能获取到已处理的日志");
        logger.info("2. 轮询开销：需要主动轮询新区块");
        logger.info("3. 数据量大：每次都获取完整的区块范围日志");
    }

    /**
     * 资源消耗对比
     */
    private static void analyzeResourceConsumption() {
        logger.info("\n--- 资源消耗对比 ---");

        logger.info("客户端资源消耗：");
        logger.info("eth_getLogs：");
        logger.info("  - 内存：存储返回的日志数据");
        logger.info("  - CPU：JSON解析和数据处理");
        logger.info("  - 网络：单次大数据量传输");

        logger.info("\neth_newFilter：");
        logger.info("  - 内存：存储过滤器ID和状态");
        logger.info("  - CPU：过滤器管理和轮询逻辑");
        logger.info("  - 网络：多次小数据量传输");

        logger.info("\n节点资源消耗：");
        logger.info("eth_getLogs：");
        logger.info("  - 内存：无持久状态");
        logger.info("  - CPU：查询时临时计算");
        logger.info("  - 存储：无额外存储需求");

        logger.info("\neth_newFilter：");
        logger.info("  - 内存：维护过滤器状态");
        logger.info("  - CPU：持续的过滤器管理");
        logger.info("  - 存储：过滤器元数据存储");
    }

    /**
     * 可靠性对比
     */
    private static void analyzeReliability() {
        logger.info("\n--- 可靠性对比 ---");

        logger.info("eth_getLogs可靠性特点：");
        logger.info("✅ 无状态：每次调用独立，不依赖服务器状态");
        logger.info("✅ 容错性好：单次失败不影响其他调用");
        logger.info("✅ 重试简单：失败后直接重试相同调用");
        logger.info("✅ 幂等性：多次调用结果一致");

        logger.info("\neth_newFilter可靠性风险：");
        logger.info("⚠️ 状态依赖：依赖节点维护的过滤器状态");
        logger.info("⚠️ 过滤器过期：节点可能清理长时间未使用的过滤器");
        logger.info("⚠️ 重试复杂：失败时需要重新创建过滤器");
        logger.info("⚠️ 状态不一致：网络中断可能导致状态丢失");
    }

    /**
     * 批量扫描场景的最佳实践
     */
    public static void batchScanningBestPractices() {
        logger.info("\n=== 批量扫描最佳实践 ===");

        logger.info("1. 使用eth_getLogs进行批量历史数据扫描");
        logger.info("2. 合理设置区块范围，避免单次查询过大");
        logger.info("3. 实现并发查询，提高扫描效率");
        logger.info("4. 添加重试机制，处理网络异常");
        logger.info("5. 监控查询性能，动态调整批量大小");

        logger.info("\n推荐的区块范围设置：");
        logger.info("- 高活跃合约：100-500个区块/批次");
        logger.info("- 普通合约：500-2000个区块/批次");
        logger.info("- 低活跃合约：2000-5000个区块/批次");
    }

    /**
     * 实时监听场景的建议
     */
    public static void realTimeMonitoringSuggestions() {
        logger.info("\n=== 实时监听场景建议 ===");

        logger.info("如果需要实时事件监听，建议：");
        logger.info("1. 使用eth_newFilter进行实时监听");
        logger.info("2. 实现过滤器生命周期管理");
        logger.info("3. 添加过滤器重建机制");
        logger.info("4. 监控过滤器状态，及时处理异常");
        logger.info("5. 考虑使用WebSocket连接减少轮询开销");

        logger.info("\n混合模式建议：");
        logger.info("- 历史数据：使用eth_getLogs批量扫描");
        logger.info("- 实时数据：使用eth_newFilter实时监听");
        logger.info("- 数据衔接：确保历史和实时数据的连续性");
    }

    public static void main(String[] args) {
        performanceAnalysis();
        batchScanningBestPractices();
        realTimeMonitoringSuggestions();
    }
}
