package org.dromara.common.scanning.chain.model;

import org.dromara.common.scanning.chain.model.eth.EthTransactionModel;
import org.dromara.common.scanning.chain.model.tron.TronTransactionModel;

import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * Scanned transaction result
 */
public class TransactionModel {

    /**
     * Transaction objects on the Ethereum chain
     */
    private EthTransactionModel ethTransactionModel;

    /**
     * TransactionInfo on the Tron chain
     */
    private TronTransactionModel tronTransactionModel;

    /**
     * Chain type identifier (BSC, ARB, BASE, ETH, TRON, SOL)
     * 用于标识交易所属的区块链类型
     */
    private String chainType;

    /**
     * 事件处理上下文
     * 用于在事件处理器之间传递状态和结果
     * 线程安全的Map，支持并发访问
     */
    private Map<String, Object> processingContext;



    // TODO SOL are under development, so there is no result set attribute for the time being

    public static TransactionModel builder(){
        return new TransactionModel();
    }

    public EthTransactionModel getEthTransactionModel() {
        return ethTransactionModel;
    }

    public TransactionModel setEthTransactionModel(EthTransactionModel ethTransactionModel) {
        this.ethTransactionModel = ethTransactionModel;
        return this;
    }

    public TronTransactionModel getTronTransactionModel() {
        return tronTransactionModel;
    }

    public TransactionModel setTronTransactionModel(TronTransactionModel tronTransactionModel) {
        this.tronTransactionModel = tronTransactionModel;
        return this;
    }

    public String getChainType() {
        return chainType;
    }

    public TransactionModel setChainType(String chainType) {
        this.chainType = chainType;
        return this;
    }

    /**
     * 获取处理上下文
     * 如果不存在则创建一个新的线程安全Map
     */
    public Map<String, Object> getProcessingContext() {
        if (processingContext == null) {
            processingContext = new ConcurrentHashMap<>();
        }
        return processingContext;
    }

    /**
     * 设置处理上下文
     */
    public TransactionModel setProcessingContext(Map<String, Object> processingContext) {
        this.processingContext = processingContext;
        return this;
    }

    /**
     * 在处理上下文中存储值
     */
    public TransactionModel putContextValue(String key, Object value) {
        getProcessingContext().put(key, value);
        return this;
    }

    /**
     * 从处理上下文中获取值
     */
    @SuppressWarnings("unchecked")
    public <T> T getContextValue(String key, Class<T> type) {
        Object value = getProcessingContext().get(key);
        if (value != null && type.isInstance(value)) {
            return (T) value;
        }
        return null;
    }

    /**
     * 从处理上下文中获取值（带默认值）
     */
    @SuppressWarnings("unchecked")
    public <T> T getContextValue(String key, Class<T> type, T defaultValue) {
        T value = getContextValue(key, type);
        return value != null ? value : defaultValue;
    }

    /**
     * 检查处理上下文中是否包含指定键
     */
    public boolean hasContextValue(String key) {
        return processingContext != null && processingContext.containsKey(key);
    }


}
