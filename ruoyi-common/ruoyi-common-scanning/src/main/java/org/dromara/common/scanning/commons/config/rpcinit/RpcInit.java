package org.dromara.common.scanning.commons.config.rpcinit;

import org.dromara.common.scanning.commons.config.BlockChainConfig;

/**
 * Set rpc address
 */
public class RpcInit {

    /**
     * Configuration object that is used to pass the configuration of the RPC URL to the main configuration object
     */
    protected BlockChainConfig blockChainConfig = new BlockChainConfig();

    public BlockChainConfig getBlockChainConfig(){
        return blockChainConfig;
    }
}
