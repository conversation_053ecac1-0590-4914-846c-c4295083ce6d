package org.dromara.common.scanning;

import org.dromara.common.scanning.biz.scan.ScanService;
import org.dromara.common.scanning.biz.thread.EventThreadPool;
import org.dromara.common.scanning.chain.RetryStrategy;
import org.dromara.common.scanning.commons.config.BatchConfig;
import org.dromara.common.scanning.commons.config.BlockChainConfig;
import org.dromara.common.scanning.commons.config.rpcinit.RpcInit;
import org.dromara.common.scanning.commons.config.rpcinit.impl.EthRpcInit;
import org.dromara.common.scanning.commons.config.rpcinit.impl.SolRpcInit;
import org.dromara.common.scanning.commons.config.rpcinit.impl.TronRpcInit;
import org.dromara.common.scanning.commons.enums.ChainType;
import org.dromara.common.scanning.monitor.EthMonitorEvent;
import org.dromara.common.scanning.monitor.TronMonitorEvent;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * Main class, used to create a block sweep task
 */
public class MagicianBlockchainScan {

    /**
     * Business class, Used to perform scan block logic
     */
    private ScanService scanService;

    /**
     * Configure the parameters required for this block scanning task
     */
    private BlockChainConfig blockChainConfig;

    /**
     * Does the rpc address exist
     */
    private boolean rpcUrlExist = false;

    /**
     * all scan job
     */
    private static List<MagicianBlockchainScan> magicianBlockchainScans = new ArrayList<>();

    private MagicianBlockchainScan(){
        scanService = new ScanService();
        blockChainConfig = new BlockChainConfig();
    }

    public static MagicianBlockchainScan create(){
        return new MagicianBlockchainScan();
    }

    /**
     * Set the RPC URL for the blockchain
     * @param rpcInit
     * @return
     */
    public MagicianBlockchainScan setRpcUrl(RpcInit rpcInit){
        if(rpcInit instanceof EthRpcInit){
            blockChainConfig.setChainType(ChainType.ETH);
            blockChainConfig.setHttpService(rpcInit.getBlockChainConfig().getHttpService());

            // 设置扩展字段存储具体的链名称
            String customChainName = rpcInit.getBlockChainConfig().getCustomChainName();
            if (customChainName != null) {
                blockChainConfig.setCustomChainName(customChainName);
            }
        } else if(rpcInit instanceof SolRpcInit){
            blockChainConfig.setChainType(ChainType.SOL);
            // TODO In development.......
        } else if(rpcInit instanceof TronRpcInit){
            blockChainConfig.setChainType(ChainType.TRON);
            blockChainConfig.setTronRpcUrls(rpcInit.getBlockChainConfig().getTronRpcUrls());
        }
        rpcUrlExist = true;
        return this;
    }

    /**
     * Setting the retry strategy
     * @param retryStrategy
     * @return
     */
    public MagicianBlockchainScan setRetryStrategy(RetryStrategy retryStrategy){
        blockChainConfig.setRetryStrategy(retryStrategy);
        return this;
    }

    /**
     * Set the scan polling interval, milliseconds
     * @param scanPeriod
     * @return
     */
    public MagicianBlockchainScan setScanPeriod(long scanPeriod) {
        blockChainConfig.setScanPeriod(scanPeriod);
        return this;
    }

    /**
     * Set the starting block height of the scan
     * @param beginBlockNumber
     * @return
     */
    public MagicianBlockchainScan setBeginBlockNumber(BigInteger beginBlockNumber) {
        blockChainConfig.setBeginBlockNumber(beginBlockNumber);
        return this;
    }

    /**
     * Set the end block height of the scan
     * @param endBlockNumber
     * @return
     */
    public MagicianBlockchainScan setEndBlockNumber(BigInteger endBlockNumber) {
        blockChainConfig.setEndBlockNumber(endBlockNumber);
        return this;
    }

    /**
     * Add ETH monitoring event
     * @param ethMonitorEvent
     * @return
     */
    public MagicianBlockchainScan addEthMonitorEvent(EthMonitorEvent ethMonitorEvent) {
        blockChainConfig.getEventConfig().addEthMonitorEvent(ethMonitorEvent);
        return this;
    }

    /**
     * Add TRON monitoring event
     * @param tronMonitorEvent
     * @return
     */
    public MagicianBlockchainScan addTronMonitorEvent(TronMonitorEvent tronMonitorEvent) {
        blockChainConfig.getEventConfig().addTronMonitorEvents(tronMonitorEvent);
        return this;
    }

    /**
     * Set batch processing configuration
     * @param batchConfig
     * @return
     */
    public MagicianBlockchainScan setBatchConfig(BatchConfig batchConfig) {
        blockChainConfig.setBatchConfig(batchConfig);
        return this;
    }

    /**
     * Enable batch processing with default configuration
     * @return
     */
    public MagicianBlockchainScan enableBatchProcessing() {
        blockChainConfig.setBatchConfig(BatchConfig.getDefault().setEnabled(true));
        return this;
    }

    /**
     * Enable batch processing with custom batch size
     * @param batchSize
     * @return
     */
    public MagicianBlockchainScan enableBatchProcessing(int batchSize) {
        blockChainConfig.setBatchConfig(BatchConfig.getDefault()
            .setEnabled(true)
            .setBatchSize(batchSize));
        return this;
    }

    /**
     * Enable batch processing with custom configuration
     * @param batchSize
     * @param concurrentThreads
     * @return
     */
    public MagicianBlockchainScan enableBatchProcessing(int batchSize, int concurrentThreads) {
        blockChainConfig.setBatchConfig(BatchConfig.getDefault()
            .setEnabled(true)
            .setBatchSize(batchSize)
            .setConcurrentThreads(concurrentThreads));
        return this;
    }

    /**
     * Enable log scanning mode (use eth_getLogs instead of block scanning)
     * @return
     */
    public MagicianBlockchainScan enableLogScanning() {
        if (blockChainConfig.getBatchConfig() == null) {
            blockChainConfig.setBatchConfig(BatchConfig.getDefault());
        }
        blockChainConfig.getBatchConfig().setLogScanEnabled(true);
        return this;
    }

    /**
     * Enable log scanning with custom configuration
     * @param batchSize
     * @param concurrentThreads
     * @return
     */
    public MagicianBlockchainScan enableLogScanning(int batchSize, int concurrentThreads) {
        blockChainConfig.setBatchConfig(BatchConfig.getDefault()
            .setLogScanEnabled(true)
            .setBatchSize(batchSize)
            .setConcurrentThreads(concurrentThreads));
        return this;
    }

    /**
     * start a task
     * @throws Exception
     */
    public void start() throws Exception {
        if (rpcUrlExist == false) {
            throw new Exception("rpcUrl cannot be empty");
        }

        if (blockChainConfig.getChainType() == null) {
            throw new Exception("ChainType cannot be empty");
        }

        if (blockChainConfig.getScanPeriod() < 1) {
            throw new Exception("scanPeriod must be greater than 1");
        }

        if(blockChainConfig.getDelayed() < 0){
            throw new Exception("delayed must be greater than 0");
        }

        if (blockChainConfig.getChainType().equals(ChainType.ETH)
                && (blockChainConfig.getEventConfig() == null
                || blockChainConfig.getEventConfig().getEthMonitorEvent() == null
                || blockChainConfig.getEventConfig().getEthMonitorEvent().size() < 1)
        ) {
            throw new Exception("You need to set up at least one monitor event");
        }

        if (blockChainConfig.getChainType().equals(ChainType.TRON)
                && (blockChainConfig.getEventConfig() == null
                || blockChainConfig.getEventConfig().getTronMonitorEvents() == null
                || blockChainConfig.getEventConfig().getTronMonitorEvents().size() < 1)
        ) {
            throw new Exception("You need to set up at least one monitor event");
        }

        // initialization scanService
        scanService.init(blockChainConfig);

        // execute the scan
        scanService.start();

        magicianBlockchainScans.add(this);
    }

    /**
     * Stop the current scan job
     */
    public void shutdown() {
        scanService.shutdown();
    }

    /**
     * Stop all scan job
     */
    public static void shutdownAll(){
        for(MagicianBlockchainScan magicianBlockchainScan : magicianBlockchainScans){
            magicianBlockchainScan.shutdown();
        }
        magicianBlockchainScans.clear();
        magicianBlockchainScans = null;

        EventThreadPool.shutdown();
    }

    /**
     * Get current block height
     * @return
     */
    public BigInteger getCurrentBlockHeight() {
        return scanService.getCurrentBlockHeight();
    }
}
