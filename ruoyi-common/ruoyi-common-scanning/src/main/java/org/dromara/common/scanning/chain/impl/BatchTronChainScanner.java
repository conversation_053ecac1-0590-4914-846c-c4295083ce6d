package org.dromara.common.scanning.chain.impl;

import org.dromara.common.scanning.biz.scan.ScanService;
import org.dromara.common.scanning.biz.thread.EventQueue;
import org.dromara.common.scanning.biz.thread.RetryStrategyQueue;
import org.dromara.common.scanning.biz.thread.model.EventModel;
import org.dromara.common.scanning.chain.ChainScanner;
import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.chain.model.tron.TronBlockModel;
import org.dromara.common.scanning.chain.model.tron.TronTransactionModel;
import org.dromara.common.scanning.commons.config.BatchConfig;
import org.dromara.common.scanning.commons.config.BlockChainConfig;
import org.dromara.common.scanning.commons.constant.TronConstants;
import org.dromara.common.scanning.commons.enums.BlockEnums;
import org.dromara.common.scanning.commons.util.JSONUtil;
import org.dromara.common.scanning.commons.util.StringUtil;
import org.dromara.common.scanning.commons.util.okhttp.OkHttpUtil;
import org.dromara.common.scanning.monitor.TronMonitorEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * TRON批量区块扫描器
 * 使用getblockbylimitnext API批量获取区块，提升扫描效率
 * 专注于批量查询功能，保持架构简洁
 * 取消降级机制，异常时直接进入重试队列
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
public class BatchTronChainScanner extends ChainScanner {

    private static final Logger logger = LoggerFactory.getLogger(BatchTronChainScanner.class);

    /**
     * TRON Node url列表
     */
    private List<String> tronRpcUrls;

    /**
     * TRON监控事件列表
     */
    private List<TronMonitorEvent> tronMonitorEvents;

    /**
     * 批量配置
     */
    private BatchConfig batchConfig;

    /**
     * 初始化方法（ChainScanner架构要求）
     */
    @Override
    public void init(BlockChainConfig blockChainConfig, EventQueue eventQueue,
                     RetryStrategyQueue retryStrategyQueue, ScanService scanService) {
        super.init(blockChainConfig, eventQueue, retryStrategyQueue, scanService);

        this.tronRpcUrls = blockChainConfig.getTronRpcUrls();
        this.tronMonitorEvents = blockChainConfig.getEventConfig().getTronMonitorEvents();
        this.batchConfig = blockChainConfig.getBatchConfig();

        if (this.batchConfig == null) {
            this.batchConfig = BatchConfig.getDefault();
        }

        logger.info("BatchTronChainScanner initialized with batchSize: {}, rpcNodes: {}",
            batchConfig.getBatchSize(), tronRpcUrls.size());
    }

    /**
     * 扫描方法（ChainScanner架构要求）
     */
    @Override
    public void scan(BigInteger beginBlockNumber) {
        try {
            // 批量扫描区块
            scanBatch(beginBlockNumber);
        } catch (Exception e) {
            logger.error("[TRON-BATCH], Exception occurred during scanning, block: {}", beginBlockNumber, e);
            // 直接添加到重试队列
            addRetry(beginBlockNumber);
        }
    }

    /**
     * 批量扫描区块
     */
    private void scanBatch(BigInteger beginBlockNumber) {
        try {
            String url = this.tronRpcUrls.get(getNextIndex(tronRpcUrls.size()));

            // 获取最新区块号
            BigInteger lastBlockNumber = getLatestBlock(url);

            if (beginBlockNumber.compareTo(BlockEnums.LAST_BLOCK_NUMBER.getValue()) == 0) {
                beginBlockNumber = lastBlockNumber;
            }

            if (scanService.getCurrentBlockHeight() == null) {
                scanService.setCurrentBlockHeight(lastBlockNumber);
            }

            if (beginBlockNumber.compareTo(lastBlockNumber) > 0) {
                logger.info("[TRON-BATCH], Chain height behind scan progress, pausing... scan: {}, latest: {}",
                    beginBlockNumber, lastBlockNumber);
                return;
            }

            // 检查是否已达到设置的结束区块
            if (blockChainConfig.getEndBlockNumber().compareTo(BigInteger.ZERO) > 0
                    && beginBlockNumber.compareTo(blockChainConfig.getEndBlockNumber()) >= 0) {
                logger.info("[TRON-BATCH], The current block height has reached the stop block height you set, so the scan job has been automatically stopped, scan progress [{}], end block height:[{}]",
                    beginBlockNumber, blockChainConfig.getEndBlockNumber());
                scanService.shutdown();
                return;
            }

            // 计算批量扫描范围
            BigInteger endBlockNumber = beginBlockNumber.add(BigInteger.valueOf(batchConfig.getBatchSize() - 1));
            if (endBlockNumber.compareTo(lastBlockNumber) > 0) {
                endBlockNumber = lastBlockNumber;
            }

            // 确保批量扫描不超过设置的结束区块
            if (blockChainConfig.getEndBlockNumber().compareTo(BigInteger.ZERO) > 0
                    && endBlockNumber.compareTo(blockChainConfig.getEndBlockNumber()) > 0) {
                endBlockNumber = blockChainConfig.getEndBlockNumber();
                logger.debug("[TRON-BATCH], Adjusted end block to configured limit: {}", endBlockNumber);
            }

            logger.debug("[TRON-BATCH], Starting batch scan from {} to {}", beginBlockNumber, endBlockNumber);

            // 批量获取区块
            List<TronBlockModel> blocks = getBatchBlocks(url, beginBlockNumber, endBlockNumber.add(BigInteger.ONE));

            if (blocks.isEmpty()) {
                logger.debug("[TRON-BATCH], No blocks returned for range {} - {}", beginBlockNumber, endBlockNumber);
                updateScanProgress(endBlockNumber);
                return;
            }

            logger.debug("[TRON-BATCH], Processing {} blocks", blocks.size());

            // 处理每个区块
            List<TransactionModel> allTransactions = new ArrayList<>();
            BigInteger currentBlockNumber = beginBlockNumber;

            for (TronBlockModel block : blocks) {
                try {
                    List<TransactionModel> blockTransactions = processBlock(block);
                    allTransactions.addAll(blockTransactions);
                    currentBlockNumber = currentBlockNumber.add(BigInteger.ONE);
                } catch (Exception e) {
                    logger.warn("[TRON-BATCH], Failed to process block in batch: {}", e.getMessage());
                }
            }

            // 将所有交易添加到事件队列
            if (!allTransactions.isEmpty()) {
                eventQueue.add(EventModel.builder()
                    .setCurrentBlockHeight(endBlockNumber)
                    .setTransactionModels(allTransactions)
                );
            }

            // 更新扫描进度
            updateScanProgress(endBlockNumber);

            logger.debug("[TRON-BATCH], Batch scan completed, processed {} transactions", allTransactions.size());

        } catch (Exception e) {
            logger.error("[TRON-BATCH], Batch scan failed for block {}: {}", beginBlockNumber, e.getMessage());
            throw new RuntimeException("Batch scan failed for block " + beginBlockNumber, e);
        }
    }



    /**
     * 处理单个区块，提取交易数据
     */
    private List<TransactionModel> processBlock(TronBlockModel block) {
        List<TransactionModel> transactionList = new ArrayList<>();

        if (block == null || block.getTransactions() == null) {
            return transactionList;
        }

        List<TronTransactionModel> tronTransactionList = block.getTransactions();

        for (TronTransactionModel transaction : tronTransactionList) {
            if (transaction == null) {
                continue;
            }
            // 设置区块信息
            transaction.setBlockID(block.getBlockID());
            transaction.setTronBlockHeaderModel(block.getTronBlockHeaderModel());

            transactionList.add(
                TransactionModel.builder()
                    .setChainType(getEffectiveChainName()) // {{ AURA-X: Modify - 使用自定义链名称或默认链类型名称. Approval: 寸止(ID:1678886403). }}
                    .setTronTransactionModel(transaction)
            );
        }

        return transactionList;
    }

    /**
     * 事件处理方法（ChainScanner架构要求）
     */
    @Override
    public void call(TransactionModel transactionModel) {
        // 复用现有TronMonitorEvent处理逻辑
        for (TronMonitorEvent tronMonitorEvent : this.tronMonitorEvents) {
            try {
                tronMonitorEvent.call(transactionModel);
            } catch (Exception e) {
                logger.error("[TRON-BATCH], An exception occurred in the call method of the listener", e);
            }
        }
    }

    /**
     * 获取有效的链名称
     * {{ AURA-X: Modify - 添加获取有效链名称的方法，优先使用自定义链名称. Approval: 寸止(ID:1678886403). }}
     *
     * @return 链名称
     */
    private String getEffectiveChainName() {
        String customChainName = blockChainConfig.getCustomChainName();
        if (customChainName != null && !customChainName.trim().isEmpty()) {
            return customChainName;
        }
        return blockChainConfig.getChainType().getChainName();
    }

    // ============ 私有辅助方法 ============

    /**
     * 批量获取区块
     */
    private List<TronBlockModel> getBatchBlocks(String url, BigInteger startNum, BigInteger endNum) throws Exception {
        String batchUrl = url + TronConstants.GET_BLOCK_BY_LIMIT_NEXT;

        // 构建批量请求参数
        java.util.Map<String, Object> params = new java.util.HashMap<>();
        params.put("startNum", startNum.longValue());
        params.put("endNum", endNum.longValue());

        String result = OkHttpUtil.postJson(batchUrl, params);

        if (StringUtil.isEmpty(result)) {
            throw new Exception("Batch block request returned null result");
        }

        // 解析批量区块响应
        return parseBatchBlocksResponse(result);
    }

    /**
     * 解析批量区块响应
     * TRON API返回格式: {"block": [区块1, 区块2, ...]}
     */
    private List<TronBlockModel> parseBatchBlocksResponse(String result) throws Exception {
        try {
            // 使用新的JSON解析方法，直接从"block"路径提取数组并解析为TronBlockModel列表
            List<TronBlockModel> blocks = JSONUtil.parseArrayFromPath(result, "block", TronBlockModel.class);

            if (blocks.isEmpty()) {
                logger.debug("[TRON-BATCH], No blocks found in response or parsing failed");
            } else {
                logger.debug("[TRON-BATCH], Successfully parsed {} blocks from response", blocks.size());
            }

            return blocks;
        } catch (Exception e) {
            logger.error("[TRON-BATCH], Failed to parse batch blocks response: {}", e.getMessage());
            throw new Exception("Failed to parse batch blocks response: " + e.getMessage());
        }
    }

    /**
     * 获取最新区块号
     */
    private BigInteger getLatestBlock(String url) throws Exception {
        TronBlockModel latestBlock = getBlock(url);
        return latestBlock.getTronBlockHeaderModel().getTronRawDataModel().getNumber();
    }

    /**
     * 获取最新区块（复用现有逻辑）
     */
    private TronBlockModel getBlock(String url) throws Exception {
        String result = OkHttpUtil.postJson(url + TronConstants.GET_NOW_BLOCK, TronConstants.GET_NOW_BLOCK_PARAMETER);
        if (StringUtil.isEmpty(result)) {
            throw new Exception("An exception occurred when obtaining the latest block, result: null");
        }
        return JSONUtil.toJavaObject(result, TronBlockModel.class);
    }



    /**
     * 更新扫描进度
     */
    private void updateScanProgress(BigInteger blockNumber) {
        blockChainConfig.setBeginBlockNumber(blockNumber.add(BigInteger.ONE));
        scanService.setCurrentBlockHeight(blockNumber);
    }
}
