package org.dromara.common.scanning.chain.model.tron;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * TransactionInfo on the Tron chain
 */
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class TronTransactionModel {

    @JsonProperty("txID")
    private String txID;

    @JsonProperty("ret")
    private List<TronRetModel> ret;

    @JsonProperty("signature")
    private List<String> signature;

    @JsonProperty("raw_data_hex")
    private String rawDataHex;

    @JsonProperty("raw_data")
    private TronTransactionRawDataModel rawData;

    // ------------ Block information, used for developers to obtain in the listener -------------

    @JsonIgnore
    private String blockID;

    @JsonIgnore
    private TronBlockHeaderModel tronBlockHeaderModel;

}
