package org.dromara.common.scanning.commons.config;

/**
 * 批量处理配置类
 */
public class BatchConfig {

    /**
     * 是否启用批量模式
     */
    private boolean enabled = true;

    /**
     * 批量大小 - 每次获取的区块数量
     */
    private int batchSize = 30;

    /**
     * 并发线程数 - 用于并发获取区块
     */
    private int concurrentThreads = 5;

    /**
     * 批量超时时间（毫秒）
     */
    private long batchTimeoutMs = 30000;

    /**
     * 是否启用自适应批量大小
     */
    private boolean adaptiveBatchSize = true;

    /**
     * 最小批量大小
     */
    private int minBatchSize = 5;

    /**
     * 最大批量大小
     */
    private int maxBatchSize = 100;

    /**
     * 失败率阈值，超过此阈值将减小批量大小
     */
    private double failureRateThreshold = 0.1;

    /**
     * 是否启用日志扫描模式（使用eth_getLogs而不是区块扫描）
     */
    private boolean logScanEnabled = false;

    /**
     * 获取默认配置
     */
    public static BatchConfig getDefault() {
        return new BatchConfig();
    }

    /**
     * 创建批量配置构建器
     */
    public static BatchConfig builder() {
        return new BatchConfig();
    }

    public boolean isEnabled() {
        return enabled;
    }

    public BatchConfig setEnabled(boolean enabled) {
        this.enabled = enabled;
        return this;
    }

    public int getBatchSize() {
        return batchSize;
    }

    public BatchConfig setBatchSize(int batchSize) {
        if (batchSize <= 0) {
            throw new IllegalArgumentException("Batch size must be positive");
        }
        this.batchSize = batchSize;
        return this;
    }

    public int getConcurrentThreads() {
        return concurrentThreads;
    }

    public BatchConfig setConcurrentThreads(int concurrentThreads) {
        if (concurrentThreads <= 0) {
            throw new IllegalArgumentException("Concurrent threads must be positive");
        }
        this.concurrentThreads = concurrentThreads;
        return this;
    }

    public long getBatchTimeoutMs() {
        return batchTimeoutMs;
    }

    public BatchConfig setBatchTimeoutMs(long batchTimeoutMs) {
        if (batchTimeoutMs <= 0) {
            throw new IllegalArgumentException("Batch timeout must be positive");
        }
        this.batchTimeoutMs = batchTimeoutMs;
        return this;
    }

    public boolean isAdaptiveBatchSize() {
        return adaptiveBatchSize;
    }

    public BatchConfig setAdaptiveBatchSize(boolean adaptiveBatchSize) {
        this.adaptiveBatchSize = adaptiveBatchSize;
        return this;
    }

    public int getMinBatchSize() {
        return minBatchSize;
    }

    public BatchConfig setMinBatchSize(int minBatchSize) {
        if (minBatchSize <= 0) {
            throw new IllegalArgumentException("Min batch size must be positive");
        }
        this.minBatchSize = minBatchSize;
        return this;
    }

    public int getMaxBatchSize() {
        return maxBatchSize;
    }

    public BatchConfig setMaxBatchSize(int maxBatchSize) {
        if (maxBatchSize <= 0) {
            throw new IllegalArgumentException("Max batch size must be positive");
        }
        this.maxBatchSize = maxBatchSize;
        return this;
    }

    public double getFailureRateThreshold() {
        return failureRateThreshold;
    }

    public BatchConfig setFailureRateThreshold(double failureRateThreshold) {
        if (failureRateThreshold < 0 || failureRateThreshold > 1) {
            throw new IllegalArgumentException("Failure rate threshold must be between 0 and 1");
        }
        this.failureRateThreshold = failureRateThreshold;
        return this;
    }

    public boolean isLogScanEnabled() {
        return logScanEnabled;
    }

    public BatchConfig setLogScanEnabled(boolean logScanEnabled) {
        this.logScanEnabled = logScanEnabled;
        return this;
    }

    /**
     * 验证配置的有效性
     */
    public void validate() {
        if (minBatchSize > maxBatchSize) {
            throw new IllegalArgumentException("Min batch size cannot be greater than max batch size");
        }

        if (batchSize < minBatchSize || batchSize > maxBatchSize) {
            throw new IllegalArgumentException("Batch size must be between min and max batch size");
        }
    }

    @Override
    public String toString() {
        return "BatchConfig{" +
                "enabled=" + enabled +
                ", batchSize=" + batchSize +
                ", concurrentThreads=" + concurrentThreads +
                ", batchTimeoutMs=" + batchTimeoutMs +
                ", adaptiveBatchSize=" + adaptiveBatchSize +
                ", minBatchSize=" + minBatchSize +
                ", maxBatchSize=" + maxBatchSize +
                ", failureRateThreshold=" + failureRateThreshold +
                ", logScanEnabled=" + logScanEnabled +
                '}';
    }
}
