package org.dromara.common.scanning.monitor;

import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.monitor.filter.EthMonitorFilter;

import java.util.List;

/**
 * Ethereum listening events
 */
public interface EthMonitorEvent {

    /**
     * Monitor filter
     *
     * When a qualified transaction is scanned, the call method will be triggered
     * @return
     */
    default EthMonitorFilter ethMonitorFilter(){
        return null;
    }

    /**
     * Get monitor contract addresses for RPC-level filtering
     * 获取监控合约地址列表，用于RPC层面的过滤优化
     *
     * @return 监控的合约地址列表，如果返回null或空列表则不进行地址过滤
     */
    default List<String> getMonitorContractAddresses() {
        return null;
    }

    /**
     * Filter the transaction data according to the above conditions, and execute the monitoring event
     * @param transactionModel
     */
    void call(TransactionModel transactionModel);
}
