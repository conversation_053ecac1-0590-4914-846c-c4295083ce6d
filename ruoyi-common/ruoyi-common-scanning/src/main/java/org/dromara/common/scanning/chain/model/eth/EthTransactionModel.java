package org.dromara.common.scanning.chain.model.eth;

import org.web3j.protocol.core.methods.response.EthBlock;
import org.web3j.protocol.core.methods.response.Log;

/**
 * Transaction objects on the Ethereum chain
 */
public class EthTransactionModel {

    /**
     * Block Info
     */
    private EthBlock ethBlock;

    /**
     * Transaction objects
     */
    private EthBlock.TransactionObject transactionObject;

    /**
     * Log object (for log-based scanning)
     */
    private Log log;

    public static EthTransactionModel builder(){
        return new EthTransactionModel();
    }

    public EthBlock getEthBlock() {
        ethBlock.getBlock().setTransactions(null);
        return ethBlock;
    }

    public EthTransactionModel setEthBlock(EthBlock ethBlock) {
        this.ethBlock = ethBlock;
        return this;
    }

    public EthBlock.TransactionObject getTransactionObject() {
        return transactionObject;
    }

    public EthTransactionModel setTransactionObject(EthBlock.TransactionObject transactionObject) {
        this.transactionObject = transactionObject;
        return this;
    }

    public Log getLog() {
        return log;
    }

    public EthTransactionModel setLog(Log log) {
        this.log = log;
        return this;
    }

    /**
     * Create from Log object (for log-based scanning)
     */
    public static EthTransactionModel fromLog(Log log) {
        return new EthTransactionModel().setLog(log);
    }
}
