package org.dromara.common.scanning.chain.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameter;
import org.web3j.protocol.core.methods.request.EthFilter;
import org.web3j.protocol.core.methods.response.EthLog;
import org.web3j.protocol.core.methods.response.EthUninstallFilter;
import org.web3j.protocol.core.methods.response.Log;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 基于eth_newFilter的事件扫描器（概念设计）
 *
 * 注意：这个类展示了如果要实现eth_newFilter方式需要的复杂性
 * 在当前的批量扫描场景下，不推荐使用这种方式
 */
public class FilterBasedEthScanner {

    private static final Logger logger = LoggerFactory.getLogger(FilterBasedEthScanner.class);

    /**
     * Web3j客户端
     */
    private Web3j web3j;

    /**
     * 过滤器管理器
     */
    private FilterManager filterManager;

    /**
     * 定时清理器
     */
    private ScheduledExecutorService cleanupExecutor;

    public FilterBasedEthScanner(Web3j web3j) {
        this.web3j = web3j;
        this.filterManager = new FilterManager();
        this.cleanupExecutor = Executors.newSingleThreadScheduledExecutor();

        // 定期清理过期的过滤器
        this.cleanupExecutor.scheduleAtFixedRate(
            this::cleanupExpiredFilters,
            60, 60, TimeUnit.SECONDS
        );
    }

    /**
     * 创建事件过滤器
     */
    public BigInteger createEventFilter(BigInteger fromBlock, BigInteger toBlock,
                                   List<String> addresses, List<String> topics) {
        try {
            EthFilter filter = new EthFilter(
                DefaultBlockParameter.valueOf(fromBlock),
                DefaultBlockParameter.valueOf(toBlock),
                addresses
            );

            if (topics != null && !topics.isEmpty()) {
                filter.addOptionalTopics(topics.toArray(new String[0]));
            }

            org.web3j.protocol.core.methods.response.EthFilter newFilter = web3j.ethNewFilter(filter).send();

            if (newFilter.hasError()) {
                logger.error("Failed to create filter: {}", newFilter.getError().getMessage());
                return null;
            }

            BigInteger filterId = newFilter.getFilterId();
            filterManager.registerFilter(filterId, fromBlock, toBlock);

            logger.info("Created filter {} for blocks {}-{}", filterId, fromBlock, toBlock);
            return filterId;

        } catch (Exception e) {
            logger.error("Exception creating filter", e);
            return null;
        }
    }

    /**
     * 获取过滤器日志
     */
    public List<Log> getFilterLogs(BigInteger filterId) {
        try {
            if (!filterManager.isValidFilter(filterId)) {
                logger.warn("Filter {} is not valid or expired", filterId);
                return new ArrayList<>();
            }

            EthLog filterLogs = web3j.ethGetFilterLogs(filterId).send();

            if (filterLogs.hasError()) {
                logger.error("Failed to get filter logs: {}", filterLogs.getError().getMessage());
                filterManager.markFilterAsInvalid(filterId);
                return new ArrayList<>();
            }

            List<Log> logs = new ArrayList<>();
            filterLogs.getLogs().forEach(logResult -> {
                if (logResult instanceof org.web3j.protocol.core.methods.response.EthLog.LogObject) {
                    logs.add(((org.web3j.protocol.core.methods.response.EthLog.LogObject) logResult).get());
                }
            });

            filterManager.updateLastAccess(filterId);
            logger.debug("Retrieved {} logs from filter {}", logs.size(), filterId);

            return logs;

        } catch (Exception e) {
            logger.error("Exception getting filter logs for filter {}", filterId, e);
            filterManager.markFilterAsInvalid(filterId);
            return new ArrayList<>();
        }
    }

    /**
     * 卸载过滤器
     */
    public boolean uninstallFilter(BigInteger filterId) {
        try {
            EthUninstallFilter uninstallFilter = web3j.ethUninstallFilter(filterId).send();

            if (uninstallFilter.hasError()) {
                logger.error("Failed to uninstall filter {}: {}",
                           filterId, uninstallFilter.getError().getMessage());
                return false;
            }

            boolean success = uninstallFilter.isUninstalled();
            if (success) {
                filterManager.unregisterFilter(filterId);
                logger.info("Successfully uninstalled filter {}", filterId);
            }

            return success;

        } catch (Exception e) {
            logger.error("Exception uninstalling filter {}", filterId, e);
            return false;
        }
    }

    /**
     * 清理过期的过滤器
     */
    private void cleanupExpiredFilters() {
        List<BigInteger> expiredFilters = filterManager.getExpiredFilters();

        for (BigInteger filterId : expiredFilters) {
            logger.info("Cleaning up expired filter {}", filterId);
            uninstallFilter(filterId);
        }

        if (!expiredFilters.isEmpty()) {
            logger.info("Cleaned up {} expired filters", expiredFilters.size());
        }
    }

    /**
     * 关闭扫描器
     */
    public void shutdown() {
        logger.info("Shutting down FilterBasedEthScanner");

        // 卸载所有过滤器
        List<BigInteger> allFilters = filterManager.getAllFilters();
        for (BigInteger filterId : allFilters) {
            uninstallFilter(filterId);
        }

        // 关闭清理线程
        if (cleanupExecutor != null && !cleanupExecutor.isShutdown()) {
            cleanupExecutor.shutdown();
        }
    }

    /**
     * 过滤器管理器
     */
    private static class FilterManager {
        private final Map<BigInteger, FilterInfo> filters = new ConcurrentHashMap<>();
        private final long FILTER_TIMEOUT_MS = 5 * 60 * 1000; // 5分钟超时

        public void registerFilter(BigInteger filterId, BigInteger fromBlock, BigInteger toBlock) {
            filters.put(filterId, new FilterInfo(fromBlock, toBlock, System.currentTimeMillis()));
        }

        public void unregisterFilter(BigInteger filterId) {
            filters.remove(filterId);
        }

        public boolean isValidFilter(BigInteger filterId) {
            FilterInfo info = filters.get(filterId);
            return info != null && !info.isExpired(FILTER_TIMEOUT_MS);
        }

        public void markFilterAsInvalid(BigInteger filterId) {
            filters.remove(filterId);
        }

        public void updateLastAccess(BigInteger filterId) {
            FilterInfo info = filters.get(filterId);
            if (info != null) {
                info.updateLastAccess();
            }
        }

        public List<BigInteger> getExpiredFilters() {
            List<BigInteger> expired = new ArrayList<>();
            long currentTime = System.currentTimeMillis();

            filters.entrySet().removeIf(entry -> {
                if (entry.getValue().isExpired(FILTER_TIMEOUT_MS)) {
                    expired.add(entry.getKey());
                    return true;
                }
                return false;
            });

            return expired;
        }

        public List<BigInteger> getAllFilters() {
            return new ArrayList<>(filters.keySet());
        }
    }

    /**
     * 过滤器信息
     */
    private static class FilterInfo {
        private final BigInteger fromBlock;
        private final BigInteger toBlock;
        private final long createTime;
        private volatile long lastAccessTime;

        public FilterInfo(BigInteger fromBlock, BigInteger toBlock, long createTime) {
            this.fromBlock = fromBlock;
            this.toBlock = toBlock;
            this.createTime = createTime;
            this.lastAccessTime = createTime;
        }

        public void updateLastAccess() {
            this.lastAccessTime = System.currentTimeMillis();
        }

        public boolean isExpired(long timeoutMs) {
            return (System.currentTimeMillis() - lastAccessTime) > timeoutMs;
        }
    }

    /**
     * 为什么不推荐在批量扫描中使用这种方式
     */
    public static void whyNotRecommendedForBatchScanning() {
        Logger logger = LoggerFactory.getLogger(FilterBasedEthScanner.class);

        logger.info("=== 为什么不推荐在批量扫描中使用eth_newFilter ===");

        logger.info("1. 复杂性增加：");
        logger.info("   - 需要管理过滤器生命周期");
        logger.info("   - 需要处理过滤器过期和重建");
        logger.info("   - 需要定期清理资源");

        logger.info("2. 性能开销：");
        logger.info("   - 每个过滤器需要2次RPC调用（创建+获取）");
        logger.info("   - 过滤器占用节点内存资源");
        logger.info("   - 需要额外的状态管理开销");

        logger.info("3. 可靠性风险：");
        logger.info("   - 过滤器可能被节点清理");
        logger.info("   - 网络中断可能导致状态丢失");
        logger.info("   - 错误恢复更复杂");

        logger.info("4. 扩展性限制：");
        logger.info("   - 节点限制同时存在的过滤器数量");
        logger.info("   - 不适合大规模并发扫描");

        logger.info("\n结论：在批量历史数据扫描场景下，eth_getLogs是更好的选择");
    }
}
