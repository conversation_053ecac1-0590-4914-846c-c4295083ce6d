package org.dromara.common.scanning.commons.enums;

/**
 * blockchain type
 */
public enum  ChainType {

    ETH, BSC, ARB, BASE, SOL, TRON;

    /**
     * 获取链名称字符串
     * 用于日志显示和配置映射
     */
    public String getChainName() {
        return this.name();
    }

    /**
     * 从字符串创建ChainType
     */
    public static ChainType fromString(String chainName) {
        if (chainName == null) {
            return null;
        }
        try {
            return ChainType.valueOf(chainName.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
}
