package org.dromara.common.scanning.biz.thread.model;

import org.web3j.protocol.core.methods.response.Log;

import java.math.BigInteger;
import java.util.List;

/**
 * 日志事件模型
 * 用于封装批量获取的事件日志数据
 */
public class LogEventModel {

    /**
     * 起始区块高度
     */
    private BigInteger fromBlock;

    /**
     * 结束区块高度
     */
    private BigInteger toBlock;

    /**
     * 事件日志列表
     */
    private List<Log> logs;

    /**
     * 日志数量
     */
    private int logCount;

    /**
     * 创建构建器
     */
    public static LogEventModel builder() {
        return new LogEventModel();
    }

    public BigInteger getFromBlock() {
        return fromBlock;
    }

    public LogEventModel setFromBlock(BigInteger fromBlock) {
        this.fromBlock = fromBlock;
        return this;
    }

    public BigInteger getToBlock() {
        return toBlock;
    }

    public LogEventModel setToBlock(BigInteger toBlock) {
        this.toBlock = toBlock;
        return this;
    }

    public List<Log> getLogs() {
        return logs;
    }

    public LogEventModel setLogs(List<Log> logs) {
        this.logs = logs;
        this.logCount = logs != null ? logs.size() : 0;
        return this;
    }

    public int getLogCount() {
        return logCount;
    }

    /**
     * 检查是否包含日志数据
     */
    public boolean hasLogs() {
        return logs != null && !logs.isEmpty();
    }

    /**
     * 获取区块范围大小
     */
    public BigInteger getBlockRange() {
        if (fromBlock != null && toBlock != null) {
            return toBlock.subtract(fromBlock).add(BigInteger.ONE);
        }
        return BigInteger.ZERO;
    }

    @Override
    public String toString() {
        return "LogEventModel{" +
                "fromBlock=" + fromBlock +
                ", toBlock=" + toBlock +
                ", logCount=" + logCount +
                ", blockRange=" + getBlockRange() +
                '}';
    }
}
