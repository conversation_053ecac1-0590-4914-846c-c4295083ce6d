package org.dromara.common.scanning.commons.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * JSON utility class
 */
public class JSONUtil {

    private static Logger logger = LoggerFactory.getLogger(JSONUtil.class);

    /**
     * Convert any object to another java object
     * @param obj
     * @param cls
     * @param <T>
     * @return
     */
    public static  <T> T toJavaObject(Object obj, Class<T> cls) {
        if(obj == null){
            return null;
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            if(obj instanceof String){
                return objectMapper.readValue(obj.toString(), cls);
            } else {
                return objectMapper.readValue(objectMapper.writeValueAsString(obj), cls);
            }
        } catch (Exception e){
            logger.error("An exception occurs when converting an object to another Java object through jackson", e);
            return null;
        }
    }

    /**
     * Convert any object to a Map
     * @param obj
     * @return
     */
    public static Map<String, Object> toMap(Object obj) {
        if(obj instanceof Map){
            return (Map<String, Object>) obj;
        }
        Map<String, Object> map = toJavaObject(obj, HashMap.class);
        if(map == null){
            return new HashMap<>();
        }
        return map;
    }

    /**
     * Convert any object to JSON string
     * @param obj
     * @return
     */
    public static String toJSONString(Object obj) {
        if(obj == null){
            return "";
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            if(obj instanceof  String){
                return obj.toString();
            } else {
                return objectMapper.writeValueAsString(obj);
            }
        } catch (Exception e){
            logger.error("Convert object to JSON string exception", e);
            return "";
        }
    }

    /**
     * Parse JSON string to List of specific type
     * @param jsonString JSON字符串
     * @param elementClass 列表元素的类型
     * @param <T> 泛型类型
     * @return 解析后的List，解析失败返回空List
     */
    public static <T> List<T> parseArray(String jsonString, Class<T> elementClass) {
        if (StringUtil.isEmpty(jsonString)) {
            return new ArrayList<>();
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(jsonString,
                objectMapper.getTypeFactory().constructCollectionType(List.class, elementClass));
        } catch (Exception e) {
            logger.error("Parse JSON array exception for class: {}", elementClass.getSimpleName(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Extract array from JSON path and parse to List of specific type
     * 从JSON响应中提取指定路径的数组并解析为特定类型的List
     *
     * @param jsonString JSON字符串
     * @param path JSON路径（如 "block" 表示提取 {"block": [...]} 中的数组）
     * @param elementClass 数组元素的类型
     * @param <T> 泛型类型
     * @return 解析后的List，解析失败返回空List
     */
    public static <T> List<T> parseArrayFromPath(String jsonString, String path, Class<T> elementClass) {
        if (StringUtil.isEmpty(jsonString) || StringUtil.isEmpty(path)) {
            return new ArrayList<>();
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            // 先解析为Map
            Map<String, Object> responseMap = objectMapper.readValue(jsonString, Map.class);

            // 提取指定路径的数据
            Object arrayData = responseMap.get(path);
            if (arrayData == null) {
                logger.warn("Path '{}' not found in JSON response", path);
                return new ArrayList<>();
            }

            // 将提取的数据转换为JSON字符串，再解析为指定类型的List
            String arrayJson = objectMapper.writeValueAsString(arrayData);
            return parseArray(arrayJson, elementClass);

        } catch (Exception e) {
            logger.error("Parse JSON array from path '{}' exception for class: {}", path, elementClass.getSimpleName(), e);
            return new ArrayList<>();
        }
    }
}
