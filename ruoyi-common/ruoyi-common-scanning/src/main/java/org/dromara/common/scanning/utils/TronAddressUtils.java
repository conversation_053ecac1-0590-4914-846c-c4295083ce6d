package org.dromara.common.scanning.utils;

import lombok.extern.slf4j.Slf4j;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.util.Arrays;

/**
 * TRON地址转换工具类
 * 处理TRON地址的Base58编码/解码和十六进制转换
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
public class TronAddressUtils {

    private static final String BASE58_ALPHABET = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";
    private static final int[] BASE58_INDEXES = new int[128];

    static {
        Arrays.fill(BASE58_INDEXES, -1);
        for (int i = 0; i < BASE58_ALPHABET.length(); i++) {
            BASE58_INDEXES[BASE58_ALPHABET.charAt(i)] = i;
        }
    }

    /**
     * 将TRON地址转换为十六进制格式（用于智能合约调用）
     *
     * @param address TRON地址（如：TJEv4KfgVafkvTqpjsLr97MhQfzDLdYtqJ）
     * @return 64位十六进制字符串（用于合约参数）
     */
    public static String addressToHex(String address) {
        try {
            // 1. Base58解码得到21字节的地址
            byte[] decoded = base58Decode(address);

            // 2. 去掉最后4字节的校验和，得到21字节地址
            byte[] addressBytes = Arrays.copyOf(decoded, 21);

            // 3. 验证第一个字节是0x41（TRON地址前缀）
            if (addressBytes[0] != 0x41) {
                throw new IllegalArgumentException("不是有效的TRON地址前缀: " + String.format("%02x", addressBytes[0]));
            }

            // 4. 去掉第一个字节（0x41），得到20字节的以太坊格式地址
            byte[] ethAddress = Arrays.copyOfRange(addressBytes, 1, 21);

            // 5. 转换为64位十六进制字符串（左边补0）
            StringBuilder hex = new StringBuilder();
            // 前面补24个0（12字节）
            hex.append("000000000000000000000000");
            // 后面跟20字节的地址
            for (byte b : ethAddress) {
                hex.append(String.format("%02x", b & 0xFF));
            }

            return hex.toString();

        } catch (Exception e) {
            log.error("TRON地址转换失败: address={}, error={}", address, e.getMessage());
            throw new IllegalArgumentException("无效的TRON地址: " + address);
        }
    }

    /**
     * 将十六进制地址转换为TRON地址
     *
     * @param hexAddress 十六进制地址
     * @return TRON地址
     */
    public static String hexToAddress(String hexAddress) {
        try {
            // 移除0x前缀
            if (hexAddress.startsWith("0x")) {
                hexAddress = hexAddress.substring(2);
            }

            // 确保是40位十六进制（20字节）
            if (hexAddress.length() != 40) {
                throw new IllegalArgumentException("十六进制地址长度必须是40位");
            }

            // 转换为字节数组
            byte[] addressBytes = hexStringToByteArray(hexAddress);

            // 添加TRON网络前缀0x41
            byte[] tronAddress = new byte[21];
            tronAddress[0] = 0x41;
            System.arraycopy(addressBytes, 0, tronAddress, 1, 20);

            // 计算校验和
            byte[] hash1 = sha256(tronAddress);
            byte[] hash2 = sha256(hash1);
            byte[] checksum = Arrays.copyOf(hash2, 4);

            // 组合地址和校验和
            byte[] fullAddress = new byte[25];
            System.arraycopy(tronAddress, 0, fullAddress, 0, 21);
            System.arraycopy(checksum, 0, fullAddress, 21, 4);

            // Base58编码
            return base58Encode(fullAddress);

        } catch (Exception e) {
            log.error("十六进制地址转换失败: hexAddress={}, error={}", hexAddress, e.getMessage());
            throw new IllegalArgumentException("无效的十六进制地址: " + hexAddress);
        }
    }

    /**
     * 将完整的TRON十六进制地址转换为Base58格式
     * 支持42位TRON十六进制地址（包含41前缀）和40位以太坊格式地址
     *
     * @param fullHexAddress 完整的十六进制地址（可能包含41前缀）
     * @return TRON Base58地址
     */
    public static String hexToAddressFull(String fullHexAddress) {
        if (fullHexAddress == null || fullHexAddress.trim().isEmpty()) {
            throw new IllegalArgumentException("地址不能为空");
        }

        try {
            String normalizedHex = fullHexAddress.trim().toLowerCase();
            log.debug("处理完整十六进制地址: {}", normalizedHex);

            // 移除0x前缀
            if (normalizedHex.startsWith("0x")) {
                normalizedHex = normalizedHex.substring(2);
            }

            // 处理42位TRON完整地址（包含41前缀）
            if (normalizedHex.length() == 42) {
                if (!normalizedHex.startsWith("41")) {
                    throw new IllegalArgumentException("42位地址必须以41开头: " + normalizedHex);
                }
                // 去掉41前缀，得到40位地址
                String ethFormatAddress = normalizedHex.substring(2);
                log.debug("从42位地址提取40位地址: {} -> {}", normalizedHex, ethFormatAddress);
                return hexToAddress(ethFormatAddress);
            }
            // 处理40位以太坊格式地址
            else if (normalizedHex.length() == 40) {
                log.debug("处理40位以太坊格式地址: {}", normalizedHex);
                return hexToAddress(normalizedHex);
            }
            // 不支持的长度
            else {
                throw new IllegalArgumentException(
                    String.format("不支持的地址长度: %d (期望40或42位)", normalizedHex.length()));
            }

        } catch (Exception e) {
            log.error("完整十六进制地址转换失败: fullHexAddress={}, error={}", fullHexAddress, e.getMessage());
            throw new IllegalArgumentException("无效的完整十六进制地址: " + fullHexAddress + ", 错误: " + e.getMessage());
        }
    }

    /**
     * 验证TRON地址格式是否正确
     */
    public static boolean isValidTronAddress(String address) {
        try {
            if (address == null || address.length() != 34) {
                return false;
            }

            if (!address.startsWith("T")) {
                return false;
            }

            // 尝试解码验证
            byte[] decoded = base58Decode(address);
            if (decoded.length != 25) {
                return false;
            }

            // 验证校验和
            byte[] addressBytes = Arrays.copyOf(decoded, 21);
            byte[] hash1 = sha256(addressBytes);
            byte[] hash2 = sha256(hash1);
            byte[] expectedChecksum = Arrays.copyOf(hash2, 4);
            byte[] actualChecksum = Arrays.copyOfRange(decoded, 21, 25);

            return Arrays.equals(expectedChecksum, actualChecksum);

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Base58解码（使用BigInteger实现）
     */
    private static byte[] base58Decode(String input) {
        if (input.length() == 0) {
            return new byte[0];
        }

        // 计算前导1的数量
        int leadingOnes = 0;
        while (leadingOnes < input.length() && input.charAt(leadingOnes) == '1') {
            leadingOnes++;
        }

        // 转换为BigInteger
        BigInteger num = BigInteger.ZERO;
        BigInteger base = BigInteger.valueOf(58);

        for (int i = leadingOnes; i < input.length(); i++) {
            char c = input.charAt(i);
            int digit = BASE58_INDEXES[c];
            if (digit < 0) {
                throw new IllegalArgumentException("无效的Base58字符: " + c);
            }
            num = num.multiply(base).add(BigInteger.valueOf(digit));
        }

        // 转换为字节数组
        byte[] decoded = num.toByteArray();

        // 移除符号位（如果存在）
        if (decoded.length > 1 && decoded[0] == 0) {
            decoded = Arrays.copyOfRange(decoded, 1, decoded.length);
        }

        // 添加前导零
        byte[] result = new byte[leadingOnes + decoded.length];
        System.arraycopy(decoded, 0, result, leadingOnes, decoded.length);

        return result;
    }

    /**
     * Base58编码（使用BigInteger实现）
     */
    private static String base58Encode(byte[] input) {
        if (input.length == 0) {
            return "";
        }

        // 计算前导零的数量
        int leadingZeros = 0;
        while (leadingZeros < input.length && input[leadingZeros] == 0) {
            leadingZeros++;
        }

        // 转换为BigInteger
        BigInteger num = new BigInteger(1, input);
        StringBuilder result = new StringBuilder();

        // 转换为Base58
        BigInteger base = BigInteger.valueOf(58);
        while (num.compareTo(BigInteger.ZERO) > 0) {
            BigInteger[] divmod = num.divideAndRemainder(base);
            result.append(BASE58_ALPHABET.charAt(divmod[1].intValue()));
            num = divmod[0];
        }

        // 添加前导1
        for (int i = 0; i < leadingZeros; i++) {
            result.append('1');
        }

        return result.reverse().toString();
    }

    /**
     * 十六进制字符串转字节数组
     */
    private static byte[] hexStringToByteArray(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }

    /**
     * SHA256哈希
     */
    private static byte[] sha256(byte[] input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            return digest.digest(input);
        } catch (Exception e) {
            throw new RuntimeException("SHA256计算失败", e);
        }
    }

}
