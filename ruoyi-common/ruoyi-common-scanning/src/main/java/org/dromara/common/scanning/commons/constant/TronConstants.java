package org.dromara.common.scanning.commons.constant;

import java.util.HashMap;
import java.util.Map;

public class TronConstants {

    public static final String GET_NOW_BLOCK  = "/wallet/getblock";

    public static final Map<String, Object> GET_NOW_BLOCK_PARAMETER = new HashMap<String, Object>(){
        {
            put("detail", false);
        }
    };

    public static final String GET_BLOCK_BY_NUM = "/wallet/getblockbynum";

    // ============ 批量扫描API常量 ============

    /**
     * 批量获取区块API端点
     * 用于高效的区块扫描，一次获取多个连续区块
     */
    public static final String GET_BLOCK_BY_LIMIT_NEXT = "/wallet/getblockbylimitnext";
}
