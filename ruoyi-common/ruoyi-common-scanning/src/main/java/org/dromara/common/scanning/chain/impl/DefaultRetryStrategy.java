package org.dromara.common.scanning.chain.impl;

import org.dromara.common.scanning.chain.ChainScanner;
import org.dromara.common.scanning.chain.RetryStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 默认重试策略实现
 * 提供生产级别的重试机制，包括重试次数限制、指数退避等
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public class DefaultRetryStrategy implements RetryStrategy {

    private static final Logger logger = LoggerFactory.getLogger(DefaultRetryStrategy.class);

    /**
     * 重试次数记录
     */
    private final ConcurrentHashMap<BigInteger, Integer> retryCount = new ConcurrentHashMap<>();

    /**
     * 最大重试次数
     */
    private final int maxRetries;

    /**
     * 基础延迟时间（毫秒）
     */
    private final long baseDelay;

    /**
     * 退避倍数
     */
    private final double backoffMultiplier;

    /**
     * 扫描器引用，用于重新扫描
     */
    private final ChainScanner chainScanner;

    /**
     * 构造函数 - 使用默认配置
     * 
     * @param chainScanner 扫描器实例
     */
    public DefaultRetryStrategy(ChainScanner chainScanner) {
        this(chainScanner, 3, 2000, 2.0);
    }

    /**
     * 构造函数 - 自定义配置
     * 
     * @param chainScanner 扫描器实例
     * @param maxRetries 最大重试次数
     * @param baseDelay 基础延迟时间（毫秒）
     * @param backoffMultiplier 退避倍数
     */
    public DefaultRetryStrategy(ChainScanner chainScanner, int maxRetries, long baseDelay, double backoffMultiplier) {
        this.chainScanner = chainScanner;
        this.maxRetries = maxRetries;
        this.baseDelay = baseDelay;
        this.backoffMultiplier = backoffMultiplier;
    }

    @Override
    public void retry(BigInteger blockNumber) {
        int count = retryCount.getOrDefault(blockNumber, 0) + 1;

        if (count > maxRetries) {
            logger.warn("区块 {} 超过最大重试次数 {}，放弃重试", blockNumber, maxRetries);
            retryCount.remove(blockNumber);
            return;
        }

        retryCount.put(blockNumber, count);
        logger.info("重试区块 {} - 第 {} 次尝试", blockNumber, count);

        try {
            // 指数退避延迟
            if (count > 1) {
                long delay = (long) (baseDelay * Math.pow(backoffMultiplier, count - 1));
                logger.debug("重试延迟 {} 毫秒", delay);
                Thread.sleep(delay);
            }

            // 调用扫描器重新扫描
            chainScanner.scan(blockNumber);

            // 重试成功，移除记录
            retryCount.remove(blockNumber);
            logger.info("区块 {} 重试成功", blockNumber);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("重试区块 {} 被中断", blockNumber);
        } catch (Exception e) {
            logger.error("重试区块 {} 失败: {}", blockNumber, e.getMessage());
            // 不重新抛出异常，让RetryStrategyConsumer处理重新入队
        }
    }

    /**
     * 获取当前重试统计信息
     */
    public void logRetryStatistics() {
        if (!retryCount.isEmpty()) {
            logger.info("当前重试队列中有 {} 个区块待重试", retryCount.size());
            retryCount.forEach((block, count) -> 
                logger.debug("区块 {} 已重试 {} 次", block, count));
        }
    }

    /**
     * 清理过期的重试记录
     */
    public void cleanup() {
        retryCount.clear();
        logger.info("重试策略清理完成");
    }

    /**
     * 获取最大重试次数
     */
    public int getMaxRetries() {
        return maxRetries;
    }

    /**
     * 获取基础延迟时间
     */
    public long getBaseDelay() {
        return baseDelay;
    }

    /**
     * 获取退避倍数
     */
    public double getBackoffMultiplier() {
        return backoffMultiplier;
    }
}
