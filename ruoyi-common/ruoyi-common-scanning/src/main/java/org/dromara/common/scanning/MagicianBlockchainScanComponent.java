package org.dromara.common.scanning;


import org.dromara.common.scanning.commons.config.rpcinit.impl.SolRpcInit;

import java.math.BigInteger;

/**
 * 主类, 用于创建区块扫描任务
 *
 * <AUTHOR>
 * @date 2025/2/18 16:58
 */
public class MagicianBlockchainScanComponent {

//    @Autowired
//    private StringRedisTemplate redisTemplate;
//    @Autowired
//    private TronEventOne tronEventOne;
//
    private static MagicianBlockchainScan blockChainScan;


    public static void main(String[] args) {
        startSolScan();
    }

    public static void startSolScan() {

        try {
            blockChainScan = MagicianBlockchainScan.create()
                    .setRpcUrl(SolRpcInit.create())
                    .setBeginBlockNumber(BigInteger.valueOf(323428144))
                    .setScanPeriod(3000)
//                    .addTronMonitorEvent(tronEventOne)
            ;
            blockChainScan.start();

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

//    public void startTronScan() {
//        if (WalletConfig.openSyncTron.equals(false)) {
//            log.info("Tron区块扫描功能已关闭");
//            return;
//        }
//        EventThreadPool.init(2);
//        /*
//         * 从数据库获取最新区块高度，如果没有则从接口获取
//         */
////        BigInteger startBlock;
////        if (Boolean.TRUE.equals(redisTemplate.hasKey(RedisConstants.BIZ_BLOCK_NUMBER_TRON))) {
////            startBlock = new BigInteger(Objects.requireNonNull(redisTemplate.opsForValue().get(RedisConstants.BIZ_BLOCK_NUMBER_TRON)));
////            startBlock = startBlock.subtract(BigInteger.ONE);
////        } else {
//        String rs = HttpUtil.get(WalletConfig.tronApiUrl + "/wallet/getnowblock?visible=true");
//        JSONObject jsonObject = JSONUtil.parseObj(rs);
//        BigInteger startBlock = jsonObject.getJSONObject("block_header").getJSONObject("raw_data").getBigInteger("number");
////        }
//
//        /*
//         * 跟ETH的区别一，这里需要用TronRpcInit
//         */
//        try {
//            blockChainScan = MagicianBlockchainScan.create()
//                    .setRpcUrl(TronRpcInit.create().addRpcUrl(WalletConfig.tronApiUrl + "/wallet"))
//                    .setBeginBlockNumber(startBlock)
//                    .setScanPeriod(3000)
//                    .addTronMonitorEvent(tronEventOne);
//            blockChainScan.start();
//
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//
//        /*
//         * 多线程补偿
//         */
//
//        if (Boolean.FALSE.equals(redisTemplate.hasKey(RedisConstants.BIZ_BLOCK_NUMBER_TRON))) {
//            return;
//        }
//        BigInteger begin = new BigInteger(Objects.requireNonNull(redisTemplate.opsForValue().get(RedisConstants.BIZ_BLOCK_NUMBER_TRON)));
////        //分成3份
////        BigInteger gap = startBlock.subtract(begin).divide(new BigInteger("3"));
////        while (begin.compareTo(startBlock) < 0) {
////            BigInteger end = begin.add(gap);
//        BigInteger end = startBlock;
//        String redisKey = RedisConstants.BIZ_BLOCK_NUMBER_TRON_COMPENSATE + end;
//        redisTemplate.opsForValue().set(redisKey, begin.toString());
//        try {
//            MagicianBlockchainScan magicianBlockchainScan = MagicianBlockchainScan.create()
//                    .setRpcUrl(TronRpcInit.create().addRpcUrl(WalletConfig.tronApiUrl + "/wallet"))
//                    .setBeginBlockNumber(begin)
//                    .setEndBlockNumber(end)
//                    .setScanPeriod(1400)
//                    .addTronMonitorEvent(tronEventOne);
//            magicianBlockchainScan.start();
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//        //finally {
////                begin = end;
////            }
////    }
//
//    }
//
//    public void startTronScan(BigInteger begin, BigInteger end) {
//        EventThreadPool.init(1);
//
//        String redisKey = RedisConstants.BIZ_BLOCK_NUMBER_TRON_COMPENSATE + end;
//        if (Boolean.FALSE.equals(redisTemplate.hasKey(redisKey))) {
//            redisTemplate.opsForValue().set(redisKey, end.toString());
//        }
//
//        try {
//            MagicianBlockchainScan magicianBlockchainScan = MagicianBlockchainScan.create()
//                    .setRpcUrl(TronRpcInit.create().addRpcUrl(WalletConfig.tronApiUrl + "/wallet"))
//                    .setBeginBlockNumber(begin)
//                    .setEndBlockNumber(end)
//                    .setScanPeriod(1500)
//                    .addTronMonitorEvent(tronEventOne);
//            magicianBlockchainScan.start();
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//
//    }
}
