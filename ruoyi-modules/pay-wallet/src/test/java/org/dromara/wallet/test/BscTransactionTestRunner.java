package org.dromara.wallet.test;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.chain.model.eth.EthTransactionModel;
import org.web3j.protocol.core.methods.response.Log;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * BSC交易测试运行器
 * 提供模拟的TransactionModel数据用于测试
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Slf4j
public class BscTransactionTestRunner {

    /**
     * 创建模拟的BSC交易数据
     * 基于真实的BSC链交易结构
     */
    public static List<TransactionModel> createMockBscTransactions() {
        List<TransactionModel> transactions = new ArrayList<>();

        // 交易1: USDT转账交易
        TransactionModel tx1 = createMockTransaction(
            "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
            BigInteger.valueOf(35000000L),
            "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
            "******************************************", // USDT合约
            BigInteger.valueOf(100L),
            "******************************************", // from
            "0xa817C3f712C41EAB66C67A7F0E1De9E72114DBeF", // to
            "1000000000000000000" // 1 USDT (18位精度)
        );
        transactions.add(tx1);

        // 交易2: BNB原生转账
        TransactionModel tx2 = createMockTransaction(
            "0x2345678901bcdef12345678901bcdef12345678901bcdef12345678901bcdef1",
            BigInteger.valueOf(35000001L),
            "0xbcdef12345678901bcdef12345678901bcdef12345678901bcdef12345678901",
            "0x0000000000000000000000000000000000000000", // 原生代币
            BigInteger.valueOf(101L),
            "0xa817C3f712C41EAB66C67A7F0E1De9E72114DBeF", // from
            "******************************************", // to
            "500000000000000000" // 0.5 BNB
        );
        transactions.add(tx2);

        // 交易3: 另一个USDT转账
        TransactionModel tx3 = createMockTransaction(
            "0x3456789012cdef123456789012cdef123456789012cdef123456789012cdef12",
            BigInteger.valueOf(35000002L),
            "0xcdef123456789012cdef123456789012cdef123456789012cdef123456789012",
            "******************************************", // USDT合约
            BigInteger.valueOf(102L),
            "0x742d35Cc6634C0532925a3b8D4C9db96590c6C87", // from
            "******************************************", // to
            "2500000000000000000" // 2.5 USDT
        );
        transactions.add(tx3);

        log.info("创建了{}个模拟BSC交易用于测试", transactions.size());
        return transactions;
    }

    /**
     * 创建单个模拟交易
     */
    private static TransactionModel createMockTransaction(
            String txHash,
            BigInteger blockNumber,
            String blockHash,
            String contractAddress,
            BigInteger txIndex,
            String fromAddress,
            String toAddress,
            String amount) {

        // 创建模拟的Log对象
        Log mockLog = createMockLog(txHash, blockNumber, blockHash, contractAddress, txIndex, fromAddress, toAddress, amount);

        // 创建EthTransactionModel
        EthTransactionModel ethTxModel = EthTransactionModel.builder()
            .setLog(mockLog);

        // 创建TransactionModel
        TransactionModel transactionModel = TransactionModel.builder()
            .setEthTransactionModel(ethTxModel)
            .setChainType("BSC");

        return transactionModel;
    }

    /**
     * 创建模拟的Log对象
     */
    private static Log createMockLog(
            String txHash,
            BigInteger blockNumber,
            String blockHash,
            String contractAddress,
            BigInteger txIndex,
            String fromAddress,
            String toAddress,
            String amount) {

        Log log = new Log();
        log.setTransactionHash(txHash);
        log.setBlockNumber(blockNumber.toString());
        log.setBlockHash(blockHash);
        log.setAddress(contractAddress);
        log.setTransactionIndex(txIndex.toString());
        log.setLogIndex(BigInteger.ZERO.toString());

        // 设置Transfer事件的topics
        List<String> topics = new ArrayList<>();
        topics.add("0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"); // Transfer事件签名
        topics.add(addressToTopic(fromAddress)); // from地址
        topics.add(addressToTopic(toAddress));   // to地址
        log.setTopics(topics);

        // 设置转账金额数据
        log.setData(amountToHex(amount));

        return log;
    }

    /**
     * 将地址转换为topic格式（32字节）
     */
    private static String addressToTopic(String address) {
        if (address.startsWith("0x")) {
            address = address.substring(2);
        }
        return "0x" + "000000000000000000000000" + address.toLowerCase();
    }

    /**
     * 将金额转换为十六进制数据
     */
    private static String amountToHex(String amount) {
        BigInteger amountBig = new BigInteger(amount);
        String hex = amountBig.toString(16);
        // 补齐到64位（32字节）
        while (hex.length() < 64) {
            hex = "0" + hex;
        }
        return "0x" + hex;
    }

    /**
     * 打印交易详细信息用于调试
     */
    public static void printTransactionDetails(List<TransactionModel> transactions) {
        log.info("=== BSC测试交易数据详情 ===");

        for (int i = 0; i < transactions.size(); i++) {
            TransactionModel tx = transactions.get(i);
            EthTransactionModel ethTx = tx.getEthTransactionModel();
            Log txLog = ethTx.getLog();

            log.info("交易 #{}", i + 1);
            log.info("  交易哈希: {}", txLog.getTransactionHash());
            log.info("  区块号: {}", txLog.getBlockNumber());
            log.info("  区块哈希: {}", txLog.getBlockHash());
            log.info("  合约地址: {}", txLog.getAddress());
            log.info("  交易索引: {}", txLog.getTransactionIndex());
            log.info("  链类型: {}", tx.getChainType());

            if (txLog.getTopics() != null && txLog.getTopics().size() >= 3) {
                String fromTopic = txLog.getTopics().get(1);
                String toTopic = txLog.getTopics().get(2);
                String fromAddress = "0x" + fromTopic.substring(26);
                String toAddress = "0x" + toTopic.substring(26);

                log.info("  发送方: {}", fromAddress);
                log.info("  接收方: {}", toAddress);
            }

            if (txLog.getData() != null && !txLog.getData().equals("0x")) {
                try {
                    BigInteger amount = new BigInteger(txLog.getData().substring(2), 16);
                    log.info("  转账金额(wei): {}", amount);
                } catch (Exception e) {
                    log.warn("  无法解析转账金额: {}", e.getMessage());
                }
            }
            log.info("  ---");
        }
    }

    /**
     * 验证交易数据的完整性
     */
    public static boolean validateTransactionData(List<TransactionModel> transactions) {
        if (transactions == null || transactions.isEmpty()) {
            log.error("交易列表为空");
            return false;
        }

        for (int i = 0; i < transactions.size(); i++) {
            TransactionModel tx = transactions.get(i);

            if (tx == null) {
                log.error("交易 #{} 为null", i + 1);
                return false;
            }

            if (tx.getChainType() == null || !tx.getChainType().equals("BSC")) {
                log.error("交易 #{} 链类型错误: {}", i + 1, tx.getChainType());
                return false;
            }

            if (tx.getEthTransactionModel() == null) {
                log.error("交易 #{} EthTransactionModel为null", i + 1);
                return false;
            }

            Log txLog = tx.getEthTransactionModel().getLog();
            if (txLog == null) {
                log.error("交易 #{} Log为null", i + 1);
                return false;
            }

            if (txLog.getTransactionHash() == null || txLog.getTransactionHash().isEmpty()) {
                log.error("交易 #{} 交易哈希为空", i + 1);
                return false;
            }

            if (txLog.getBlockNumber() == null) {
                log.error("交易 #{} 区块号为null", i + 1);
                return false;
            }
        }

        log.info("所有交易数据验证通过");
        return true;
    }

    /**
     * 主方法，用于测试
     */
    public static void main(String[] args) {
        log.info("开始创建BSC测试交易数据...");

        // 创建模拟交易数据
        List<TransactionModel> testTransactions = createMockBscTransactions();

        // 打印详细信息
        printTransactionDetails(testTransactions);

        // 验证数据完整性
        boolean isValid = validateTransactionData(testTransactions);
        log.info("数据验证结果: {}", isValid ? "通过" : "失败");

        // 输出使用示例
        log.info("=== 使用示例 ===");
        log.info("// 在您的测试类中使用:");
        log.info("List<TransactionModel> testData = BscTransactionTestRunner.createMockBscTransactions();");
        log.info("// 然后使用testData进行您的业务逻辑测试");
    }
}
