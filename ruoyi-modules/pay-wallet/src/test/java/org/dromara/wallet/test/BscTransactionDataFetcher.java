package org.dromara.wallet.test;

import lombok.extern.slf4j.Slf4j;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameter;
import org.web3j.protocol.core.methods.request.EthFilter;
import org.web3j.protocol.core.methods.response.EthLog;
import org.web3j.protocol.core.methods.response.Log;
import org.web3j.protocol.http.HttpService;
import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.chain.model.eth.EthTransactionModel;
import org.web3j.protocol.core.methods.response.EthBlock;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * BSC交易数据获取工具
 * 用于从BSC链获取真实的交易数据，生成测试用的TransactionModel
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Slf4j
public class BscTransactionDataFetcher {

    // BSC测试网RPC端点
    private static final String BSC_TESTNET_RPC = "https://capable-sleek-sponge.bsc-testnet.quiknode.pro/e0946ba9546e5a1119b2b904ed407c0e7447bc5b/";

    // Transfer事件签名
    private static final String TRANSFER_EVENT_TOPIC = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef";

    // 测试地址
    private static final String TEST_ADDRESS = "******************************************".toLowerCase();

    // USDT合约地址（BSC测试网）
    private static final String USDT_CONTRACT = "******************************************";

    // API单次请求的最大区块范围限制
    private static final int MAX_BLOCK_RANGE = 10000;

    private final Web3j web3j;

    public BscTransactionDataFetcher() {
        this.web3j = Web3j.build(new HttpService(BSC_TESTNET_RPC));
    }

    /**
     * 获取指定地址的最新交易记录
     *
     * @param address 目标地址
     * @param blockRange 扫描的区块范围
     * @return TransactionModel列表
     */
    public List<TransactionModel> fetchTransactionData(String address, int blockRange) {
        List<TransactionModel> transactionModels = new ArrayList<>();

        try {
            log.info("开始获取BSC链地址{}的交易数据，区块范围: {}", address, blockRange);

            // 获取最新区块号
            BigInteger latestBlock = web3j.ethBlockNumber().send().getBlockNumber();
            BigInteger startBlock = latestBlock.subtract(BigInteger.valueOf(blockRange));

            log.info("总扫描区块范围: {} - {}", startBlock, latestBlock);

            // 如果请求范围超过限制，则分批处理
            if (blockRange > MAX_BLOCK_RANGE) {
                log.info("区块范围{}超过API限制{}，将分批处理", blockRange, MAX_BLOCK_RANGE);
                transactionModels = fetchTransactionDataInBatches(address, startBlock, latestBlock);
            } else {
                // 单次请求
                transactionModels = fetchSingleBatch(address, startBlock, latestBlock);
            }

            log.info("共找到{}条与地址{}相关的交易", transactionModels.size(), address);

        } catch (Exception e) {
            log.error("获取交易数据失败", e);
        }

        return transactionModels;
    }

    /**
     * 分批获取交易数据
     *
     * @param address 目标地址
     * @param startBlock 起始区块
     * @param endBlock 结束区块
     * @return TransactionModel列表
     */
    private List<TransactionModel> fetchTransactionDataInBatches(String address, BigInteger startBlock, BigInteger endBlock) {
        List<TransactionModel> allTransactions = new ArrayList<>();

        BigInteger currentStart = startBlock;
        int batchCount = 0;

        while (currentStart.compareTo(endBlock) < 0) {
            batchCount++;

            // 计算当前批次的结束区块
            BigInteger currentEnd = currentStart.add(BigInteger.valueOf(MAX_BLOCK_RANGE - 1));
            if (currentEnd.compareTo(endBlock) > 0) {
                currentEnd = endBlock;
            }

            log.info("执行第{}批请求，区块范围: {} - {}", batchCount, currentStart, currentEnd);

            try {
                // 获取当前批次的交易数据
                List<TransactionModel> batchTransactions = fetchSingleBatch(address, currentStart, currentEnd);
                allTransactions.addAll(batchTransactions);

                log.info("第{}批请求完成，找到{}条相关交易", batchCount, batchTransactions.size());

                // 添加延迟以避免请求过于频繁
                if (currentStart.add(BigInteger.valueOf(MAX_BLOCK_RANGE)).compareTo(endBlock) < 0) {
                    Thread.sleep(100); // 100ms延迟
                }

            } catch (Exception e) {
                log.error("第{}批请求失败，区块范围: {} - {}", batchCount, currentStart, currentEnd, e);
                // 继续处理下一批，不中断整个流程
            }

            // 移动到下一批
            currentStart = currentEnd.add(BigInteger.ONE);
        }

        log.info("分批处理完成，总共执行{}批请求，累计找到{}条交易", batchCount, allTransactions.size());
        return allTransactions;
    }

    /**
     * 单批次获取交易数据
     *
     * @param address 目标地址
     * @param fromBlock 起始区块
     * @param toBlock 结束区块
     * @return TransactionModel列表
     */
    private List<TransactionModel> fetchSingleBatch(String address, BigInteger fromBlock, BigInteger toBlock) throws Exception {
        List<TransactionModel> transactionModels = new ArrayList<>();

        // 创建过滤器，监听Transfer事件
        EthFilter filter = new EthFilter(
            DefaultBlockParameter.valueOf(fromBlock),
            DefaultBlockParameter.valueOf(toBlock),
            List.of(USDT_CONTRACT) // 监听USDT合约
        );
        filter.addSingleTopic(TRANSFER_EVENT_TOPIC);

        // 获取日志
        EthLog ethLog = web3j.ethGetLogs(filter).send();

        if (ethLog.hasError()) {
            throw new RuntimeException("获取日志失败: " + ethLog.getError().getMessage());
        }

        log.debug("区块范围 {} - {} 找到{}条Transfer事件日志", fromBlock, toBlock, ethLog.getLogs().size());

        // 处理每个日志
        for (EthLog.LogResult<?> logResult : ethLog.getLogs()) {
            if (logResult instanceof EthLog.LogObject) {
                Log logDetail = ((EthLog.LogObject) logResult).get();

                // 检查是否涉及目标地址
                if (isAddressInvolved(logDetail, address)) {
                    TransactionModel transactionModel = createTransactionModel(logDetail);
                    if (transactionModel != null) {
                        transactionModels.add(transactionModel);
                        log.debug("找到相关交易: {}", logDetail.getTransactionHash());
                    }
                }
            }
        }

        return transactionModels;
    }

    /**
     * 检查日志是否涉及指定地址
     */
    private boolean isAddressInvolved(Log log, String address) {
        if (log.getTopics() == null || log.getTopics().size() < 3) {
            return false;
        }

        // Transfer事件的topic[1]是from地址，topic[2]是to地址
        String fromTopic = log.getTopics().get(1);
        String toTopic = log.getTopics().get(2);

        // 地址在topic中是32字节，需要去掉前面的0x和前24个0
        String normalizedAddress = "0x" + address.substring(2).toLowerCase();
        String fromAddress = "0x" + fromTopic.substring(26).toLowerCase();
        String toAddress = "0x" + toTopic.substring(26).toLowerCase();

        return normalizedAddress.equals(fromAddress) || normalizedAddress.equals(toAddress);
    }

    /**
     * 从日志创建TransactionModel
     */
    private TransactionModel createTransactionModel(Log logDetail) {
        try {
            // 获取区块信息
            EthBlock ethBlock = web3j.ethGetBlockByNumber(
                DefaultBlockParameter.valueOf(logDetail.getBlockNumber()), false
            ).send();

            if (ethBlock.getBlock() == null) {
                log.warn("无法获取区块信息: {}", logDetail.getBlockNumber());
                return null;
            }

            // 创建EthTransactionModel
            EthTransactionModel ethTransactionModel = EthTransactionModel.builder()
                .setEthBlock(ethBlock)
                .setLog(logDetail);

            // 创建TransactionModel
            TransactionModel transactionModel = TransactionModel.builder()
                .setEthTransactionModel(ethTransactionModel)
                .setChainType("BSC");

            return transactionModel;

        } catch (Exception e) {
            log.error("创建TransactionModel失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 打印交易详细信息
     */
    public void printTransactionDetails(List<TransactionModel> transactions) {
        log.info("=== BSC交易数据详情 ===");

        for (int i = 0; i < transactions.size(); i++) {
            TransactionModel tx = transactions.get(i);
            EthTransactionModel ethTx = tx.getEthTransactionModel();
            Log txLog = ethTx.getLog();

            log.info("交易 #{}", i + 1);
            log.info("  交易哈希: {}", txLog.getTransactionHash());
            log.info("  区块号: {}", txLog.getBlockNumber());
            log.info("  区块哈希: {}", txLog.getBlockHash());
            log.info("  合约地址: {}", txLog.getAddress());
            log.info("  交易索引: {}", txLog.getTransactionIndex());
            log.info("  日志索引: {}", txLog.getLogIndex());

            if (txLog.getTopics() != null && txLog.getTopics().size() >= 3) {
                String fromTopic = txLog.getTopics().get(1);
                String toTopic = txLog.getTopics().get(2);
                String fromAddress = "0x" + fromTopic.substring(26);
                String toAddress = "0x" + toTopic.substring(26);

                log.info("  发送方: {}", fromAddress);
                log.info("  接收方: {}", toAddress);
            }

            if (txLog.getData() != null && !txLog.getData().equals("0x")) {
                log.info("  转账金额(原始): {}", txLog.getData());
                // 解析金额（USDT是18位精度）
                try {
                    BigInteger amount = new BigInteger(txLog.getData().substring(2), 16);
                    log.info("  转账金额(wei): {}", amount);
                } catch (Exception e) {
                    log.warn("  无法解析转账金额: {}", e.getMessage());
                }
            }

            log.info("  区块时间戳: {}", ethTx.getEthBlock().getBlock().getTimestamp());
            log.info("  链类型: {}", tx.getChainType());
            log.info("  ---");
        }
    }

    /**
     * 主方法，用于测试
     */
    public static void main(String[] args) {
        BscTransactionDataFetcher fetcher = new BscTransactionDataFetcher();

        // 获取最近1000个区块的交易数据
        List<TransactionModel> transactions = fetcher.fetchTransactionData(TEST_ADDRESS, 100000);

        // 打印详细信息
        fetcher.printTransactionDetails(transactions);

        // 输出可用于测试的代码片段
        fetcher.generateTestCode(transactions);
    }

    /**
     * 生成测试代码片段
     */
    public void generateTestCode(List<TransactionModel> transactions) {
        if (transactions.isEmpty()) {
            log.info("没有找到交易数据，无法生成测试代码");
            return;
        }

        log.info("=== 生成的测试代码片段 ===");
        log.info("// 基于真实BSC交易数据的测试用例");
        log.info("List<TransactionModel> testTransactions = new ArrayList<>();");

        for (int i = 0; i < Math.min(transactions.size(), 3); i++) { // 只生成前3个
            TransactionModel tx = transactions.get(i);
            EthTransactionModel ethTx = tx.getEthTransactionModel();
            Log txLog = ethTx.getLog();

            log.info("");
            log.info("// 交易 #{}", i + 1);
            log.info("TransactionModel tx{} = TransactionModel.builder()", i + 1);
            log.info("    .setChainType(\"BSC\")");
            log.info("    .setEthTransactionModel(EthTransactionModel.builder()");
            log.info("        .setLog(createMockLog(");
            log.info("            \"{}\", // transactionHash", txLog.getTransactionHash());
            log.info("            BigInteger.valueOf({}L), // blockNumber", txLog.getBlockNumber());
            log.info("            \"{}\", // blockHash", txLog.getBlockHash());
            log.info("            \"{}\", // contractAddress", txLog.getAddress());
            log.info("            BigInteger.valueOf({}L) // transactionIndex", txLog.getTransactionIndex());
            log.info("        ))");
            log.info("        .build());");
            log.info("testTransactions.add(tx{});", i + 1);
        }

        log.info("");
        log.info("// 使用测试数据");
        log.info("for (TransactionModel tx : testTransactions) {");
        log.info("    // 执行您的测试逻辑");
        log.info("    processTransaction(tx);");
        log.info("}");
    }

    /**
     * 关闭资源
     */
    public void close() {
        if (web3j != null) {
            web3j.shutdown();
        }
    }
}
