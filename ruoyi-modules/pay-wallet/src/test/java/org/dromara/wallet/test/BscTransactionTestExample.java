package org.dromara.wallet.test;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.chain.model.eth.EthTransactionModel;
import org.web3j.protocol.core.methods.response.Log;

import java.math.BigInteger;
import java.util.List;

/**
 * BSC交易测试示例
 * 演示如何使用模拟的TransactionModel数据进行测试
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Slf4j
public class BscTransactionTestExample {

    /**
     * 主测试方法
     */
    public static void main(String[] args) {
        log.info("=== BSC交易模型测试示例 ===");

        // 1. 创建模拟的BSC交易数据
        List<TransactionModel> testTransactions = BscTransactionTestRunner.createMockBscTransactions();

        // 2. 验证数据完整性
        boolean isValid = BscTransactionTestRunner.validateTransactionData(testTransactions);
        if (!isValid) {
            log.error("测试数据验证失败，退出测试");
            return;
        }

        // 3. 打印交易详细信息
        BscTransactionTestRunner.printTransactionDetails(testTransactions);

        // 4. 演示如何处理每个交易
        processTransactions(testTransactions);

        // 5. 演示如何提取特定信息
        extractTransactionInfo(testTransactions);

        log.info("=== 测试完成 ===");
    }

    /**
     * 处理交易列表的示例
     */
    private static void processTransactions(List<TransactionModel> transactions) {
        log.info("=== 开始处理交易 ===");

        for (int i = 0; i < transactions.size(); i++) {
            TransactionModel tx = transactions.get(i);
            log.info("处理交易 #{}", i + 1);

            // 检查链类型
            if (!"BSC".equals(tx.getChainType())) {
                log.warn("跳过非BSC交易: {}", tx.getChainType());
                continue;
            }

            // 获取EthTransactionModel
            EthTransactionModel ethTx = tx.getEthTransactionModel();
            if (ethTx == null) {
                log.warn("交易 #{} 缺少EthTransactionModel", i + 1);
                continue;
            }

            // 获取Log信息
            Log txLog = ethTx.getLog();
            if (txLog == null) {
                log.warn("交易 #{} 缺少Log信息", i + 1);
                continue;
            }

            // 处理交易逻辑
            processTransaction(tx, txLog);
        }

        log.info("交易处理完成，共处理{}笔交易", transactions.size());
    }

    /**
     * 处理单个交易的示例
     */
    private static void processTransaction(TransactionModel transaction, Log logDetail) {
        String txHash = logDetail.getTransactionHash();
        BigInteger blockNumber = logDetail.getBlockNumber();
        String contractAddress = logDetail.getAddress();

        log.info("  交易哈希: {}", txHash);
        log.info("  区块号: {}", blockNumber);
        log.info("  合约地址: {}", contractAddress);

        // 判断是否为原生代币转账
        if ("******************************************".equals(contractAddress)) {
            log.info("  类型: BNB原生转账");
        } else {
            log.info("  类型: 代币转账");
        }

        // 解析Transfer事件
        if (logDetail.getTopics() != null && logDetail.getTopics().size() >= 3) {
            String fromTopic = logDetail.getTopics().get(1);
            String toTopic = logDetail.getTopics().get(2);

            // 提取地址（去掉前面的0填充）
            String fromAddress = "0x" + fromTopic.substring(26);
            String toAddress = "0x" + toTopic.substring(26);

            log.info("  发送方: {}", fromAddress);
            log.info("  接收方: {}", toAddress);

            // 检查是否涉及目标地址
            String targetAddress = "******************************************";
            if (targetAddress.equalsIgnoreCase(fromAddress)) {
                log.info("  ✓ 目标地址作为发送方");
            } else if (targetAddress.equalsIgnoreCase(toAddress)) {
                log.info("  ✓ 目标地址作为接收方");
            }
        }

        // 解析转账金额
        if (logDetail.getData() != null && !logDetail.getData().equals("0x")) {
            try {
                BigInteger amount = new BigInteger(logDetail.getData().substring(2), 16);
                log.info("  转账金额: {} wei", amount);

                // 转换为可读格式（假设18位精度）
                double readableAmount = amount.doubleValue() / Math.pow(10, 18);
                log.info("  转账金额: {} 代币", readableAmount);
            } catch (Exception e) {
                log.warn("  无法解析转账金额: {}", e.getMessage());
            }
        }

        log.info("  ---");
    }

    /**
     * 提取交易信息的示例
     */
    private static void extractTransactionInfo(List<TransactionModel> transactions) {
        log.info("=== 交易信息统计 ===");

        int totalTransactions = transactions.size();
        int usdtTransactions = 0;
        int bnbTransactions = 0;
        int incomingTransactions = 0;
        int outgoingTransactions = 0;

        String targetAddress = "******************************************";

        for (TransactionModel tx : transactions) {
            if (tx.getEthTransactionModel() == null || tx.getEthTransactionModel().getLog() == null) {
                continue;
            }

            Log log = tx.getEthTransactionModel().getLog();
            String contractAddress = log.getAddress();

            // 统计代币类型
            if ("******************************************".equals(contractAddress)) {
                bnbTransactions++;
            } else if ("******************************************".equalsIgnoreCase(contractAddress)) {
                usdtTransactions++;
            }

            // 统计交易方向
            if (log.getTopics() != null && log.getTopics().size() >= 3) {
                String fromTopic = log.getTopics().get(1);
                String toTopic = log.getTopics().get(2);
                String fromAddress = "0x" + fromTopic.substring(26);
                String toAddress = "0x" + toTopic.substring(26);

                if (targetAddress.equalsIgnoreCase(fromAddress)) {
                    outgoingTransactions++;
                } else if (targetAddress.equalsIgnoreCase(toAddress)) {
                    incomingTransactions++;
                }
            }
        }

        log.info("总交易数: {}", totalTransactions);
        log.info("USDT交易: {}", usdtTransactions);
        log.info("BNB交易: {}", bnbTransactions);
        log.info("接收交易: {}", incomingTransactions);
        log.info("发送交易: {}", outgoingTransactions);

        // 计算百分比
        if (totalTransactions > 0) {
            log.info("USDT交易占比: {:.1f}%", (usdtTransactions * 100.0 / totalTransactions));
            log.info("BNB交易占比: {:.1f}%", (bnbTransactions * 100.0 / totalTransactions));
            log.info("接收交易占比: {:.1f}%", (incomingTransactions * 100.0 / totalTransactions));
            log.info("发送交易占比: {:.1f}%", (outgoingTransactions * 100.0 / totalTransactions));
        }
    }

    /**
     * 演示如何在业务代码中使用这些测试数据
     */
    public static void demonstrateBusinessUsage() {
        log.info("=== 业务代码使用示例 ===");

        // 获取测试数据
        List<TransactionModel> testData = BscTransactionTestRunner.createMockBscTransactions();

        // 模拟业务处理
        for (TransactionModel tx : testData) {
            // 这里可以调用您的实际业务方法
            // 例如：
            // evmTransactionManager.processTransactions(Arrays.asList(tx));
            // 或者：
            // walletService.handleTransaction(tx);

            log.info("处理交易: {}", tx.getEthTransactionModel().getLog().getTransactionHash());
        }

        log.info("业务处理示例完成");
    }
}
