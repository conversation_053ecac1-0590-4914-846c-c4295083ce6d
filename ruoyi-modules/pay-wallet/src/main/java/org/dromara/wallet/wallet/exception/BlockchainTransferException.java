package org.dromara.wallet.wallet.exception;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 区块链转账统一异常类
 *
 * <p>用于封装所有区块链转账相关的异常情况，提供统一的异常处理机制。</p>
 * <p>利用Java原生的异常链机制保留完整的原始异常信息。</p>
 *
 * <p>主要特性：</p>
 * <ul>
 *   <li>统一的异常接口，支持所有区块链（TRON、EVM、Solana等）</li>
 *   <li>保留完整的业务上下文信息（手续费使用情况、尝试金额等）</li>
 *   <li>通过errorCode支持差异化的异常处理策略</li>
 *   <li>利用Java异常链机制保留原始异常的完整信息</li>
 *   <li>提供静态工厂方法简化异常创建</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
public class BlockchainTransferException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 区块链名称（如：TRON、BSC、ETH、ARB、BASE、SOLANA）
     */
    private final String chainName;

    /**
     * 错误码，用于分类处理不同类型的异常
     */
    private final String errorCode;

    /**
     * 是否使用了手续费钱包
     */
    private final boolean feeWalletUsed;

    /**
     * 尝试提供的手续费金额
     */
    private final BigDecimal feeAttempted;

    // ==================== 构造函数 ====================

    /**
     * 基础构造函数
     *
     * @param message   错误消息
     * @param chainName 区块链名称
     * @param errorCode 错误码
     */
    public BlockchainTransferException(String message, String chainName, String errorCode) {
        super(message);
        this.chainName = chainName;
        this.errorCode = errorCode;
        this.feeWalletUsed = false;
        this.feeAttempted = BigDecimal.ZERO;
    }

    /**
     * 带原始异常的构造函数
     * 利用Java原生异常链机制保留完整的原始异常信息
     *
     * @param message   错误消息
     * @param chainName 区块链名称
     * @param errorCode 错误码
     * @param cause     原始异常
     */
    public BlockchainTransferException(String message, String chainName, String errorCode, Throwable cause) {
        super(message, cause);  // 原始异常自动保存在cause中
        this.chainName = chainName;
        this.errorCode = errorCode;
        this.feeWalletUsed = false;
        this.feeAttempted = BigDecimal.ZERO;
    }

    /**
     * 包含完整业务上下文的构造函数
     *
     * @param message       错误消息
     * @param chainName     区块链名称
     * @param errorCode     错误码
     * @param feeWalletUsed 是否使用了手续费钱包
     * @param feeAttempted  尝试提供的手续费金额
     * @param cause         原始异常
     */
    public BlockchainTransferException(String message, String chainName, String errorCode,
                                     boolean feeWalletUsed, BigDecimal feeAttempted, Throwable cause) {
        super(message, cause);
        this.chainName = chainName;
        this.errorCode = errorCode;
        this.feeWalletUsed = feeWalletUsed;
        this.feeAttempted = feeAttempted != null ? feeAttempted : BigDecimal.ZERO;
    }

    // ==================== 静态工厂方法 ====================

    /**
     * 包装现有异常的静态工厂方法
     *
     * @param chainName         区块链名称
     * @param errorCode         错误码
     * @param originalException 原始异常
     * @return BlockchainTransferException实例
     */
    public static BlockchainTransferException wrap(String chainName, String errorCode, Throwable originalException) {
        return new BlockchainTransferException(
            chainName + "转账失败: " + originalException.getMessage(),
            chainName,
            errorCode,
            originalException
        );
    }

    /**
     * 包装现有异常并包含业务上下文的静态工厂方法
     *
     * @param chainName         区块链名称
     * @param errorCode         错误码
     * @param feeWalletUsed     是否使用了手续费钱包
     * @param feeAttempted      尝试提供的手续费金额
     * @param originalException 原始异常
     * @return BlockchainTransferException实例
     */
    public static BlockchainTransferException wrapWithContext(String chainName, String errorCode,
                                                            boolean feeWalletUsed, BigDecimal feeAttempted,
                                                            Throwable originalException) {
        return new BlockchainTransferException(
            chainName + "转账失败: " + originalException.getMessage(),
            chainName,
            errorCode,
            feeWalletUsed,
            feeAttempted,
            originalException
        );
    }

    // ==================== 常用异常类型的静态工厂方法 ====================

    /**
     * 余额不足异常
     *
     * @param chainName    区块链名称
     * @param attemptedAmount 尝试转账的金额
     * @return BlockchainTransferException实例
     */
    public static BlockchainTransferException insufficientBalance(String chainName, BigDecimal attemptedAmount) {
        return new BlockchainTransferException(
            "余额不足，无法完成转账，尝试金额: " + attemptedAmount,
            chainName,
            "INSUFFICIENT_BALANCE"
        );
    }

    /**
     * 手续费钱包操作失败异常
     *
     * @param chainName    区块链名称
     * @param feeAttempted 尝试提供的手续费金额
     * @param cause        原始异常
     * @return BlockchainTransferException实例
     */
    public static BlockchainTransferException feeWalletFailed(String chainName, BigDecimal feeAttempted, Throwable cause) {
        return new BlockchainTransferException(
            "手续费钱包操作失败: " + (cause != null ? cause.getMessage() : "未知错误"),
            chainName,
            "FEE_WALLET_FAILED",
            true,
            feeAttempted,
            cause
        );
    }

    /**
     * 转账验证失败异常
     *
     * @param chainName       区块链名称
     * @param transactionHash 交易哈希
     * @return BlockchainTransferException实例
     */
    public static BlockchainTransferException verificationFailed(String chainName, String transactionHash) {
        return new BlockchainTransferException(
            "转账验证失败，交易可能未确认，交易哈希: " + transactionHash,
            chainName,
            "VERIFICATION_FAILED"
        );
    }

    /**
     * 网络超时异常
     *
     * @param chainName 区块链名称
     * @param cause     原始异常
     * @return BlockchainTransferException实例
     */
    public static BlockchainTransferException networkTimeout(String chainName, Throwable cause) {
        return new BlockchainTransferException(
            "网络超时，请稍后重试",
            chainName,
            "NETWORK_TIMEOUT",
            cause
        );
    }

    /**
     * 不支持的代币异常
     *
     * @param chainName   区块链名称
     * @param tokenSymbol 代币符号
     * @return BlockchainTransferException实例
     */
    public static BlockchainTransferException unsupportedToken(String chainName, String tokenSymbol) {
        return new BlockchainTransferException(
            chainName + "链不支持代币: " + tokenSymbol,
            chainName,
            "UNSUPPORTED_TOKEN"
        );
    }

    /**
     * 配置错误异常
     *
     * @param chainName 区块链名称
     * @param configKey 配置项名称
     * @return BlockchainTransferException实例
     */
    public static BlockchainTransferException configurationError(String chainName, String configKey) {
        return new BlockchainTransferException(
            chainName + "链配置错误，缺少配置项: " + configKey,
            chainName,
            "CONFIGURATION_ERROR"
        );
    }

    /**
     * 创建参数验证错误异常
     *
     * @param chainName 区块链名称
     * @param message   错误消息
     * @return BlockchainTransferException实例
     */
    public static BlockchainTransferException validationError(String chainName, String message) {
        return new BlockchainTransferException(
            message,
            chainName,
            "VALIDATION_ERROR"
        );
    }

    // ==================== Getter方法 ====================

    /**
     * 获取区块链名称
     *
     * @return 区块链名称
     */
    public String getChainName() {
        return chainName;
    }

    /**
     * 获取错误码
     *
     * @return 错误码
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 是否使用了手续费钱包
     *
     * @return true如果使用了手续费钱包
     */
    public boolean isFeeWalletUsed() {
        return feeWalletUsed;
    }

    /**
     * 获取尝试提供的手续费金额
     *
     * @return 手续费金额
     */
    public BigDecimal getFeeAttempted() {
        return feeAttempted;
    }

    /**
     * 获取原始异常
     * 这是Java Exception类的标准方法，用于获取异常链中的原始异常
     *
     * @return 原始异常，如果没有则返回null
     */
    @Override
    public Throwable getCause() {
        return super.getCause();
    }

    // ==================== 重写方法 ====================

    /**
     * 重写toString方法，提供更详细的异常信息
     *
     * @return 格式化的异常信息字符串
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("BlockchainTransferException{");
        sb.append("chainName='").append(chainName).append('\'');
        sb.append(", errorCode='").append(errorCode).append('\'');
        sb.append(", feeWalletUsed=").append(feeWalletUsed);
        sb.append(", feeAttempted=").append(feeAttempted);
        sb.append(", message='").append(getMessage()).append('\'');

        Throwable cause = getCause();
        if (cause != null) {
            sb.append(", cause=").append(cause.getClass().getSimpleName());
            sb.append("('").append(cause.getMessage()).append("')");
        }

        sb.append('}');
        return sb.toString();
    }
}
