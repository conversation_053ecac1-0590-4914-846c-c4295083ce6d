package org.dromara.wallet.wallet.exception;

import org.dromara.common.core.exception.base.BaseException;

import java.io.Serial;

/**
 * 钱包模块基础异常类
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
public class WalletException extends BaseException {

    @Serial
    private static final long serialVersionUID = 1L;

    public WalletException(String code, Object... args) {
        super("wallet", code, args, null);
    }

    public WalletException(String defaultMessage) {
        super("wallet", defaultMessage);
    }

    public WalletException(String code, Object[] args, String defaultMessage) {
        super("wallet", code, args, defaultMessage);
    }
}
