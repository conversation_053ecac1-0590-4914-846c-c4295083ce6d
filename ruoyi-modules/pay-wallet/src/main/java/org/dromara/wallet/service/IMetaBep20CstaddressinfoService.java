package org.dromara.wallet.service;

import org.dromara.wallet.domain.MetaBep20Cstaddressinfo;
import org.dromara.wallet.domain.vo.MetaBep20CstaddressinfoVo;
import org.dromara.wallet.domain.bo.MetaBep20CstaddressinfoBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;

/**
 * ETH客户热钱包地址信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface IMetaBep20CstaddressinfoService {

    MetaBep20Cstaddressinfo queryByAddress(String address);

    /**
     * 查询ETH客户热钱包地址信息
     *
     * @param id 主键
     * @return ETH客户热钱包地址信息
     */
    MetaBep20CstaddressinfoVo queryById(Long id);

    /**
     * 分页查询ETH客户热钱包地址信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return ETH客户热钱包地址信息分页列表
     */
    TableDataInfo<MetaBep20CstaddressinfoVo> queryPageList(MetaBep20CstaddressinfoBo bo, PageQuery pageQuery);

    HashSet<String> queryAllAddressSet();

    /**
     * 查询符合条件的ETH客户热钱包地址信息列表
     *
     * @param bo 查询条件
     * @return ETH客户热钱包地址信息列表
     */
    List<MetaBep20CstaddressinfoVo> queryList(MetaBep20CstaddressinfoBo bo);

    /**
     * 新增ETH客户热钱包地址信息
     *
     * @param bo ETH客户热钱包地址信息
     * @return 是否新增成功
     */
    Boolean insertByBo(MetaBep20CstaddressinfoBo bo);

    /**
     * 修改ETH客户热钱包地址信息
     *
     * @param bo ETH客户热钱包地址信息
     * @return 是否修改成功
     */
    Boolean updateByBo(MetaBep20CstaddressinfoBo bo);

    /**
     * 校验并批量删除ETH客户热钱包地址信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
