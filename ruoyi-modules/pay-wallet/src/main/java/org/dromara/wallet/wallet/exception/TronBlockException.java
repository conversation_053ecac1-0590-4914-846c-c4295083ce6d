package org.dromara.wallet.wallet.exception;

import java.io.Serial;

/**
 * TRON区块相关异常类
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
public class TronBlockException extends TronException {

    @Serial
    private static final long serialVersionUID = 1L;

    public TronBlockException(String message) {
        super(message);
    }

    public TronBlockException(String code, Object... args) {
        super("block." + code, args);
    }

    /**
     * 获取交易结果失败异常
     */
    public static TronBlockException getTransactionFailed(String txid) {
        return new TronBlockException(String.format("获取交易结果失败: txid=%s", txid));
    }

    /**
     * 交易失败异常
     */
    public static TronBlockException transactionFailed(String txid, String result) {
        return new TronBlockException(String.format("交易失败: txid=%s, result=%s", txid, result));
    }

    /**
     * 查询交易失败异常
     */
    public static TronBlockException queryTransactionFailed(String txid) {
        return new TronBlockException(String.format("查询交易失败，交易API报错，txid=%s", txid));
    }

    /**
     * 区块获取失败异常
     */
    public static TronBlockException getBlockFailed(String blockId) {
        return new TronBlockException(String.format("获取区块失败: blockId=%s", blockId));
    }

    /**
     * 获取账户资源失败异常
     */
    public static TronBlockException getAccountResourceFailed(String reason) {
        return new TronBlockException("获取账户资源失败: " + reason);
    }

    /**
     * 获取账户信息失败异常
     */
    public static TronBlockException getAccountFailed(String reason) {
        return new TronBlockException("获取账户信息失败: " + reason);
    }

    /**
     * 查询TRX余额失败异常
     */
    public static TronBlockException getTrxBalanceFailed(String reason) {
        return new TronBlockException("查询TRX余额失败: " + reason);
    }

    /**
     * 查询代币余额失败异常
     */
    public static TronBlockException getTokenBalanceFailed(String reason) {
        return new TronBlockException("查询代币余额失败: " + reason);
    }
}
