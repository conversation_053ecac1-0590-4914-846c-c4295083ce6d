package org.dromara.wallet.service;

import org.dromara.wallet.domain.MetaTransferRec;
import org.dromara.wallet.domain.vo.MetaTransferRecVo;
import org.dromara.wallet.domain.bo.MetaTransferRecBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 转账记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface IMetaTransferRecService {

    MetaTransferRecVo queryByOriginalId(Long originalId);

    /**
     * 查询转账记录
     *
     * @param id 主键
     * @return 转账记录
     */
    MetaTransferRecVo queryById(Long id);

    /**
     * 分页查询转账记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 转账记录分页列表
     */
    TableDataInfo<MetaTransferRecVo> queryPageList(MetaTransferRecBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的转账记录列表
     *
     * @param bo 查询条件
     * @return 转账记录列表
     */
    List<MetaTransferRecVo> queryList(MetaTransferRecBo bo);

    /**
     * 新增转账记录
     *
     * @param bo 转账记录
     * @return 是否新增成功
     */
    Boolean insertByBo(MetaTransferRecBo bo);

    /**
     * 修改转账记录
     *
     * @param bo 转账记录
     * @return 是否修改成功
     */
    Boolean updateByBo(MetaTransferRecBo bo);

    /**
     * 校验并批量删除转账记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
