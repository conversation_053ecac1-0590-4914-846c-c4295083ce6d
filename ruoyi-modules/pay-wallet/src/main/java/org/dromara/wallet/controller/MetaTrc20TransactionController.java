package org.dromara.wallet.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.wallet.domain.vo.MetaTrc20TransactionVo;
import org.dromara.wallet.domain.bo.MetaTrc20TransactionBo;
import org.dromara.wallet.service.IMetaTrc20TransactionService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * TRON区块高度交易明细
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/wallet/trc20Transaction")
public class MetaTrc20TransactionController extends BaseController {

    private final IMetaTrc20TransactionService metaTrc20TransactionService;

    /**
     * 查询TRON区块高度交易明细列表
     */
    @SaCheckPermission("wallet:trc20Transaction:list")
    @GetMapping("/list")
    public TableDataInfo<MetaTrc20TransactionVo> list(MetaTrc20TransactionBo bo, PageQuery pageQuery) {
        return metaTrc20TransactionService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出TRON区块高度交易明细列表
     */
    @SaCheckPermission("wallet:trc20Transaction:export")
    @Log(title = "TRON区块高度交易明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MetaTrc20TransactionBo bo, HttpServletResponse response) {
        List<MetaTrc20TransactionVo> list = metaTrc20TransactionService.queryList(bo);
        ExcelUtil.exportExcel(list, "TRON区块高度交易明细", MetaTrc20TransactionVo.class, response);
    }

    /**
     * 获取TRON区块高度交易明细详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("wallet:trc20Transaction:query")
    @GetMapping("/{id}")
    public R<MetaTrc20TransactionVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(metaTrc20TransactionService.queryById(id));
    }

    /**
     * 新增TRON区块高度交易明细
     */
    @SaCheckPermission("wallet:trc20Transaction:add")
    @Log(title = "TRON区块高度交易明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MetaTrc20TransactionBo bo) {
        return toAjax(metaTrc20TransactionService.insertByBo(bo));
    }

    /**
     * 修改TRON区块高度交易明细
     */
    @SaCheckPermission("wallet:trc20Transaction:edit")
    @Log(title = "TRON区块高度交易明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MetaTrc20TransactionBo bo) {
        return toAjax(metaTrc20TransactionService.updateByBo(bo));
    }

    /**
     * 删除TRON区块高度交易明细
     *
     * @param ids 主键串
     */
    @SaCheckPermission("wallet:trc20Transaction:remove")
    @Log(title = "TRON区块高度交易明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(metaTrc20TransactionService.deleteWithValidByIds(List.of(ids), true));
    }
}
