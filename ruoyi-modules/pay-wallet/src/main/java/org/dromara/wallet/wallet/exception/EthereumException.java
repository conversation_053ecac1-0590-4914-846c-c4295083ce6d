package org.dromara.wallet.wallet.exception;

import java.io.Serial;

/**
 * 以太坊链异常类
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
public class EthereumException extends WalletException {

    @Serial
    private static final long serialVersionUID = 1L;

    public EthereumException(String code, Object... args) {
        super("wallet.ethereum." + code, args);
    }

    public EthereumException(String defaultMessage) {
        super(defaultMessage);
    }

    public EthereumException(String code, Object[] args, String defaultMessage) {
        super("wallet.ethereum." + code, args, defaultMessage);
    }

    /**
     * Gas费不足异常
     */
    public static EthereumException insufficientGas() {
        return new EthereumException("Gas费不足，无法完成交易");
    }

    /**
     * 交易失败异常
     */
    public static EthereumException transactionFailed(String reason) {
        return new EthereumException("以太坊交易失败: " + reason);
    }

    /**
     * 网络连接异常
     */
    public static EthereumException networkError(String reason) {
        return new EthereumException("以太坊网络连接异常: " + reason);
    }
}
