package org.dromara.wallet.service;

import org.dromara.wallet.domain.vo.MetaTrc20TransactionVo;
import org.dromara.wallet.domain.bo.MetaTrc20TransactionBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * TRON区块高度交易明细Service接口
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface IMetaTrc20TransactionService {

    /**
     * 查询TRON区块高度交易明细
     *
     * @param id 主键
     * @return TRON区块高度交易明细
     */
    MetaTrc20TransactionVo queryById(Long id);

    /**
     * 分页查询TRON区块高度交易明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return TRON区块高度交易明细分页列表
     */
    TableDataInfo<MetaTrc20TransactionVo> queryPageList(MetaTrc20TransactionBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的TRON区块高度交易明细列表
     *
     * @param bo 查询条件
     * @return TRON区块高度交易明细列表
     */
    List<MetaTrc20TransactionVo> queryList(MetaTrc20TransactionBo bo);

    /**
     * 新增TRON区块高度交易明细
     *
     * @param bo TRON区块高度交易明细
     * @return 是否新增成功
     */
    Boolean insertByBo(MetaTrc20TransactionBo bo);

    /**
     * 修改TRON区块高度交易明细
     *
     * @param bo TRON区块高度交易明细
     * @return 是否修改成功
     */
    Boolean updateByBo(MetaTrc20TransactionBo bo);

    /**
     * 校验并批量删除TRON区块高度交易明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
