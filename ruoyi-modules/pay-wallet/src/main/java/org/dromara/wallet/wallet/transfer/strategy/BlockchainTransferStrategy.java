package org.dromara.wallet.wallet.transfer.strategy;

import org.dromara.wallet.wallet.transfer.dto.TransferRequest;
import org.dromara.wallet.wallet.transfer.dto.BlockchainTransferResult;

/**
 * 区块链转账策略接口
 * 
 * <p>定义了统一的转账策略接口，每个区块链实现自己的转账逻辑</p>
 * <p>采用策略模式，便于扩展新的区块链支持</p>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
public interface BlockchainTransferStrategy {

    /**
     * 获取策略支持的区块链名称
     * 
     * @return 区块链名称，如 "TRON"、"BSC"、"SOLANA"
     */
    String getChainName();

    /**
     * 检查是否支持指定的区块链
     * 
     * @param chainName 区块链名称
     * @return 是否支持
     */
    boolean supports(String chainName);

    /**
     * 执行转账操作
     * 
     * <p>这是核心方法，实现具体的转账逻辑：</p>
     * <ul>
     *   <li>参数验证和预处理</li>
     *   <li>手续费估算和提供</li>
     *   <li>交易构建和发送</li>
     *   <li>交易确认和验证</li>
     *   <li>结果封装和返回</li>
     * </ul>
     * 
     * @param request 转账请求
     * @return 转账结果
     */
    BlockchainTransferResult execute(TransferRequest request);

    /**
     * 获取策略的优先级
     * 数值越小优先级越高，用于处理多个策略支持同一链的情况
     * 
     * @return 优先级数值，默认为100
     */
    default int getPriority() {
        return 100;
    }

    /**
     * 检查策略是否可用
     * 可以检查配置、网络连接等状态
     * 
     * @return 是否可用
     */
    default boolean isAvailable() {
        return true;
    }

    /**
     * 获取策略描述信息
     * 
     * @return 策略描述
     */
    default String getDescription() {
        return getChainName() + " 转账策略";
    }
}
