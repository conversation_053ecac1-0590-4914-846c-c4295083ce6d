package org.dromara.wallet.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.wallet.domain.MetaSolanaTransaction;
import org.dromara.wallet.domain.bo.MetaSolanaTransactionBo;
import org.dromara.wallet.domain.vo.MetaSolanaTransactionVo;
import org.dromara.wallet.mapper.MetaSolanaTransactionsMapper;
import org.dromara.wallet.service.IMetaSolanaTransactionsService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Solana区块高度交易明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@RequiredArgsConstructor
@Service
public class MetaSolanaTransactionsServiceImpl implements IMetaSolanaTransactionsService {

    private final MetaSolanaTransactionsMapper baseMapper;

    /**
     * 查询Solana区块高度交易明细
     *
     * @param id 主键
     * @return Solana区块高度交易明细
     */
    @Override
    public MetaSolanaTransactionVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public Boolean isExist(String signature) {
        LambdaQueryWrapper<MetaSolanaTransaction> wrapper = new LambdaQueryWrapper<MetaSolanaTransaction>()
            .eq(MetaSolanaTransaction::getTxid, signature);
        return baseMapper.exists(wrapper);
    }

    /**
     * 分页查询Solana区块高度交易明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return Solana区块高度交易明细分页列表
     */
    @Override
    public TableDataInfo<MetaSolanaTransactionVo> queryPageList(MetaSolanaTransactionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MetaSolanaTransaction> lqw = buildQueryWrapper(bo);
        Page<MetaSolanaTransactionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的Solana区块高度交易明细列表
     *
     * @param bo 查询条件
     * @return Solana区块高度交易明细列表
     */
    @Override
    public List<MetaSolanaTransactionVo> queryList(MetaSolanaTransactionBo bo) {
        LambdaQueryWrapper<MetaSolanaTransaction> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MetaSolanaTransaction> buildQueryWrapper(MetaSolanaTransactionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MetaSolanaTransaction> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getTxid()), MetaSolanaTransaction::getTxid, bo.getTxid());
        lqw.eq(bo.getBlockheight() != null, MetaSolanaTransaction::getBlockheight, bo.getBlockheight());

        // 处理地址查询 - 支持单个地址查询和多地址查询
        if (StringUtils.isNotBlank(bo.getAddress())) {
            lqw.eq(MetaSolanaTransaction::getAddress, bo.getAddress());
        } else if (params != null && params.containsKey("addressList")) {
            // 支持通过addressList参数进行多地址查询
            Object addressListObj = params.get("addressList");
            List<String> addressList = new ArrayList<>();

            // 类型安全的转换
            if (addressListObj instanceof List<?> rawList) {
                for (Object item : rawList) {
                    if (item instanceof String) {
                        addressList.add((String) item);
                    }
                }
            }

            if (!addressList.isEmpty()) {
                lqw.and(wrapper -> wrapper.in(MetaSolanaTransaction::getAddress, addressList)
                    .or()
                    .in(MetaSolanaTransaction::getFromaddress, addressList));
            }
        }
        //排序问题
        lqw.orderByDesc(params != null && params.get("orderByAmount") != null, MetaSolanaTransaction::getAmount);

        lqw.eq(StringUtils.isNotBlank(bo.getFromaddress()), MetaSolanaTransaction::getFromaddress, bo.getFromaddress());
        lqw.eq(StringUtils.isNotBlank(bo.getContract()), MetaSolanaTransaction::getContract, bo.getContract());
        lqw.eq(bo.getAmount() != null, MetaSolanaTransaction::getAmount, bo.getAmount());
        lqw.eq(bo.getFee() != null, MetaSolanaTransaction::getFee, bo.getFee());
        lqw.eq(bo.getTimestamp() != null, MetaSolanaTransaction::getTimestamp, bo.getTimestamp());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), MetaSolanaTransaction::getType, bo.getType());
        lqw.eq(bo.getIssync() != null, MetaSolanaTransaction::getIssync, bo.getIssync());
        return lqw;
    }

    /**
     * 新增Solana区块高度交易明细
     *
     * @param bo Solana区块高度交易明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MetaSolanaTransactionBo bo) {
        MetaSolanaTransaction add = MapstructUtils.convert(bo, MetaSolanaTransaction.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改Solana区块高度交易明细
     *
     * @param bo Solana区块高度交易明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MetaSolanaTransactionBo bo) {
        MetaSolanaTransaction update = MapstructUtils.convert(bo, MetaSolanaTransaction.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MetaSolanaTransaction entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除Solana区块高度交易明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
