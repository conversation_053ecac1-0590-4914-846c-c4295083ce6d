package org.dromara.wallet.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.wallet.domain.vo.MetaTransferRecVo;
import org.dromara.wallet.domain.bo.MetaTransferRecBo;
import org.dromara.wallet.service.IMetaTransferRecService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 转账记录
 * 前端访问路由地址为:/wallet/transferRec
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/transferRec")
public class MetaTransferRecController extends BaseController {

    private final IMetaTransferRecService metaTransferRecService;

    /**
     * 查询转账记录列表
     */
    @SaCheckPermission("wallet:transferRec:list")
    @GetMapping("/list")
    public TableDataInfo<MetaTransferRecVo> list(MetaTransferRecBo bo, PageQuery pageQuery) {
        return metaTransferRecService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出转账记录列表
     */
    @SaCheckPermission("wallet:transferRec:export")
    @Log(title = "转账记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MetaTransferRecBo bo, HttpServletResponse response) {
        List<MetaTransferRecVo> list = metaTransferRecService.queryList(bo);
        ExcelUtil.exportExcel(list, "转账记录", MetaTransferRecVo.class, response);
    }

    /**
     * 获取转账记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("wallet:transferRec:query")
    @GetMapping("/{id}")
    public R<MetaTransferRecVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(metaTransferRecService.queryById(id));
    }

    /**
     * 新增转账记录
     */
    @SaCheckPermission("wallet:transferRec:add")
    @Log(title = "转账记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MetaTransferRecBo bo) {
        return toAjax(metaTransferRecService.insertByBo(bo));
    }

    /**
     * 修改转账记录
     */
    @SaCheckPermission("wallet:transferRec:edit")
    @Log(title = "转账记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MetaTransferRecBo bo) {
        return toAjax(metaTransferRecService.updateByBo(bo));
    }

    /**
     * 删除转账记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("wallet:transferRec:remove")
    @Log(title = "转账记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(metaTransferRecService.deleteWithValidByIds(List.of(ids), true));
    }
}
