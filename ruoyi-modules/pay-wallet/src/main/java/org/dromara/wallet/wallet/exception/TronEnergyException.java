package org.dromara.wallet.wallet.exception;

import java.io.Serial;

/**
 * TRON能量相关异常类
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
public class TronEnergyException extends TronException {

    @Serial
    private static final long serialVersionUID = 1L;

    public TronEnergyException(String message) {
        super(message);
    }

    public TronEnergyException(String code, Object... args) {
        super("energy." + code, args);
    }

    /**
     * 获取能量价格失败异常
     */
    public static TronEnergyException getEnergyPriceFailed() {
        return new TronEnergyException("获取能量的TRX单价失败");
    }

    /**
     * 获取带宽价格失败异常
     */
    public static TronEnergyException getBandwidthPriceFailed() {
        return new TronEnergyException("获取带宽的TRX单价失败");
    }

    /**
     * 能量换算TRX失败异常
     */
    public static TronEnergyException energyToTrxFailed() {
        return new TronEnergyException("获取能量换算TRX失败");
    }

    /**
     * 代理能量失败异常
     */
    public static TronEnergyException delegateEnergyFailed(String reason) {
        return new TronEnergyException("代理能量失败: " + reason);
    }

    /**
     * 能量回收失败异常
     */
    public static TronEnergyException undelegateEnergyFailed(String reason) {
        return new TronEnergyException("能量回收失败: " + reason);
    }

    /**
     * 获取所需能量失败异常
     */
    public static TronEnergyException getNeedEnergyFailed(String reason) {
        return new TronEnergyException("获取所需能量失败: " + reason);
    }
}
