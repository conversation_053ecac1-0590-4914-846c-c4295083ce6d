package org.dromara.wallet.wallet.transfer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * TRON转账监控配置
 *
 * <p>配置TRON转账监控的各项参数，包括：</p>
 * <ul>
 *   <li>监控开关和采集频率</li>
 *   <li>性能指标阈值设置</li>
 *   <li>数据保留和清理策略</li>
 *   <li>监控报告生成规则</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
@Data
@Component
@ConfigurationProperties(prefix = "tron.transfer.monitor")
public class TronTransferMonitorConfig {

    // ==================== 基础监控配置 ====================

    /**
     * 是否启用转账监控
     */
    private boolean enabled = true;

    /**
     * 是否启用性能监控
     */
    private boolean performanceMonitorEnabled = true;

    /**
     * 是否启用异常监控
     */
    private boolean exceptionMonitorEnabled = true;

    /**
     * 是否启用详细日志记录
     */
    private boolean detailedLoggingEnabled = false;

    // ==================== 数据采集配置 ====================

    /**
     * 监控数据采集间隔（秒）
     */
    private int dataCollectionIntervalSeconds = 60;

    /**
     * 性能统计窗口时间（分钟）
     */
    private int performanceWindowMinutes = 5;

    /**
     * 是否实时计算统计指标
     */
    private boolean realTimeMetricsEnabled = true;

    /**
     * 监控指标缓存大小
     */
    private int metricsCacheSize = 10000;

    // ==================== 性能阈值配置 ====================

    /**
     * 转账成功率警告阈值（百分比）
     */
    private double successRateWarningThreshold = 95.0;

    /**
     * 转账成功率错误阈值（百分比）
     */
    private double successRateErrorThreshold = 90.0;

    /**
     * 平均转账时间警告阈值（毫秒）
     */
    private long averageTransferTimeWarningThreshold = 20000;

    /**
     * 平均转账时间错误阈值（毫秒）
     */
    private long averageTransferTimeErrorThreshold = 30000;

    /**
     * 最大转账时间阈值（毫秒）
     */
    private long maxTransferTimeThreshold = 60000;

    /**
     * 平均确认时间警告阈值（毫秒）
     */
    private long averageConfirmationTimeWarningThreshold = 30000;

    /**
     * 平均确认时间错误阈值（毫秒）
     */
    private long averageConfirmationTimeErrorThreshold = 60000;

    // ==================== 异常监控配置 ====================

    /**
     * 异常频率警告阈值（百分比）
     */
    private double exceptionRateWarningThreshold = 5.0;

    /**
     * 异常频率错误阈值（百分比）
     */
    private double exceptionRateErrorThreshold = 10.0;

    /**
     * 连续失败次数告警阈值
     */
    private int consecutiveFailureThreshold = 5;

    /**
     * 网络异常重试次数阈值
     */
    private int networkExceptionRetryThreshold = 3;

    // ==================== 手续费监控配置 ====================

    /**
     * 是否启用手续费监控
     */
    private boolean feeMonitorEnabled = true;

    /**
     * 手续费异常阈值（TRX）
     */
    private double feeAnomalyThreshold = 10.0;

    /**
     * 手续费提供率警告阈值（百分比）
     */
    private double feeProvisionRateWarningThreshold = 50.0;

    /**
     * 手续费钱包余额警告阈值（TRX）
     */
    private double feeWalletBalanceWarningThreshold = 100.0;

    // ==================== 数据存储配置 ====================

    /**
     * 是否启用监控数据持久化
     */
    private boolean dataPersistenceEnabled = false;

    /**
     * 监控数据保留天数
     */
    private int dataRetentionDays = 7;

    /**
     * 数据清理检查间隔（小时）
     */
    private int dataCleanupIntervalHours = 24;

    /**
     * 是否压缩历史数据
     */
    private boolean dataCompressionEnabled = true;

    // ==================== 报告生成配置 ====================

    /**
     * 是否启用定时报告
     */
    private boolean scheduledReportEnabled = false;

    /**
     * 报告生成间隔（小时）
     */
    private int reportGenerationIntervalHours = 6;

    /**
     * 报告保留天数
     */
    private int reportRetentionDays = 30;

    /**
     * 是否自动发送报告
     */
    private boolean autoSendReportEnabled = false;

    // ==================== 高级配置 ====================

    /**
     * 监控线程池大小
     */
    private int monitorThreadPoolSize = 2;

    /**
     * 监控任务队列大小
     */
    private int monitorTaskQueueSize = 1000;

    /**
     * 监控数据批处理大小
     */
    private int batchProcessingSize = 100;

    /**
     * 是否启用监控自诊断
     */
    private boolean selfDiagnosticsEnabled = true;

    /**
     * 监控自诊断间隔（分钟）
     */
    private int selfDiagnosticsIntervalMinutes = 30;

    // ==================== 验证方法 ====================

    /**
     * 验证配置是否有效
     */
    public boolean isValid() {
        // 检查基础配置
        if (dataCollectionIntervalSeconds <= 0 || performanceWindowMinutes <= 0) {
            return false;
        }

        // 检查阈值配置
        if (successRateWarningThreshold < successRateErrorThreshold) {
            return false;
        }

        if (averageTransferTimeWarningThreshold > averageTransferTimeErrorThreshold) {
            return false;
        }

        // 检查数据保留配置
        if (dataRetentionDays <= 0 || reportRetentionDays <= 0) {
            return false;
        }

        return true;
    }

    /**
     * 获取监控级别
     */
    public String getMonitorLevel(double successRate, long averageTime, double exceptionRate) {
        // 检查错误级别
        if (successRate < successRateErrorThreshold ||
            averageTime > averageTransferTimeErrorThreshold ||
            exceptionRate > exceptionRateErrorThreshold) {
            return "ERROR";
        }

        // 检查警告级别
        if (successRate < successRateWarningThreshold ||
            averageTime > averageTransferTimeWarningThreshold ||
            exceptionRate > exceptionRateWarningThreshold) {
            return "WARN";
        }

        return "INFO";
    }

    /**
     * 检查是否需要告警
     */
    public boolean shouldAlert(double successRate, long averageTime, double exceptionRate) {
        return !getMonitorLevel(successRate, averageTime, exceptionRate).equals("INFO");
    }

    /**
     * 获取配置摘要
     */
    public String getConfigSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("TRON转账监控配置: ");
        summary.append("enabled=").append(enabled);
        
        if (enabled) {
            summary.append(", performance=").append(performanceMonitorEnabled);
            summary.append(", exception=").append(exceptionMonitorEnabled);
            summary.append(", fee=").append(feeMonitorEnabled);
            summary.append(", interval=").append(dataCollectionIntervalSeconds).append("s");
            summary.append(", window=").append(performanceWindowMinutes).append("min");
            summary.append(", retention=").append(dataRetentionDays).append("days");
        }
        
        return summary.toString();
    }

    /**
     * 获取性能阈值摘要
     */
    public String getThresholdSummary() {
        return String.format(
                "性能阈值: 成功率(警告=%.1f%%, 错误=%.1f%%), " +
                "转账时间(警告=%dms, 错误=%dms), " +
                "异常率(警告=%.1f%%, 错误=%.1f%%)",
                successRateWarningThreshold, successRateErrorThreshold,
                averageTransferTimeWarningThreshold, averageTransferTimeErrorThreshold,
                exceptionRateWarningThreshold, exceptionRateErrorThreshold
        );
    }

    // ==================== 动态配置更新 ====================

    /**
     * 更新性能阈值
     */
    public void updatePerformanceThresholds(double successRateWarning, double successRateError,
                                          long transferTimeWarning, long transferTimeError) {
        this.successRateWarningThreshold = successRateWarning;
        this.successRateErrorThreshold = successRateError;
        this.averageTransferTimeWarningThreshold = transferTimeWarning;
        this.averageTransferTimeErrorThreshold = transferTimeError;
    }

    /**
     * 更新异常监控阈值
     */
    public void updateExceptionThresholds(double exceptionRateWarning, double exceptionRateError,
                                        int consecutiveFailure) {
        this.exceptionRateWarningThreshold = exceptionRateWarning;
        this.exceptionRateErrorThreshold = exceptionRateError;
        this.consecutiveFailureThreshold = consecutiveFailure;
    }
}
