package org.dromara.wallet.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.wallet.domain.vo.MetaBaseTransactionVo;
import org.dromara.wallet.domain.bo.MetaBaseTransactionBo;
import org.dromara.wallet.service.IMetaBaseTransactionService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * base区块高度交易明细
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/wallet/baseTransactions")
public class MetaBaseTransactionController extends BaseController {

    private final IMetaBaseTransactionService metaBaseTransactionService;

    /**
     * 查询base区块高度交易明细列表
     */
    @SaCheckPermission("wallet:baseTransactions:list")
    @GetMapping("/list")
    public TableDataInfo<MetaBaseTransactionVo> list(MetaBaseTransactionBo bo, PageQuery pageQuery) {
        return metaBaseTransactionService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出base区块高度交易明细列表
     */
    @SaCheckPermission("wallet:baseTransactions:export")
    @Log(title = "base区块高度交易明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MetaBaseTransactionBo bo, HttpServletResponse response) {
        List<MetaBaseTransactionVo> list = metaBaseTransactionService.queryList(bo);
        ExcelUtil.exportExcel(list, "base区块高度交易明细", MetaBaseTransactionVo.class, response);
    }

    /**
     * 获取base区块高度交易明细详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("wallet:baseTransactions:query")
    @GetMapping("/{id}")
    public R<MetaBaseTransactionVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(metaBaseTransactionService.queryById(id));
    }

    /**
     * 新增base区块高度交易明细
     */
    @SaCheckPermission("wallet:baseTransactions:add")
    @Log(title = "base区块高度交易明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MetaBaseTransactionBo bo) {
        return toAjax(metaBaseTransactionService.insertByBo(bo));
    }

    /**
     * 修改base区块高度交易明细
     */
    @SaCheckPermission("wallet:baseTransactions:edit")
    @Log(title = "base区块高度交易明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MetaBaseTransactionBo bo) {
        return toAjax(metaBaseTransactionService.updateByBo(bo));
    }

    /**
     * 删除base区块高度交易明细
     *
     * @param ids 主键串
     */
    @SaCheckPermission("wallet:baseTransactions:remove")
    @Log(title = "base区块高度交易明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(metaBaseTransactionService.deleteWithValidByIds(List.of(ids), true));
    }
}
