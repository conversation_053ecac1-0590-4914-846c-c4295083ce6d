package org.dromara.wallet.service.processor.impl;

import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.ChainType;
import org.dromara.wallet.config.facade.TronConfigFacade;
import org.dromara.wallet.domain.bo.WalletCoinRecBo;
import org.dromara.wallet.service.IWalletCoinRecService;
import org.dromara.wallet.service.processor.WalletChainProcessor;
import org.dromara.wallet.service.processor.WalletProcessResult;
import org.dromara.wallet.wallet.helper.TronHelper;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * TRON链钱包处理器
 * 负责处理TRON链的钱包余额记录
 *
 * <p>处理内容包括：</p>
 * <ul>
 *   <li>TRX原生代币余额</li>
 *   <li>TRC20合约代币余额</li>
 *   <li>租户信息处理</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TronWalletProcessor implements WalletChainProcessor {

    private final TronConfigFacade tronConfigFacade;
    private final TronHelper tronHelper;
    @Lazy
    @Resource
    private IWalletCoinRecService walletCoinRecService;

    @Override
    public ChainType getSupportedChainType() {
        return ChainType.TRON;
    }

    @Override
    public void processWallet(String walletAddress, String walletTenantId) {
        if (walletAddress == null || walletAddress.isEmpty()) {
            log.error("TRON钱包地址不能为空");
            return;
        }

        log.debug("开始处理TRON钱包: 地址={}, 租户ID={}", walletAddress, walletTenantId);

        try {
            WalletProcessResult result = new WalletProcessResult(walletAddress, "TRON");

            // 处理TRX原生代币
            processTronNativeToken(walletAddress, walletTenantId, result);

            // 处理TRC20代币
            processTronTokens(walletAddress, walletTenantId, result);

            result.finish();
            if (result.hasFailures()) {
                log.warn(result.getSummary());
            } else {
                log.info(result.getSummary());
            }

            // 详细日志（debug级别）
            if (log.isDebugEnabled()) {
                log.debug(result.getDetailedSummary());
            }

        } catch (Exception e) {
            log.error("TRON钱包处理失败: 地址={}, 租户ID={}, 错误={}",
                walletAddress, walletTenantId, e.getMessage(), e);
            throw new RuntimeException("TRON钱包处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理TRX原生代币
     */
    private void processTronNativeToken(String walletAddress, String walletTenantId, WalletProcessResult result) {
        try {
            // 使用统一入口查询TRX余额
            BigDecimal trxBalance = tronHelper.balanceGetForRead(walletAddress, "TRX");

            if (trxBalance != null) {
                WalletCoinRecBo balanceRecord = new WalletCoinRecBo();
                balanceRecord.setChainType("TRON");
                balanceRecord.setWalletAddress(walletAddress);
                balanceRecord.setTokenAddress("_TRX_NATIVE_");
                balanceRecord.setTokenSymbol("TRX");
                balanceRecord.setBalance(trxBalance);
                balanceRecord.setDecimals(6); // TRX默认6位小数
                balanceRecord.setCreateBy(1111L);
                balanceRecord.setTenantId(walletTenantId); // 设置租户ID

                walletCoinRecService.insertByBo(balanceRecord);
                result.addSuccess("TRX", trxBalance);
            }
        } catch (Exception e) {
            result.addFailure("TRX");
            log.debug("处理TRX原生代币失败: {}, error: {}", walletAddress, e.getMessage());
        }
    }

    /**
     * 处理TRC20代币
     */
    private void processTronTokens(String walletAddress, String walletTenantId, WalletProcessResult result) {
        // 获取所有启用的代币
        for (String tokenSymbol : tronConfigFacade.getEnabledTokenSymbols()) {
            if ("TRX".equalsIgnoreCase(tokenSymbol)) {
                continue; // 原生代币已经处理过了
            }

            try {
                // 使用统一入口查询代币余额
                BigDecimal balance = tronHelper.balanceGetForRead(walletAddress, tokenSymbol);

                if (balance != null) {
                    WalletCoinRecBo balanceRecord = new WalletCoinRecBo();
                    balanceRecord.setChainType("TRON");
                    balanceRecord.setWalletAddress(walletAddress);
                    balanceRecord.setTokenAddress(tronConfigFacade.getContractAddress(tokenSymbol));
                    balanceRecord.setTokenSymbol(tokenSymbol);
                    balanceRecord.setBalance(balance);
                    balanceRecord.setDecimals(tronConfigFacade.getContractDecimals(tokenSymbol));
                    balanceRecord.setCreateBy(1111L);
                    balanceRecord.setTenantId(walletTenantId); // 设置租户ID

                    walletCoinRecService.insertByBo(balanceRecord);
                    result.addSuccess(tokenSymbol, balance);
                }
            } catch (Exception e) {
                result.addFailure(tokenSymbol);
                log.debug("处理TRON代币失败: {} {}, error: {}", walletAddress, tokenSymbol, e.getMessage());
            }
        }
    }
}
