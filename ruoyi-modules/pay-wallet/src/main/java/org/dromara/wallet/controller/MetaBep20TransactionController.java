package org.dromara.wallet.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.wallet.domain.vo.MetaBep20TransactionVo;
import org.dromara.wallet.domain.bo.MetaBep20TransactionBo;
import org.dromara.wallet.service.IMetaBep20TransactionService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * BSC区块高度交易明细
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/wallet/bep20Transactions")
public class MetaBep20TransactionController extends BaseController {

    private final IMetaBep20TransactionService metaBep20TransactionService;

    /**
     * 查询BSC区块高度交易明细列表
     */
    @SaCheckPermission("wallet:bep20Transactions:list")
    @GetMapping("/list")
    public TableDataInfo<MetaBep20TransactionVo> list(MetaBep20TransactionBo bo, PageQuery pageQuery) {
        return metaBep20TransactionService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出BSC区块高度交易明细列表
     */
    @SaCheckPermission("wallet:bep20Transactions:export")
    @Log(title = "BSC区块高度交易明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MetaBep20TransactionBo bo, HttpServletResponse response) {
        List<MetaBep20TransactionVo> list = metaBep20TransactionService.queryList(bo);
        ExcelUtil.exportExcel(list, "BSC区块高度交易明细", MetaBep20TransactionVo.class, response);
    }

    /**
     * 获取BSC区块高度交易明细详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("wallet:bep20Transactions:query")
    @GetMapping("/{id}")
    public R<MetaBep20TransactionVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(metaBep20TransactionService.queryById(id));
    }

    /**
     * 新增BSC区块高度交易明细
     */
    @SaCheckPermission("wallet:bep20Transactions:add")
    @Log(title = "BSC区块高度交易明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MetaBep20TransactionBo bo) {
        return toAjax(metaBep20TransactionService.insertByBo(bo));
    }

    /**
     * 修改BSC区块高度交易明细
     */
    @SaCheckPermission("wallet:bep20Transactions:edit")
    @Log(title = "BSC区块高度交易明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MetaBep20TransactionBo bo) {
        return toAjax(metaBep20TransactionService.updateByBo(bo));
    }

    /**
     * 删除BSC区块高度交易明细
     *
     * @param ids 主键串
     */
    @SaCheckPermission("wallet:bep20Transactions:remove")
    @Log(title = "BSC区块高度交易明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(metaBep20TransactionService.deleteWithValidByIds(List.of(ids), true));
    }
}
