package org.dromara.wallet.service.processor;

import lombok.Getter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 钱包处理结果统计类
 * 用于记录钱包余额处理的统计信息和结果
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public class WalletProcessResult {
    
    // Getters
    @Getter
    private final String walletAddress;
    private final String chainType;
    private final List<String> successTokens = new ArrayList<>();
    private final List<String> failedTokens = new ArrayList<>();
    private final Map<String, BigDecimal> tokenBalances = new HashMap<>();
    private long startTime;
    private long endTime;

    public WalletProcessResult(String walletAddress, String chainType) {
        this.walletAddress = walletAddress;
        this.chainType = chainType;
        this.startTime = System.currentTimeMillis();
    }

    public void addSuccess(String tokenSymbol, BigDecimal balance) {
        successTokens.add(tokenSymbol);
        tokenBalances.put(tokenSymbol, balance);
    }

    public void addFailure(String tokenSymbol) {
        failedTokens.add(tokenSymbol);
    }

    public void finish() {
        this.endTime = System.currentTimeMillis();
    }

    public boolean hasFailures() {
        return !failedTokens.isEmpty();
    }

    public String getSummary() {
        long duration = endTime - startTime;
        return String.format("%s钱包处理完成: 地址=%s, 成功=%d, 失败=%d, 耗时=%dms",
            chainType, walletAddress, successTokens.size(), failedTokens.size(), duration);
    }

    public String getDetailedSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append(getSummary()).append("\n");
        
        if (!successTokens.isEmpty()) {
            sb.append("成功代币: ");
            for (String token : successTokens) {
                BigDecimal balance = tokenBalances.get(token);
                sb.append(String.format("%s(%.6f) ", token, balance));
            }
            sb.append("\n");
        }
        
        if (!failedTokens.isEmpty()) {
            sb.append("失败代币: ").append(String.join(", ", failedTokens)).append("\n");
        }
        
        return sb.toString();
    }
}
