package org.dromara.wallet.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.wallet.domain.bo.TrcCstaddressBo;
import org.dromara.wallet.domain.vo.TrcCstaddressVo;

import java.util.Collection;
import java.util.List;

/**
 * TRON钱包地址Service接口
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
public interface ITronAddressService {

    /**
     * 查询TRON钱包地址
     *
     * @param id 主键
     * @return TRON钱包地址
     */
    TrcCstaddressVo queryById(Long id);

    /**
     * 根据客户ID查询TRON钱包地址
     *
     * @param cstId 客户ID
     * @return TRON钱包地址
     */
    TrcCstaddressVo queryByCstId(Long cstId);

    /**
     * 分页查询TRON钱包地址列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return TRON钱包地址分页列表
     */
    TableDataInfo<TrcCstaddressVo> queryPageList(TrcCstaddressBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的TRON钱包地址列表
     *
     * @param bo 查询条件
     * @return TRON钱包地址列表
     */
    List<TrcCstaddressVo> queryList(TrcCstaddressBo bo);

    /**
     * 新增TRON钱包地址
     *
     * @param bo TRON钱包地址
     * @return 是否新增成功
     */
    Boolean insertByBo(TrcCstaddressBo bo);

    /**
     * 修改TRON钱包地址
     *
     * @param bo TRON钱包地址
     * @return 是否修改成功
     */
    Boolean updateByBo(TrcCstaddressBo bo);

    /**
     * 校验并批量删除TRON钱包地址信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
