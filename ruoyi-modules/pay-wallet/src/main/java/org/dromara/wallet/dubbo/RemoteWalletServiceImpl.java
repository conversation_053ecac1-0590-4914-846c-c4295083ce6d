package org.dromara.wallet.dubbo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.dromara.wallet.api.RemoteWalletService;
import org.dromara.wallet.api.model.RemoteWalletDto;
import org.dromara.wallet.api.model.RemoteTransferStatusDto;
import org.dromara.wallet.api.model.RemoteBatchTransferStatusRequest;
import org.dromara.wallet.api.model.RemoteBatchTransferStatusResponse;
import org.dromara.wallet.wallet.helper.EvmHelper;
import org.dromara.wallet.wallet.helper.SolanaHelper;
import org.dromara.wallet.wallet.helper.TronHelper;
import org.dromara.wallet.wallet.transfer.service.UnifiedTransferService;
import org.dromara.wallet.wallet.transfer.dto.TransferStatusResult;
import org.dromara.wallet.wallet.transfer.dto.TransferRequest;
import org.dromara.wallet.wallet.transfer.dto.UnifiedTransferResult;
import org.dromara.wallet.config.ChainType;
import org.dromara.wallet.utils.WalletPrivateKeyUtil;
import org.dromara.wallet.config.facade.*;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 钱包服务Dubbo实现
 *
 * <AUTHOR>
 * @date 2025/7/18 16:12
 **/
@Slf4j
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteWalletServiceImpl implements RemoteWalletService {

    private final UnifiedTransferService unifiedTransferService;
    private final TronConfigFacade tronConfigFacade;
    private final BscConfigFacade bscConfigFacade;
    private final ArbConfigFacade arbConfigFacade;
    private final BaseConfigFacade baseConfigFacade;
    private final AvaxConfigFacade avaxConfigFacade;
    private final SolanaConfigFacade solanaConfigFacade;

    @Override
    public String withdraw(RemoteWalletDto dto) {
        log.info("开始执行内部提款，requestId: {}, toAddress: {}, amount: {}, tokenSymbol: {}, chainName: {}",
            dto.getRequestId(), dto.getToAddress(), dto.getAmount(), dto.getTokenSymbol(), dto.getChainName());

        try {
            // 1. 参数验证
            validateWithdrawRequest(dto);

            // 2. 获取链类型
            ChainType chainType = ChainType.fromCode(dto.getChainName());
            if (chainType == null) {
                throw new IllegalArgumentException("不支持的区块链类型: " + dto.getChainName());
            }

            // 3. 获取资金钱包私钥
            String privateKey = getFundWalletPrivateKey(chainType);
            if (privateKey == null) {
                throw new IllegalArgumentException(chainType.getDisplayName() + "链资金钱包未配置");
            }

            // 4. 构建转账请求
            TransferRequest request = TransferRequest.builder()
                .privateKey(privateKey)
                .toAddress(dto.getToAddress())
                .amount(dto.getAmount().toString())
                .tokenSymbol(dto.getTokenSymbol())
                .chainName(dto.getChainName())
                .enableFeeWallet(true)
                .businessType("withdraw")
                .stpValue(getStpValue())
                .waitForConfirmation(false)
                .memo("安全提款操作")
                .requestId(dto.getRequestId())
                .syncMode(false) // 异步模式
                .build();

            // 5. 执行转账
            UnifiedTransferResult transferResult = unifiedTransferService.transfer(request);

            if (transferResult.isSuccess()) {
                log.info("内部提款执行成功，requestId: {}, orderId: {}",
                    dto.getRequestId(), transferResult.getOrderId());
                return transferResult.getOrderId() != null ? transferResult.getOrderId().toString() : "SUCCESS";
            } else {
                log.error("内部提款执行失败，requestId: {}, error: {}",
                    dto.getRequestId(), transferResult.getErrorMessage());
                throw new RuntimeException("提款执行失败: " + transferResult.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("内部提款处理异常，requestId: {}, error: {}", dto.getRequestId(), e.getMessage(), e);
            throw new RuntimeException("提款处理异常: " + e.getMessage(), e);
        }
    }

    /**
     * 验证提款请求参数
     */
    private void validateWithdrawRequest(RemoteWalletDto dto) {
        if (dto == null) {
            throw new IllegalArgumentException("提款请求不能为空");
        }
        if (dto.getToAddress() == null || dto.getToAddress().trim().isEmpty()) {
            throw new IllegalArgumentException("收款地址不能为空");
        }
        if (dto.getAmount() == null || dto.getAmount().compareTo(java.math.BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("提款金额必须大于0");
        }
        if (dto.getTokenSymbol() == null || dto.getTokenSymbol().trim().isEmpty()) {
            throw new IllegalArgumentException("代币符号不能为空");
        }
        if (dto.getChainName() == null || dto.getChainName().trim().isEmpty()) {
            throw new IllegalArgumentException("区块链名称不能为空");
        }
        if (dto.getRequestId() == null || dto.getRequestId().trim().isEmpty()) {
            throw new IllegalArgumentException("请求ID不能为空");
        }
    }

    /**
     * 获取资金钱包私钥
     */
    private String getFundWalletPrivateKey(ChainType chainType) {
        try {
            String encryptedPrivateKey = switch (chainType) {
                case TRON -> tronConfigFacade.getFeeWalletPrivateKey();
                case BSC -> bscConfigFacade.getFeeWalletPrivateKey();
                case ARB -> arbConfigFacade.getFeeWalletPrivateKey();
                case BASE -> baseConfigFacade.getFeeWalletPrivateKey();
                case AVAX -> avaxConfigFacade.getFeeWalletPrivateKey();
                case SOLANA -> solanaConfigFacade.getFeeWalletPrivateKey();
            };

            if (encryptedPrivateKey == null || encryptedPrivateKey.trim().isEmpty()) {
                return null;
            }

            // 解密私钥
            return WalletPrivateKeyUtil.getDecryptedPrivateKey(encryptedPrivateKey);
        } catch (Exception e) {
            log.error("获取{}链资金钱包私钥失败: {}", chainType.getDisplayName(), e.getMessage());
            return null;
        }
    }

    /**
     * 获取STP值
     */
    private String getStpValue() {
        try {
            return StpUtil.getTokenValue();
        } catch (Exception e) {
            log.warn("获取STP值失败，使用默认值: {}", e.getMessage());
            return "default";
        }
    }

    @Override
    public RemoteTransferStatusDto queryTransferStatusByRequestId(String requestId) {
        log.info("Dubbo接口查询转账状态: requestId={}", requestId);

        try {
            // 调用统一转账服务查询状态
            TransferStatusResult statusResult = unifiedTransferService.queryTransferStatusByRequestId(requestId);

            // 转换为Dubbo接口返回的DTO
            RemoteTransferStatusDto result = convertToRemoteDto(statusResult);

            log.info("Dubbo接口查询转账状态成功: requestId={}, status={}", requestId, result.getStatus());
            return result;

        } catch (Exception e) {
            log.error("Dubbo接口查询转账状态失败: requestId={}, error={}", requestId, e.getMessage(), e);

            // 返回错误结果
            return RemoteTransferStatusDto.builder()
                .requestId(requestId)
                .status(-1)
                .statusDescription("查询失败")
                .errorMessage("查询转账状态时发生错误: " + e.getMessage())
                .queryTime(new Date())
                .totalRecords(0)
                .confirmedRecords(0)
                .failedRecords(0)
                .build();
        }
    }

    /**
     * 转换TransferStatusResult为RemoteTransferStatusDto
     */
    private RemoteTransferStatusDto convertToRemoteDto(TransferStatusResult statusResult) {
        if (statusResult == null) {
            return RemoteTransferStatusDto.builder()
                .status(-1)
                .statusDescription("未知错误")
                .errorMessage("查询结果为空")
                .queryTime(new Date())
                .totalRecords(0)
                .confirmedRecords(0)
                .failedRecords(0)
                .build();
        }

        // 创建转账记录详情（单个记录）
        RemoteTransferStatusDto.TransferRecordDetail recordDetail =
            RemoteTransferStatusDto.TransferRecordDetail.builder()
                .id(statusResult.getOrderId())
                .transactionHash(statusResult.getTxHash())
                .chainName(statusResult.getChainName())
                .confirmationStatus(statusResult.getStatus())
                .requiredConfirmations(statusResult.getRequiredConfirmations())
                .actualConfirmations(statusResult.getActualConfirmations())
                .blockHeight(statusResult.getBlockHeight())
                .startTime(statusResult.getStartTime() != null ?
                    Date.from(statusResult.getStartTime().atZone(java.time.ZoneId.systemDefault()).toInstant()) : null)
                .endTime(statusResult.getEndTime() != null ?
                    Date.from(statusResult.getEndTime().atZone(java.time.ZoneId.systemDefault()).toInstant()) : null)
                .confirmationTimeMs(statusResult.getProcessingTimeMs())
                .retryCount(statusResult.getRetryCount())
                .errorMessage(statusResult.getErrorMessage())
                .transferType(statusResult.getTransferType())
                .build();

        // 计算统计信息
        int totalRecords = 1;
        int confirmedRecords = (statusResult.getStatus() != null && statusResult.getStatus() == 2) ? 1 : 0;
        int failedRecords = (statusResult.getStatus() != null && (statusResult.getStatus() == 3 || statusResult.getStatus() == 4)) ? 1 : 0;

        return RemoteTransferStatusDto.builder()
            .requestId(statusResult.getRequestId())
            .status(statusResult.getStatus())
            .statusDescription(statusResult.getStatusDescription())
            .totalRecords(totalRecords)
            .confirmedRecords(confirmedRecords)
            .failedRecords(failedRecords)
            .errorMessage(statusResult.getErrorMessage())
            .queryTime(new Date())
            .records(List.of(recordDetail))
            .build();
    }

    @Override
    public RemoteBatchTransferStatusResponse batchQueryTransferStatus(RemoteBatchTransferStatusRequest request) {
        log.info("Dubbo接口批量查询转账状态: requestIds={}, includeDetails={}",
            request.getRequestIds(), request.getIncludeDetails());

        try {
            // 参数验证
            if (request.getRequestIds() == null || request.getRequestIds().isEmpty()) {
                return RemoteBatchTransferStatusResponse.failure("请求ID列表不能为空");
            }

            if (request.getRequestIds().size() > 100) {
                return RemoteBatchTransferStatusResponse.failure("批量查询最多支持100个请求ID");
            }

            // 调用统一转账服务批量查询
            Map<String, TransferStatusResult> statusResults =
                unifiedTransferService.batchQueryTransferStatusByRequestIds(request.getRequestIds());

            // 转换为Dubbo接口返回的DTO
            Map<String, RemoteTransferStatusDto> results = new HashMap<>();
            List<String> notFoundRequestIds = new ArrayList<>();

            for (String requestId : request.getRequestIds()) {
                TransferStatusResult statusResult = statusResults.get(requestId);

                if (statusResult != null && statusResult.getStatus() != null && statusResult.getStatus() != -1) {
                    // 应用过滤条件
                    if (shouldIncludeResult(statusResult, request)) {
                        RemoteTransferStatusDto dto = convertToRemoteDto(statusResult);

                        // 如果不需要详细信息，清空records
                        if (!Boolean.TRUE.equals(request.getIncludeDetails())) {
                            dto.setRecords(null);
                        }

                        results.put(requestId, dto);
                    }
                } else {
                    notFoundRequestIds.add(requestId);
                }
            }

            // 生成统计信息
            RemoteBatchTransferStatusResponse.BatchStatistics statistics =
                generateBatchStatistics(results.values());

            RemoteBatchTransferStatusResponse response = RemoteBatchTransferStatusResponse.success(
                results, notFoundRequestIds, statistics);

            log.info("Dubbo接口批量查询转账状态成功: 请求{}个, 找到{}个, 未找到{}个",
                request.getRequestIds().size(), results.size(), notFoundRequestIds.size());

            return response;

        } catch (Exception e) {
            log.error("Dubbo接口批量查询转账状态失败: requestIds={}, error={}",
                request.getRequestIds(), e.getMessage(), e);

            return RemoteBatchTransferStatusResponse.failure("批量查询转账状态时发生错误: " + e.getMessage());
        }
    }

    /**
     * 判断是否应该包含该结果（根据过滤条件）
     */
    private boolean shouldIncludeResult(TransferStatusResult statusResult, RemoteBatchTransferStatusRequest request) {
        Integer status = statusResult.getStatus();
        if (status == null) {
            return false;
        }

        // 只返回失败的记录
        if (Boolean.TRUE.equals(request.getOnlyFailures())) {
            return status == 3 || status == 4; // 失败或超时
        }

        // 只返回处理中的记录
        if (Boolean.TRUE.equals(request.getOnlyProcessing())) {
            return status == 0 || status == 1; // 待确认或确认中
        }

        // 默认返回所有记录
        return true;
    }

    /**
     * 生成批量查询统计信息
     */
    private RemoteBatchTransferStatusResponse.BatchStatistics generateBatchStatistics(
        Iterable<RemoteTransferStatusDto> results) {

        int pendingCount = 0;
        int processingCount = 0;
        int confirmedCount = 0;
        int failedCount = 0;
        int timeoutCount = 0;

        Map<String, Integer> chainStatistics = new HashMap<>();
        Map<String, Integer> typeStatistics = new HashMap<>();

        for (RemoteTransferStatusDto result : results) {
            Integer status = result.getStatus();
            if (status != null) {
                switch (status) {
                    case 0 -> pendingCount++;
                    case 1 -> processingCount++;
                    case 2 -> confirmedCount++;
                    case 3 -> failedCount++;
                    case 4 -> timeoutCount++;
                }
            }

            // 统计区块链分布
            if (result.getRecords() != null) {
                for (RemoteTransferStatusDto.TransferRecordDetail record : result.getRecords()) {
                    if (record.getChainName() != null) {
                        chainStatistics.merge(record.getChainName(), 1, Integer::sum);
                    }
                    if (record.getTransferType() != null) {
                        typeStatistics.merge(record.getTransferType(), 1, Integer::sum);
                    }
                }
            }
        }

        return RemoteBatchTransferStatusResponse.BatchStatistics.builder()
            .pendingCount(pendingCount)
            .processingCount(processingCount)
            .confirmedCount(confirmedCount)
            .failedCount(failedCount)
            .timeoutCount(timeoutCount)
            .chainStatistics(chainStatistics)
            .typeStatistics(typeStatistics)
            .build();
    }
}
