package org.dromara.wallet.wallet.exception;

import java.io.Serial;

/**
 * TRON转账异常类
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
public class TronTransferException extends TronException {

    @Serial
    private static final long serialVersionUID = 1L;

    public TronTransferException(String message) {
        super(message);
    }

    public TronTransferException(String code, Object... args) {
        super("transfer." + code, args);
    }

    /**
     * TRC20转账失败异常
     */
    public static TronTransferException trc20TransferFailed(String reason) {
        return new TronTransferException("TRC20转账失败: " + reason);
    }

    /**
     * TRX转账失败异常
     */
    public static TronTransferException trxTransferFailed(String reason) {
        return new TronTransferException("TRX转账失败: " + reason);
    }

    /**
     * 转账参数错误异常
     */
    public static TronTransferException invalidTransferParams(String fromAddress, String toAddress, long amount) {
        return new TronTransferException(String.format("转账参数错误: fromAddress=%s, toAddress=%s, amount=%d",
            fromAddress, toAddress, amount));
    }

    /**
     * 能量不足异常
     */
    public static TronTransferException insufficientEnergy() {
        return new TronTransferException("能量不足，无法完成转账");
    }

    /**
     * 超过能量限制异常
     */
    public static TronTransferException energyLimitExceeded() {
        return new TronTransferException("超过TRX或能量限制");
    }
}
