package org.dromara.wallet.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.wallet.domain.bo.MetaSolanaTransactionBo;
import org.dromara.wallet.domain.vo.MetaSolanaTransactionVo;
import org.dromara.wallet.service.IMetaSolanaCstaddressinfoService;
import org.dromara.wallet.service.IMetaSolanaTransactionsService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Solana交易明细
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/sol/solanaTransactions")
public class MetaSolanaTransactionsController extends BaseController {

    private final IMetaSolanaTransactionsService metaSolanaTransactionsService;
    //    private final SolTransactionManager solTransactionManager;
    private final IMetaSolanaCstaddressinfoService metaSolanaCstaddressinfoService;

//    /**
//     * 触发归集操作
//     */
//    @SaIgnore
//    @Log(title = "触发归集操作", businessType = BusinessType.OTHER)
//    @RepeatSubmit()
//    @PostMapping("/collect/{walletAddress}/{solCoinType}")
//    public R<Void> collect(@PathVariable String walletAddress, @PathVariable SolCoinType solCoinType) {
//        solTransactionManager.doCollection(walletAddress, solCoinType);
//        return R.ok();
//    }

//    /**
//     * 触发某条交易记录入账功能(SolTransactionManager::doData)
//     */
////    @SaCheckPermission("sol:solanaTransactions:doData")
//    @SaIgnore
//    @Log(title = "Solana交易入账", businessType = BusinessType.OTHER)
//    @RepeatSubmit()
//    @PostMapping("/doData/{transactionId}")
//    public R<Void> doData(@NotNull(message = "交易ID不能为空") @PathVariable Long transactionId) {
//        try {
//            solTransactionManager.doData(transactionId, false);
//            return R.ok();
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    /**
//     * 查询并返回RechargeDataVo格式
//     */
//    @SaIgnore
//    @GetMapping("/queryRechargeData")
//    public List<RechargeDataVo> queryRechargeData(RechargeDto rechargeDto) {
//        MetaSolanaTransactionBo bo = new MetaSolanaTransactionBo();
//        bo.getParams().put("orderByAmount", true);
//        if (StringUtils.isBlank(rechargeDto.getTxnStatus())) {
//            rechargeDto.setTxnStatus("0");
//        }
//
//       // 设置查询条件
//        if (StringUtils.isNotEmpty(rechargeDto.getTxid())) {
//            bo.setTxid(rechargeDto.getTxid());
//        }
//
//        if (rechargeDto.getId() != null) {
//            bo.setId(rechargeDto.getId());
//        }
//
////        if (StringUtils.isNotEmpty(rechargeDto.getCoinNet())) {
////            SolCoinType solCoinType = SolCoinType.valueOf(rechargeDto.getCoinNet());
////            bo.setContract(solCoinType.getContractAddress());
////        }
////        bo.setContract(SolCoinType.USDT.getContractAddress());
//
//        if (StringUtils.isNotEmpty(rechargeDto.getTxnStatus())) {
//            bo.setIssync(Integer.parseInt(rechargeDto.getTxnStatus()));
//        }
//
//        // 处理sysId查询 - 需要关联meta_solana_cstaddressinfo表
//        if (StringUtils.isNotEmpty(rechargeDto.getSysId())) {
//            // 1. 查询sysId对应的所有地址
//            MetaSolanaCstaddressinfoBo queryBo = new MetaSolanaCstaddressinfoBo();
//            queryBo.setSysId(rechargeDto.getSysId());
//            List<MetaSolanaCstaddressinfoVo> addressList = metaSolanaCstaddressinfoService.queryList(queryBo);
//
//            if (addressList != null && !addressList.isEmpty()) {
//                // 获取所有地址的集合用于查询
//                List<String> addresses = addressList.stream()
//                    .map(MetaSolanaCstaddressinfoVo::getCstAddress)
//                    .collect(Collectors.toList());
//
//                // 设置地址参数，这里需要自定义处理，因为MetaSolanaTransactionBo没有直接支持多地址查询
//                // 将地址作为参数传入查询（根据业务需求可能需要修改service层代码以支持此类查询）
//                bo.getParams().put("addressList", addresses);
//
//                // 将sysId保存，用于后续结果处理
//                bo.getParams().put("sysId", rechargeDto.getSysId());
//            } else {
//                // 如果没有找到地址，返回空结果
//                return List.of();
//            }
//        }
//
//        // 查询数据
//        List<MetaSolanaTransactionVo> tableDataInfo = metaSolanaTransactionsService.queryList(bo);
//
//        // 转换数据格式
//        // 如果查询条件包含sysId，设置到结果中
//        return tableDataInfo.stream()
//            .map(vo -> {
//                RechargeDataVo dataVo = convertToRechargeDataVo(vo);
//                MetaSolanaCstaddressinfo oneByAddress = metaSolanaCstaddressinfoService.getOneByAddress(vo.getAddress());
//                if (oneByAddress != null) {
//                    dataVo.setUserName(oneByAddress.getCstId().toString());
//                }
//                // 如果查询条件包含sysId，设置到结果中V
//                if (StringUtils.isNotEmpty(rechargeDto.getSysId())) {
//                    dataVo.setSysId(rechargeDto.getSysId());
//                }
//                return dataVo;
//            }).toList();
//    }

//    /**
//     * 将MetaSolanaTransactionVo转换为RechargeDataVo
//     */
//    private RechargeDataVo convertToRechargeDataVo(MetaSolanaTransactionVo vo) {
//        RechargeDataVo dataVo = new RechargeDataVo();
//
//        // 基本字段转换
//        dataVo.setTxid(vo.getTxid());
//        dataVo.setId(vo.getId() != null ? BigInteger.valueOf(vo.getId()) : null);
//        dataVo.setBlockheight(vo.getBlockheight() != null ? BigInteger.valueOf(vo.getBlockheight()) : null);
//        dataVo.setAddress(vo.getAddress());
//        dataVo.setFromaddress(vo.getFromaddress());
//        dataVo.setContract(vo.getContract());
//        dataVo.setCoinNet("SOL");
//        dataVo.setAmount(vo.getAmount());
//        dataVo.setFee(vo.getFee());
//        dataVo.setDate(DateFormatUtils.format(new Date(vo.getTimestamp() * 1000), "yyyy-MM-dd HH:mm:ss"));
//
//        // 类型转换
//        dataVo.setType(vo.getType());
//        dataVo.setIssync(vo.getIssync() != null ? vo.getIssync().shortValue() : null);
//
//        return dataVo;
//    }

    /**
     * 查询Solana交易明细列表
     */
//    @SaCheckPermission("sol:solanaTransactions:list")
    @SaIgnore
    @GetMapping("/list")
    public TableDataInfo<MetaSolanaTransactionVo> list(MetaSolanaTransactionBo bo, PageQuery pageQuery) {
        return metaSolanaTransactionsService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出Solana交易明细列表
     */
    @SaCheckPermission("sol:solanaTransactions:export")
    @Log(title = "Solana交易明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MetaSolanaTransactionBo bo, HttpServletResponse response) {
        List<MetaSolanaTransactionVo> list = metaSolanaTransactionsService.queryList(bo);
        ExcelUtil.exportExcel(list, "Solana交易明细", MetaSolanaTransactionVo.class, response);
    }

    /**
     * 获取Solana交易明细详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("sol:solanaTransactions:query")
    @GetMapping("/{id}")
    public R<MetaSolanaTransactionVo> getInfo(@NotNull(message = "主键不能为空")
                                              @PathVariable Long id) {
        return R.ok(metaSolanaTransactionsService.queryById(id));
    }

    /**
     * 新增Solana交易明细
     */
    @SaCheckPermission("sol:solanaTransactions:add")
    @Log(title = "Solana交易明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MetaSolanaTransactionBo bo) {
        return toAjax(metaSolanaTransactionsService.insertByBo(bo));
    }

    /**
     * 修改Solana交易明细
     */
    @SaCheckPermission("sol:solanaTransactions:edit")
    @Log(title = "Solana交易明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MetaSolanaTransactionBo bo) {
        return toAjax(metaSolanaTransactionsService.updateByBo(bo));
    }

    /**
     * 删除Solana交易明细
     *
     * @param ids 主键串
     */
    @SaCheckPermission("sol:solanaTransactions:remove")
    @Log(title = "Solana交易明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(metaSolanaTransactionsService.deleteWithValidByIds(List.of(ids), true));
    }
}
