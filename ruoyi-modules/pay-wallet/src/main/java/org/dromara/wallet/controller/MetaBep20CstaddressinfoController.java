package org.dromara.wallet.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.wallet.domain.vo.MetaBep20CstaddressinfoVo;
import org.dromara.wallet.domain.bo.MetaBep20CstaddressinfoBo;
import org.dromara.wallet.service.IMetaBep20CstaddressinfoService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * ETH客户热钱包地址信息
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/wallet/bep20Cstaddressinfo")
public class MetaBep20CstaddressinfoController extends BaseController {

    private final IMetaBep20CstaddressinfoService metaBep20CstaddressinfoService;

    /**
     * 查询ETH客户热钱包地址信息列表
     */
    @SaCheckPermission("wallet:bep20Cstaddressinfo:list")
    @GetMapping("/list")
    public TableDataInfo<MetaBep20CstaddressinfoVo> list(MetaBep20CstaddressinfoBo bo, PageQuery pageQuery) {
        return metaBep20CstaddressinfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出ETH客户热钱包地址信息列表
     */
    @SaCheckPermission("wallet:bep20Cstaddressinfo:export")
    @Log(title = "ETH客户热钱包地址信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MetaBep20CstaddressinfoBo bo, HttpServletResponse response) {
        List<MetaBep20CstaddressinfoVo> list = metaBep20CstaddressinfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "ETH客户热钱包地址信息", MetaBep20CstaddressinfoVo.class, response);
    }

    /**
     * 获取ETH客户热钱包地址信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("wallet:bep20Cstaddressinfo:query")
    @GetMapping("/{id}")
    public R<MetaBep20CstaddressinfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(metaBep20CstaddressinfoService.queryById(id));
    }

    /**
     * 新增ETH客户热钱包地址信息
     */
    @SaCheckPermission("wallet:bep20Cstaddressinfo:add")
    @Log(title = "ETH客户热钱包地址信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MetaBep20CstaddressinfoBo bo) {
        return toAjax(metaBep20CstaddressinfoService.insertByBo(bo));
    }

    /**
     * 修改ETH客户热钱包地址信息
     */
    @SaCheckPermission("wallet:bep20Cstaddressinfo:edit")
    @Log(title = "ETH客户热钱包地址信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MetaBep20CstaddressinfoBo bo) {
        return toAjax(metaBep20CstaddressinfoService.updateByBo(bo));
    }

    /**
     * 删除ETH客户热钱包地址信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("wallet:bep20Cstaddressinfo:remove")
    @Log(title = "ETH客户热钱包地址信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(metaBep20CstaddressinfoService.deleteWithValidByIds(List.of(ids), true));
    }
}
