package org.dromara.wallet.wallet.transfer.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

/**
 * TRON转账监控指标收集器
 *
 * <p>收集和统计TRON转账相关的性能指标，包括：</p>
 * <ul>
 *   <li>转账成功率和失败率统计</li>
 *   <li>转账耗时和确认时间监控</li>
 *   <li>手续费使用情况统计</li>
 *   <li>异常类型分布统计</li>
 *   <li>实时性能指标计算</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
@Slf4j
@Component
public class TronTransferMetrics {

    // ==================== 基础统计指标 ====================

    /**
     * 总转账次数
     */
    private final AtomicLong totalTransfers = new AtomicLong(0);

    /**
     * 成功转账次数
     */
    private final AtomicLong successfulTransfers = new AtomicLong(0);

    /**
     * 失败转账次数
     */
    private final AtomicLong failedTransfers = new AtomicLong(0);

    /**
     * 总转账耗时（毫秒）
     */
    private final AtomicLong totalTransferTime = new AtomicLong(0);

    /**
     * 总确认耗时（毫秒）
     */
    private final AtomicLong totalConfirmationTime = new AtomicLong(0);

    /**
     * 总手续费消耗（TRX）
     */
    private final AtomicReference<BigDecimal> totalFeeConsumed = new AtomicReference<>(BigDecimal.ZERO);

    // ==================== 分类统计 ====================

    /**
     * TRX转账统计
     */
    private final AtomicLong trxTransfers = new AtomicLong(0);

    /**
     * TRC20转账统计
     */
    private final AtomicLong trc20Transfers = new AtomicLong(0);

    /**
     * 手续费提供次数
     */
    private final AtomicLong feeProvisionCount = new AtomicLong(0);

    /**
     * 异常类型统计
     */
    private final ConcurrentHashMap<String, AtomicInteger> exceptionTypeCount = new ConcurrentHashMap<>();

    // ==================== 性能指标 ====================

    /**
     * 最后更新时间
     */
    private final AtomicReference<LocalDateTime> lastUpdateTime = new AtomicReference<>(LocalDateTime.now());

    /**
     * 最快转账时间（毫秒）
     */
    private final AtomicLong fastestTransferTime = new AtomicLong(Long.MAX_VALUE);

    /**
     * 最慢转账时间（毫秒）
     */
    private final AtomicLong slowestTransferTime = new AtomicLong(0);

    // ==================== 指标记录方法 ====================

    /**
     * 记录转账开始
     */
    public void recordTransferStart(String transferType) {
        totalTransfers.incrementAndGet();
        
        if ("TRX".equalsIgnoreCase(transferType)) {
            trxTransfers.incrementAndGet();
        } else {
            trc20Transfers.incrementAndGet();
        }
        
        updateLastUpdateTime();
        log.debug("TRON转账开始记录: type={}, total={}", transferType, totalTransfers.get());
    }

    /**
     * 记录转账成功
     */
    public void recordTransferSuccess(long transferTimeMs, long confirmationTimeMs, 
                                    BigDecimal feeAmount, boolean feeProvided) {
        successfulTransfers.incrementAndGet();
        totalTransferTime.addAndGet(transferTimeMs);
        totalConfirmationTime.addAndGet(confirmationTimeMs);
        
        // 更新最快/最慢转账时间
        updateFastestTime(transferTimeMs);
        updateSlowestTime(transferTimeMs);
        
        // 记录手续费
        if (feeProvided && feeAmount != null) {
            feeProvisionCount.incrementAndGet();
            addToTotalFee(feeAmount);
        }
        
        updateLastUpdateTime();
        log.debug("TRON转账成功记录: time={}ms, confirmation={}ms, fee={}", 
                transferTimeMs, confirmationTimeMs, feeAmount);
    }

    /**
     * 记录转账失败
     */
    public void recordTransferFailure(String exceptionType, long transferTimeMs) {
        failedTransfers.incrementAndGet();
        totalTransferTime.addAndGet(transferTimeMs);
        
        // 统计异常类型
        exceptionTypeCount.computeIfAbsent(exceptionType, k -> new AtomicInteger(0)).incrementAndGet();
        
        updateLastUpdateTime();
        log.debug("TRON转账失败记录: exception={}, time={}ms", exceptionType, transferTimeMs);
    }

    /**
     * 记录手续费提供
     */
    public void recordFeeProvision(BigDecimal feeAmount, String provisionMethod) {
        feeProvisionCount.incrementAndGet();
        addToTotalFee(feeAmount);
        
        updateLastUpdateTime();
        log.debug("TRON手续费提供记录: amount={}, method={}", feeAmount, provisionMethod);
    }

    // ==================== 指标计算方法 ====================

    /**
     * 计算成功率（百分比）
     */
    public double getSuccessRate() {
        long total = totalTransfers.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) successfulTransfers.get() / total * 100.0;
    }

    /**
     * 计算失败率（百分比）
     */
    public double getFailureRate() {
        long total = totalTransfers.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) failedTransfers.get() / total * 100.0;
    }

    /**
     * 计算平均转账时间（毫秒）
     */
    public double getAverageTransferTime() {
        long total = totalTransfers.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) totalTransferTime.get() / total;
    }

    /**
     * 计算平均确认时间（毫秒）
     */
    public double getAverageConfirmationTime() {
        long successful = successfulTransfers.get();
        if (successful == 0) {
            return 0.0;
        }
        return (double) totalConfirmationTime.get() / successful;
    }

    /**
     * 计算平均手续费（TRX）
     */
    public BigDecimal getAverageFee() {
        long feeCount = feeProvisionCount.get();
        if (feeCount == 0) {
            return BigDecimal.ZERO;
        }
        return totalFeeConsumed.get().divide(BigDecimal.valueOf(feeCount), 6, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 获取手续费提供率（百分比）
     */
    public double getFeeProvisionRate() {
        long total = totalTransfers.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) feeProvisionCount.get() / total * 100.0;
    }

    // ==================== 监控报告方法 ====================

    /**
     * 生成监控报告
     */
    public TronTransferMonitorReport generateReport() {
        return TronTransferMonitorReport.builder()
                .totalTransfers(totalTransfers.get())
                .successfulTransfers(successfulTransfers.get())
                .failedTransfers(failedTransfers.get())
                .successRate(getSuccessRate())
                .failureRate(getFailureRate())
                .averageTransferTime(getAverageTransferTime())
                .averageConfirmationTime(getAverageConfirmationTime())
                .fastestTransferTime(fastestTransferTime.get() == Long.MAX_VALUE ? 0 : fastestTransferTime.get())
                .slowestTransferTime(slowestTransferTime.get())
                .trxTransfers(trxTransfers.get())
                .trc20Transfers(trc20Transfers.get())
                .feeProvisionCount(feeProvisionCount.get())
                .totalFeeConsumed(totalFeeConsumed.get())
                .averageFee(getAverageFee())
                .feeProvisionRate(getFeeProvisionRate())
                .exceptionTypeCount(new ConcurrentHashMap<>(exceptionTypeCount))
                .lastUpdateTime(lastUpdateTime.get())
                .build();
    }

    /**
     * 生成简要统计信息
     */
    public String generateSummary() {
        return String.format(
                "TRON转账统计: 总计=%d, 成功=%d(%.2f%%), 失败=%d(%.2f%%), " +
                "平均耗时=%.0fms, 平均确认=%.0fms, 手续费提供率=%.2f%%",
                totalTransfers.get(),
                successfulTransfers.get(), getSuccessRate(),
                failedTransfers.get(), getFailureRate(),
                getAverageTransferTime(),
                getAverageConfirmationTime(),
                getFeeProvisionRate()
        );
    }

    /**
     * 检查是否需要告警
     */
    public boolean shouldAlert() {
        // 失败率超过10%
        if (getFailureRate() > 10.0 && totalTransfers.get() >= 10) {
            return true;
        }
        
        // 平均转账时间超过30秒
        if (getAverageTransferTime() > 30000 && totalTransfers.get() >= 5) {
            return true;
        }
        
        // 最近没有成功转账（超过1小时）
        LocalDateTime lastUpdate = lastUpdateTime.get();
        if (lastUpdate.isBefore(LocalDateTime.now().minusHours(1)) && totalTransfers.get() > 0) {
            return true;
        }
        
        return false;
    }

    // ==================== 辅助方法 ====================

    private void updateFastestTime(long transferTime) {
        fastestTransferTime.updateAndGet(current -> Math.min(current, transferTime));
    }

    private void updateSlowestTime(long transferTime) {
        slowestTransferTime.updateAndGet(current -> Math.max(current, transferTime));
    }

    private void addToTotalFee(BigDecimal feeAmount) {
        totalFeeConsumed.updateAndGet(current -> current.add(feeAmount));
    }

    private void updateLastUpdateTime() {
        lastUpdateTime.set(LocalDateTime.now());
    }

    // ==================== 重置方法 ====================

    /**
     * 重置所有统计指标
     */
    public void reset() {
        totalTransfers.set(0);
        successfulTransfers.set(0);
        failedTransfers.set(0);
        totalTransferTime.set(0);
        totalConfirmationTime.set(0);
        totalFeeConsumed.set(BigDecimal.ZERO);
        trxTransfers.set(0);
        trc20Transfers.set(0);
        feeProvisionCount.set(0);
        fastestTransferTime.set(Long.MAX_VALUE);
        slowestTransferTime.set(0);
        exceptionTypeCount.clear();
        updateLastUpdateTime();
        
        log.info("TRON转账监控指标已重置");
    }

    // ==================== Getter 方法 ====================

    public long getTotalTransfers() { return totalTransfers.get(); }
    public long getSuccessfulTransfers() { return successfulTransfers.get(); }
    public long getFailedTransfers() { return failedTransfers.get(); }
    public long getTrxTransfers() { return trxTransfers.get(); }
    public long getTrc20Transfers() { return trc20Transfers.get(); }
    public long getFeeProvisionCount() { return feeProvisionCount.get(); }
    public BigDecimal getTotalFeeConsumed() { return totalFeeConsumed.get(); }
    public LocalDateTime getLastUpdateTime() { return lastUpdateTime.get(); }
}
