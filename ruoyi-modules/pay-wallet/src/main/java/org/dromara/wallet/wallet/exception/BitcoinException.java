package org.dromara.wallet.wallet.exception;

import java.io.Serial;

/**
 * 比特币链异常类
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
public class BitcoinException extends WalletException {

    @Serial
    private static final long serialVersionUID = 1L;

    public BitcoinException(String code, Object... args) {
        super("wallet.bitcoin." + code, args);
    }

    public BitcoinException(String defaultMessage) {
        super(defaultMessage);
    }

    public BitcoinException(String code, Object[] args, String defaultMessage) {
        super("wallet.bitcoin." + code, args, defaultMessage);
    }

    /**
     * UTXO不足异常
     */
    public static BitcoinException insufficientUtxo() {
        return new BitcoinException("UTXO不足，无法完成交易");
    }

    /**
     * 交易费不足异常
     */
    public static BitcoinException insufficientFee() {
        return new BitcoinException("交易费不足");
    }

    /**
     * 交易失败异常
     */
    public static BitcoinException transactionFailed(String reason) {
        return new BitcoinException("比特币交易失败: " + reason);
    }

    /**
     * 网络连接异常
     */
    public static BitcoinException networkError(String reason) {
        return new BitcoinException("比特币网络连接异常: " + reason);
    }
}
