package org.dromara.wallet.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.facade.BscConfigFacade;
import org.dromara.wallet.config.facade.SolanaConfigFacade;
import org.dromara.wallet.config.facade.TronConfigFacade;
import org.dromara.wallet.domain.MetaBep20Cstaddressinfo;
import org.dromara.wallet.domain.MetaSolanaCstaddressinfo;
import org.dromara.wallet.domain.MetaTrc20Cstaddressinfo;
import org.dromara.wallet.domain.dto.MetaMainAddress;
import org.dromara.wallet.domain.dto.WalletTransferBo;
import org.dromara.wallet.service.IMetaBep20CstaddressinfoService;
import org.dromara.wallet.service.IMetaSolanaCstaddressinfoService;
import org.dromara.wallet.service.IMetaTrc20CstaddressinfoService;
import org.dromara.wallet.service.IWalletTransferService;
import org.dromara.wallet.wallet.transfer.dto.TransferRequest;
import org.dromara.wallet.wallet.transfer.dto.UnifiedTransferResult;
import org.dromara.wallet.wallet.transfer.service.UnifiedTransferService;
import org.springframework.stereotype.Service;

/**
 * 钱包转账服务实现类
 * 提供统一的转账和归集功能
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WalletTransferServiceImpl implements IWalletTransferService {

    private final BscConfigFacade bscConfigFacade;
    private final TronConfigFacade tronConfigFacade;
    private final SolanaConfigFacade solanaConfigFacade;
    private final IMetaSolanaCstaddressinfoService metaSolanaCstaddressinfoService;
    private final IMetaBep20CstaddressinfoService metaBep20CstaddressinfoService;
    private final IMetaTrc20CstaddressinfoService metaTrc20CstaddressinfoService;
    private final UnifiedTransferService unifiedTransferService;

    @Override
    public UnifiedTransferResult executeTransfer(WalletTransferBo transferBo) {
        // 验证基础参数
        validateBasicParameters(transferBo);

        // 获取目标地址
        String targetAddress = determineTargetAddress(transferBo);

        // 获取私钥
        String fromPrivateKey = getPrivateKeyFromDatabase(transferBo);

        // 构建转账请求
        TransferRequest request = buildTransferRequest(transferBo, targetAddress, fromPrivateKey);

        // 执行转账
        return unifiedTransferService.transfer(request);
    }

    /**
     * 验证基础参数
     */
    private void validateBasicParameters(WalletTransferBo transferBo) {
        if (transferBo == null) {
            throw new IllegalArgumentException("转账参数不能为空");
        }
        if (transferBo.getFromAddress() == null || transferBo.getFromAddress().trim().isEmpty()) {
            throw new IllegalArgumentException("发送方地址不能为空");
        }
        if (transferBo.getAmount() == null || transferBo.getAmount().compareTo(java.math.BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("转账金额必须大于0");
        }
        if (transferBo.getTokenSymbol() == null || transferBo.getTokenSymbol().trim().isEmpty()) {
            throw new IllegalArgumentException("代币符号不能为空");
        }
        if (transferBo.getChainType() == null) {
            throw new IllegalArgumentException("区块链类型不能为空");
        }
    }

    /**
     * 确定目标地址
     * 如果toAddress为空，从配置获取主地址；否则使用指定地址
     */
    private String determineTargetAddress(WalletTransferBo transferBo) {
        if (transferBo.getToAddress() != null && !transferBo.getToAddress().trim().isEmpty()) {
            // 用户指定的目标地址
            return transferBo.getToAddress().trim();
        }

        // 从配置获取主地址
        MetaMainAddress mainAddress;
        switch (transferBo.getChainType()) {
            case TRON -> mainAddress = tronConfigFacade.getWalletConfig().getMainAddress();
            case BSC, ARB, BASE -> mainAddress = bscConfigFacade.getWalletConfig().getMainAddress();
            case SOLANA -> mainAddress = solanaConfigFacade.getWalletConfig().getMainAddress();
            default -> throw new IllegalArgumentException("不支持的区块链类型: " + transferBo.getChainType());
        }

        if (mainAddress == null || mainAddress.getAddress() == null || mainAddress.getAddress().trim().isEmpty()) {
            throw new IllegalStateException("无法获取" + transferBo.getChainType() + "的主地址配置");
        }

        return mainAddress.getAddress();
    }

    /**
     * 从数据库获取私钥
     */
    private String getPrivateKeyFromDatabase(WalletTransferBo transferBo) {
        String fromPrivateKey;
        switch (transferBo.getChainType()) {
            case TRON -> {
                MetaTrc20Cstaddressinfo walletInfo = metaTrc20CstaddressinfoService.queryByAddress(transferBo.getFromAddress());
                if (walletInfo == null) {
                    throw new IllegalArgumentException("未找到TRON地址对应的钱包信息: " + transferBo.getFromAddress());
                }
                fromPrivateKey = walletInfo.getCstTrc20private();
            }
            case BSC, ARB, BASE -> {
                MetaBep20Cstaddressinfo walletInfo = metaBep20CstaddressinfoService.queryByAddress(transferBo.getFromAddress());
                if (walletInfo == null) {
                    throw new IllegalArgumentException("未找到EVM地址对应的钱包信息: " + transferBo.getFromAddress());
                }
                fromPrivateKey = walletInfo.getCstTrc20private();
            }
            case SOLANA -> {
                MetaSolanaCstaddressinfo walletInfo = metaSolanaCstaddressinfoService.queryByAddress(transferBo.getFromAddress());
                if (walletInfo == null) {
                    throw new IllegalArgumentException("未找到Solana地址对应的钱包信息: " + transferBo.getFromAddress());
                }
                fromPrivateKey = walletInfo.getCstPrivate();
            }
            default -> throw new IllegalArgumentException("不支持的区块链类型: " + transferBo.getChainType());
        }

        if (fromPrivateKey == null || fromPrivateKey.trim().isEmpty()) {
            throw new IllegalStateException("获取到的私钥为空: " + transferBo.getFromAddress());
        }

        return fromPrivateKey;
    }

    /**
     * 构建转账请求
     */
    private TransferRequest buildTransferRequest(WalletTransferBo transferBo, String targetAddress, String fromPrivateKey) {
        return TransferRequest.builder()
            .privateKey(fromPrivateKey)
            .toAddress(targetAddress)
            .amount(transferBo.getAmount().toString())
            .tokenSymbol(transferBo.getTokenSymbol())
            .chainName(transferBo.getChainType().getCode())
            .enableFeeWallet(true)
            .waitForConfirmation(true)
            .memo(transferBo.getMemo() != null ? transferBo.getMemo() : "钱包转账操作")
            .syncMode(true)
            .build();
    }
}
