package org.dromara.wallet.wallet.transfer.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

/**
 * EVM转账监控指标收集器
 *
 * <p>收集和统计EVM兼容链转账相关的性能指标，包括：</p>
 * <ul>
 *   <li>转账成功率和失败率统计</li>
 *   <li>转账耗时和确认时间监控</li>
 *   <li>Gas费用使用情况统计</li>
 *   <li>异常类型分布统计</li>
 *   <li>多链分别统计</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
@Slf4j
@Component
public class EvmTransferMetrics {

    // ==================== 基础统计指标 ====================

    /**
     * 总转账次数
     */
    private final AtomicLong totalTransfers = new AtomicLong(0);

    /**
     * 成功转账次数
     */
    private final AtomicLong successfulTransfers = new AtomicLong(0);

    /**
     * 失败转账次数
     */
    private final AtomicLong failedTransfers = new AtomicLong(0);

    /**
     * 总转账耗时（毫秒）
     */
    private final AtomicLong totalTransferTime = new AtomicLong(0);

    /**
     * 总确认耗时（毫秒）
     */
    private final AtomicLong totalConfirmationTime = new AtomicLong(0);

    /**
     * 总Gas费消耗（ETH单位）
     */
    private final AtomicReference<BigDecimal> totalGasConsumed = new AtomicReference<>(BigDecimal.ZERO);

    // ==================== 分类统计 ====================

    /**
     * 原生代币转账统计
     */
    private final AtomicLong nativeTransfers = new AtomicLong(0);

    /**
     * ERC20代币转账统计
     */
    private final AtomicLong tokenTransfers = new AtomicLong(0);

    /**
     * Gas费提供次数
     */
    private final AtomicLong gasProvisionCount = new AtomicLong(0);

    /**
     * 异常类型统计
     */
    private final ConcurrentHashMap<String, AtomicInteger> exceptionTypeCount = new ConcurrentHashMap<>();

    /**
     * 各链转账统计
     */
    private final ConcurrentHashMap<String, AtomicLong> chainTransferCount = new ConcurrentHashMap<>();

    // ==================== 性能指标 ====================

    /**
     * 最后更新时间
     */
    private final AtomicReference<LocalDateTime> lastUpdateTime = new AtomicReference<>(LocalDateTime.now());

    /**
     * 最快转账时间（毫秒）
     */
    private final AtomicLong fastestTransferTime = new AtomicLong(Long.MAX_VALUE);

    /**
     * 最慢转账时间（毫秒）
     */
    private final AtomicLong slowestTransferTime = new AtomicLong(0);

    // ==================== 指标记录方法 ====================

    /**
     * 记录转账开始
     */
    public void recordTransferStart(String chainName, String transferType) {
        totalTransfers.incrementAndGet();
        
        // 记录链统计
        chainTransferCount.computeIfAbsent(chainName, k -> new AtomicLong(0)).incrementAndGet();
        
        // 记录转账类型
        if (isNativeTransfer(transferType)) {
            nativeTransfers.incrementAndGet();
        } else {
            tokenTransfers.incrementAndGet();
        }
        
        updateLastUpdateTime();
        log.debug("EVM转账开始记录: chain={}, type={}, total={}", chainName, transferType, totalTransfers.get());
    }

    /**
     * 记录转账成功
     */
    public void recordTransferSuccess(String chainName, long transferTime, long confirmationTime, 
                                    BigDecimal gasAmount, boolean gasProvided) {
        successfulTransfers.incrementAndGet();
        totalTransferTime.addAndGet(transferTime);
        totalConfirmationTime.addAndGet(confirmationTime);
        
        // 更新最快/最慢转账时间
        updateFastestTime(transferTime);
        updateSlowestTime(transferTime);
        
        // 记录Gas费
        if (gasProvided && gasAmount != null) {
            gasProvisionCount.incrementAndGet();
            addToTotalGas(gasAmount);
        }
        
        updateLastUpdateTime();
        log.debug("EVM转账成功记录: chain={}, time={}ms, confirmation={}ms, gas={}", 
                chainName, transferTime, confirmationTime, gasAmount);
    }

    /**
     * 记录转账失败
     */
    public void recordTransferFailure(String chainName, String exceptionType, long transferTime) {
        failedTransfers.incrementAndGet();
        totalTransferTime.addAndGet(transferTime);
        
        // 统计异常类型
        String key = chainName + "_" + exceptionType;
        exceptionTypeCount.computeIfAbsent(key, k -> new AtomicInteger(0)).incrementAndGet();
        
        updateLastUpdateTime();
        log.debug("EVM转账失败记录: chain={}, exception={}, time={}ms", chainName, exceptionType, transferTime);
    }

    /**
     * 记录Gas费提供
     */
    public void recordGasProvision(String chainName, BigDecimal gasAmount, String provisionMethod) {
        gasProvisionCount.incrementAndGet();
        addToTotalGas(gasAmount);
        
        updateLastUpdateTime();
        log.debug("EVM Gas费提供记录: chain={}, amount={}, method={}", chainName, gasAmount, provisionMethod);
    }

    // ==================== 指标计算方法 ====================

    /**
     * 计算成功率（百分比）
     */
    public double getSuccessRate() {
        long total = totalTransfers.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) successfulTransfers.get() / total * 100.0;
    }

    /**
     * 计算失败率（百分比）
     */
    public double getFailureRate() {
        long total = totalTransfers.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) failedTransfers.get() / total * 100.0;
    }

    /**
     * 计算平均转账时间（毫秒）
     */
    public double getAverageTransferTime() {
        long total = totalTransfers.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) totalTransferTime.get() / total;
    }

    /**
     * 计算平均确认时间（毫秒）
     */
    public double getAverageConfirmationTime() {
        long successful = successfulTransfers.get();
        if (successful == 0) {
            return 0.0;
        }
        return (double) totalConfirmationTime.get() / successful;
    }

    /**
     * 计算平均Gas费（ETH）
     */
    public BigDecimal getAverageGas() {
        long gasCount = gasProvisionCount.get();
        if (gasCount == 0) {
            return BigDecimal.ZERO;
        }
        return totalGasConsumed.get().divide(BigDecimal.valueOf(gasCount), 6, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 获取Gas费提供率（百分比）
     */
    public double getGasProvisionRate() {
        long total = totalTransfers.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) gasProvisionCount.get() / total * 100.0;
    }

    /**
     * 获取指定链的转账次数
     */
    public long getChainTransferCount(String chainName) {
        AtomicLong count = chainTransferCount.get(chainName);
        return count != null ? count.get() : 0;
    }

    // ==================== 监控报告方法 ====================

    /**
     * 生成监控报告
     */
    public EvmTransferMonitorReport generateReport() {
        return EvmTransferMonitorReport.builder()
                .totalTransfers(totalTransfers.get())
                .successfulTransfers(successfulTransfers.get())
                .failedTransfers(failedTransfers.get())
                .successRate(getSuccessRate())
                .failureRate(getFailureRate())
                .averageTransferTime(getAverageTransferTime())
                .averageConfirmationTime(getAverageConfirmationTime())
                .fastestTransferTime(fastestTransferTime.get() == Long.MAX_VALUE ? 0 : fastestTransferTime.get())
                .slowestTransferTime(slowestTransferTime.get())
                .nativeTransfers(nativeTransfers.get())
                .tokenTransfers(tokenTransfers.get())
                .gasProvisionCount(gasProvisionCount.get())
                .totalGasConsumed(totalGasConsumed.get())
                .averageGas(getAverageGas())
                .gasProvisionRate(getGasProvisionRate())
                .chainTransferCount(new ConcurrentHashMap<>(chainTransferCount))
                .exceptionTypeCount(new ConcurrentHashMap<>(exceptionTypeCount))
                .lastUpdateTime(lastUpdateTime.get())
                .build();
    }

    /**
     * 生成简要统计信息
     */
    public String generateSummary() {
        return String.format(
                "EVM转账统计: 总计=%d, 成功=%d(%.2f%%), 失败=%d(%.2f%%), " +
                "平均耗时=%.0fms, 平均确认=%.0fms, Gas提供率=%.2f%%",
                totalTransfers.get(),
                successfulTransfers.get(), getSuccessRate(),
                failedTransfers.get(), getFailureRate(),
                getAverageTransferTime(),
                getAverageConfirmationTime(),
                getGasProvisionRate()
        );
    }

    /**
     * 检查是否需要告警
     */
    public boolean shouldAlert() {
        // 失败率超过10%
        if (getFailureRate() > 10.0 && totalTransfers.get() >= 10) {
            return true;
        }
        
        // 平均转账时间超过30秒
        if (getAverageTransferTime() > 30000 && totalTransfers.get() >= 5) {
            return true;
        }
        
        // 最近没有成功转账（超过1小时）
        LocalDateTime lastUpdate = lastUpdateTime.get();
        if (lastUpdate.isBefore(LocalDateTime.now().minusHours(1)) && totalTransfers.get() > 0) {
            return true;
        }
        
        return false;
    }

    // ==================== 辅助方法 ====================

    private boolean isNativeTransfer(String transferType) {
        return transferType != null && (
            transferType.contains("ETH") || 
            transferType.contains("BNB") || 
            transferType.contains("原生") ||
            transferType.contains("native")
        );
    }

    private void updateFastestTime(long transferTime) {
        fastestTransferTime.updateAndGet(current -> Math.min(current, transferTime));
    }

    private void updateSlowestTime(long transferTime) {
        slowestTransferTime.updateAndGet(current -> Math.max(current, transferTime));
    }

    private void addToTotalGas(BigDecimal gasAmount) {
        totalGasConsumed.updateAndGet(current -> current.add(gasAmount));
    }

    private void updateLastUpdateTime() {
        lastUpdateTime.set(LocalDateTime.now());
    }

    // ==================== 重置方法 ====================

    /**
     * 重置所有统计指标
     */
    public void reset() {
        totalTransfers.set(0);
        successfulTransfers.set(0);
        failedTransfers.set(0);
        totalTransferTime.set(0);
        totalConfirmationTime.set(0);
        totalGasConsumed.set(BigDecimal.ZERO);
        nativeTransfers.set(0);
        tokenTransfers.set(0);
        gasProvisionCount.set(0);
        fastestTransferTime.set(Long.MAX_VALUE);
        slowestTransferTime.set(0);
        chainTransferCount.clear();
        exceptionTypeCount.clear();
        updateLastUpdateTime();
        
        log.info("EVM转账监控指标已重置");
    }

    // ==================== Getter 方法 ====================

    public long getTotalTransfers() { return totalTransfers.get(); }
    public long getSuccessfulTransfers() { return successfulTransfers.get(); }
    public long getFailedTransfers() { return failedTransfers.get(); }
    public long getNativeTransfers() { return nativeTransfers.get(); }
    public long getTokenTransfers() { return tokenTransfers.get(); }
    public long getGasProvisionCount() { return gasProvisionCount.get(); }
    public BigDecimal getTotalGasConsumed() { return totalGasConsumed.get(); }
    public LocalDateTime getLastUpdateTime() { return lastUpdateTime.get(); }
    public ConcurrentHashMap<String, AtomicLong> getChainTransferCount() { return chainTransferCount; }
}
