package org.dromara.wallet.wallet.exception;

import java.io.Serial;

/**
 * TRON链异常类
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
public class TronException extends WalletException {

    @Serial
    private static final long serialVersionUID = 1L;

    public TronException(String code, Object... args) {
        super("wallet.tron." + code, args);
    }

    public TronException(String defaultMessage) {
        super(defaultMessage);
    }

    public TronException(String code, Object[] args, String defaultMessage) {
        super("wallet.tron." + code, args, defaultMessage);
    }
}
