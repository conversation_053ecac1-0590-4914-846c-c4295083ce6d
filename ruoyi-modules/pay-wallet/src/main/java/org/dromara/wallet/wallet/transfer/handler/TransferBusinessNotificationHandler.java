package org.dromara.wallet.wallet.transfer.handler;

import cn.dev33.satoken.context.mock.SaTokenContextMockUtil;
import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.ChainType;
import org.dromara.wallet.service.IWalletCoinRecService;
import org.dromara.wallet.wallet.monitor.event.BalanceChangeEvent;
import org.dromara.wallet.wallet.transfer.event.TransferCompletionEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;


/**
 * 转账业务通知处理器
 *
 * <p>监听转账完成事件，执行相应的业务逻辑处理</p>
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>异步处理转账完成事件</li>
 *   <li>支持不同转账类型的业务路由</li>
 *   <li>提供业务逻辑扩展点</li>
 *   <li>与转账确认流程解耦</li>
 * </ul>
 *
 * <p>使用场景：</p>
 * <ul>
 *   <li>提现完成后更新业务系统状态</li>
 *   <li>充值确认后触发业务逻辑</li>
 *   <li>归集完成后的后续处理</li>
 *   <li>转账失败后的业务补偿</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TransferBusinessNotificationHandler {

    /**
     * 提款业务服务（可选依赖）
     * 暂时注释掉，避免编译错误
     */
    // @DubboReference
    // private final RemoteWithdrawalBusinessService remoteWithdrawalBusinessService;

    /**
     * 事件发布器
     * 用于发布余额变动事件
     */
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 处理转账完成事件
     *
     * @param event 转账完成事件
     */
    @EventListener
    @Async
    public void handleTransferCompletion(TransferCompletionEvent event) {
        SaTokenContextMockUtil.setMockContext(() -> {
            StpUtil.setTokenValueToStorage(event.getTokenValue());
            System.out.println("是否登录：" + StpUtil.isLogin());

            if (!event.isValid()) {
                log.warn("收到无效的转账完成事件: {}", event.getFormattedInfo());
                return;
            }

            log.info("开始处理转账完成业务通知: {}", event.getFormattedInfo());

            try {
                if (event.isSuccess()) {
                    // 处理转账成功的业务逻辑
                    handleSuccessfulTransfer(event);
                } else {
                    // 处理转账失败的业务逻辑
                    handleFailedTransfer(event);
                }

                log.info("转账完成业务通知处理成功: {}", event.getFormattedInfo());

            } catch (Exception e) {
                log.error("转账完成业务通知处理失败: {}, 错误: {}",
                    event.getFormattedInfo(), e.getMessage(), e);
                // 注意：业务通知处理失败不影响转账确认结果
            }
        });

    }

    /**
     * 处理转账成功的业务逻辑
     *
     * @param event 转账完成事件
     */
    private void handleSuccessfulTransfer(TransferCompletionEvent event) {
        log.info("{}链{}转账成功，开始业务处理: requestId={}, txHash={}, confirmations={}",
            event.getChainName(), event.getTransferType(), event.getRequestId(),
            event.getTransactionHash(), event.getConfirmations());

        try {
            // 1. 触发自动余额刷新
            triggerBalanceRefresh(event);

            // 2. 根据转账类型进行不同的业务处理
            if (event.isWithdrawal()) {
                handleWithdrawalSuccess(event);
            } else if (event.isDeposit()) {
                handleDepositSuccess(event);
            } else if (event.isCollection()) {
                handleCollectionSuccess(event);
            } else {
                handleOtherTransferSuccess(event);
            }
        } catch (Exception e) {
            log.error("转账成功业务处理异常: {}, 错误: {}", event.getFormattedInfo(), e.getMessage(), e);
            // 业务处理异常不影响转账确认结果
        }
    }

    /**
     * 触发余额刷新
     *
     * @param event 转账完成事件
     */
    private void triggerBalanceRefresh(TransferCompletionEvent event) {
        try {
            log.debug("触发转账完成后余额刷新: {}", event.getFormattedInfo());

            // 将链名称转换为ChainType枚举
            ChainType chainType = ChainType.fromCode(event.getChainName().toLowerCase());

            // 发布余额变动事件
            BalanceChangeEvent balanceChangeEvent = new BalanceChangeEvent(
                this, chainType, event.getFromAddress(), null
            );
            eventPublisher.publishEvent(balanceChangeEvent);

            log.debug("余额变动事件发布成功: chain={}, address={}",
                event.getChainName(), event.getFromAddress());

        } catch (Exception e) {
            log.error("触发余额刷新失败: {}, 错误: {}", event.getFormattedInfo(), e.getMessage(), e);
            // 余额刷新失败不影响主业务流程
        }
    }

    /**
     * 处理转账失败的业务逻辑
     *
     * @param event 转账完成事件
     */
    private void handleFailedTransfer(TransferCompletionEvent event) {
        log.warn("{}链{}转账失败，开始业务处理: requestId={}, txHash={}, error={}",
            event.getChainName(), event.getTransferType(), event.getRequestId(),
            event.getTransactionHash(), event.getErrorMessage());

        // 根据转账类型进行不同的失败处理
        if (event.isWithdrawal()) {
            handleWithdrawalFailure(event);
        } else if (event.isDeposit()) {
            handleDepositFailure(event);
        } else if (event.isCollection()) {
            handleCollectionFailure(event);
        } else {
            handleOtherTransferFailure(event);
        }
    }

    // ==================== 提现业务处理 ====================

    /**
     * 处理提现成功
     */
    private void handleWithdrawalSuccess(TransferCompletionEvent event) {
        log.info("提现成功业务处理: requestId={}, txHash={}",
            event.getRequestId(), event.getTransactionHash());

        // 调用资产模块的提款业务服务
        // 暂时注释掉，避免编译错误
        /*
        if (remoteWithdrawalBusinessService != null) {
            try {
                boolean success = remoteWithdrawalBusinessService.processWithdrawalCompletion(
                    event.getRequestId(),
                    event.getTransactionHash(),
                    event.getChainName(),
                    event.getCoinType(),
                    event.getConfirmations(),
                    new BigDecimal(event.getAmount()),
                    event.getTenantId()
                );

                if (success) {
                    log.info("提款业务处理成功: requestId={}", event.getRequestId());
                } else {
                    log.warn("提款业务处理失败: requestId={}", event.getRequestId());
                }
            } catch (Exception e) {
                log.error("提款业务处理异常: requestId={}, error={}",
                    event.getRequestId(), e.getMessage(), e);
            }
        } else {
            log.debug("未找到提款业务服务实现，跳过提款业务处理");
        }
        */
        log.debug("提款业务处理已暂时禁用");
    }

    /**
     * 处理提现失败
     */
    private void handleWithdrawalFailure(TransferCompletionEvent event) {
        log.warn("提现失败业务处理: requestId={}, error={}",
            event.getRequestId(), event.getErrorMessage());

        // TODO: 实现提现失败的业务逻辑
        // 例如：
        // 1. 回滚用户余额
        // 2. 发送失败通知
        // 3. 记录失败原因
        // 4. 触发补偿机制
    }

    // ==================== 充值业务处理 ====================

    /**
     * 处理充值成功
     */
    private void handleDepositSuccess(TransferCompletionEvent event) {
        log.info("充值成功业务处理: requestId={}, txHash={}",
            event.getRequestId(), event.getTransactionHash());

        // TODO: 实现充值成功的业务逻辑
        // 例如：
        // 1. 增加用户余额
        // 2. 发送成功通知
        // 3. 记录充值记录
        // 4. 触发奖励机制
    }

    /**
     * 处理充值失败
     */
    private void handleDepositFailure(TransferCompletionEvent event) {
        log.warn("充值失败业务处理: requestId={}, error={}",
            event.getRequestId(), event.getErrorMessage());

        // TODO: 实现充值失败的业务逻辑
        // 例如：
        // 1. 记录失败原因
        // 2. 发送失败通知
        // 3. 触发人工审核
    }

    // ==================== 归集业务处理 ====================

    /**
     * 处理归集成功
     */
    private void handleCollectionSuccess(TransferCompletionEvent event) {
        log.info("归集成功业务处理: requestId={}, txHash={}",
            event.getRequestId(), event.getTransactionHash());

        // TODO: 实现归集成功的业务逻辑
        // 例如：
        // 1. 更新钱包状态
        // 2. 记录归集记录
        // 3. 触发下一轮归集
    }

    /**
     * 处理归集失败
     */
    private void handleCollectionFailure(TransferCompletionEvent event) {
        log.warn("归集失败业务处理: requestId={}, error={}",
            event.getRequestId(), event.getErrorMessage());

        // TODO: 实现归集失败的业务逻辑
        // 例如：
        // 1. 记录失败原因
        // 2. 触发重试机制
        // 3. 发送告警通知
    }

    // ==================== 其他转账业务处理 ====================

    /**
     * 处理其他类型转账成功
     */
    private void handleOtherTransferSuccess(TransferCompletionEvent event) {
        log.info("其他转账成功业务处理: type={}, requestId={}, txHash={}",
            event.getTransferType(), event.getRequestId(), event.getTransactionHash());

        // TODO: 实现其他转账成功的业务逻辑
    }

    /**
     * 处理其他类型转账失败
     */
    private void handleOtherTransferFailure(TransferCompletionEvent event) {
        log.warn("其他转账失败业务处理: type={}, requestId={}, error={}",
            event.getTransferType(), event.getRequestId(), event.getErrorMessage());

        // TODO: 实现其他转账失败的业务逻辑
    }
}
