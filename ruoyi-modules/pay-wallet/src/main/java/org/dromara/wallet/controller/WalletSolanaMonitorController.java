package org.dromara.wallet.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.wallet.domain.vo.MonitorStatusVo;
import org.dromara.wallet.domain.vo.MonitorSummaryVo;
import org.dromara.wallet.domain.vo.SubscriptionInfoVo;
import org.dromara.wallet.wallet.monitor.solana.SolanaMonitorManager;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Solana监控管理控制器
 * 提供监控状态查询、订阅管理等功能
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/solana/monitor")
@RequiredArgsConstructor
public class WalletSolanaMonitorController {

    private final SolanaMonitorManager solanaMonitorManager;

    /**
     * 获取监控状态
     *
     * @return 监控状态信息
     */
    @GetMapping("/status")
    public R<MonitorStatusVo> getMonitorStatus() {
        try {
            MonitorStatusVo status = solanaMonitorManager.getMonitorStatus();
            return R.ok(status);
        } catch (Exception e) {
            log.error("获取监控状态失败: {}", e.getMessage());
            return R.fail("获取监控状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取订阅信息列表
     *
     * @return 订阅信息列表
     */
    @GetMapping("/subscriptions")
    public R<List<SubscriptionInfoVo>> getSubscriptions() {
        try {
            List<SubscriptionInfoVo> subscriptions = solanaMonitorManager.getSubscriptions();
            return R.ok(subscriptions);
        } catch (Exception e) {
            log.error("获取订阅信息失败: {}", e.getMessage());
            return R.fail("获取订阅信息失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发重连
     *
     * @return 重连结果
     */
    @PostMapping("/reconnect")
    public R<String> triggerReconnect() {
        try {
            boolean success = solanaMonitorManager.triggerReconnect();
            if (success) {
                return R.ok("重连请求已提交，正在后台执行");
            } else {
                return R.fail("重连请求失败，可能已有重连在进行中");
            }
        } catch (Exception e) {
            log.error("触发重连失败: {}", e.getMessage());
            return R.fail("触发重连失败: " + e.getMessage());
        }
    }

    /**
     * 获取监控统计摘要
     *
     * @return 监控摘要信息
     */
    @GetMapping("/summary")
    public R<MonitorSummaryVo> getMonitorSummary() {
        try {
            MonitorStatusVo status = solanaMonitorManager.getMonitorStatus();

            // 构建摘要信息
            MonitorSummaryVo summary = MonitorSummaryVo.builder()
                .connected(status.getConnected())
                .enabled(status.getMonitorEnabled())
                .totalAddresses(status.getTotalAddresses())
                .subscribedAddresses(status.getSubscribedAddresses())
                .reconnectAttempts(status.getReconnectAttempts())
                .totalTransactions(status.getTransactionStats() != null ?
                    status.getTransactionStats().getTotalTransactions() : 0L)
                .todayTransactions(status.getTransactionStats() != null ?
                    status.getTransactionStats().getTodayTransactions() : 0L)
                .build();

            return R.ok(summary);
        } catch (Exception e) {
            log.error("获取监控摘要失败: {}", e.getMessage());
            return R.fail("获取监控摘要失败: " + e.getMessage());
        }
    }
}
