package org.dromara.wallet.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.wallet.config.WalletPrivateKeyProperties;
import org.dromara.wallet.utils.WalletPrivateKeyUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.HashMap;
import java.util.Map;

/**
 * 私钥控制层
 *
 * <AUTHOR>
 * @date 2025/7/7 09:49
 **/
@Slf4j
@Controller("/privateKey")
@RequiredArgsConstructor
public class WalletPrivateKeyController {

    private final WalletPrivateKeyProperties walletPrivateKeyProperties;

    /**
     * 解密私钥
     * 传入密文，返回解密后的明文数据
     *
     * @param encryptedData 密文
     * @return 解密后的明文数据
     */
    @PostMapping("/decrypt")
    public R<Map<String, Object>> decryptPrivateKey(@RequestParam String encryptedData) {
        try {
            // 参数验证
            if (encryptedData == null || encryptedData.trim().isEmpty()) {
                return R.fail("密文不能为空");
            }

            // 执行解密
            String decryptedData = WalletPrivateKeyUtil.decryptPrivateKey(encryptedData.trim());

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("encryptedData", encryptedData);
            result.put("decryptedData", decryptedData);
            result.put("algorithm", "AES-GCM");
            result.put("success", true);

            log.info("私钥解密成功，密文长度: {}", encryptedData.length());
            return R.ok("解密成功", result);

        } catch (Exception e) {
            log.error("私钥解密失败，密文: {}", encryptedData, e);

            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("encryptedData", encryptedData);
            errorResult.put("error", e.getMessage());
            errorResult.put("success", false);

            return R.fail("解密失败: " + e.getMessage(), errorResult);
        }
    }

    /**
     * 智能解密私钥（推荐使用）
     * 自动判断是否需要解密，如果是明文则直接返回
     *
     * @param data 可能是加密或未加密的数据
     * @return 解密后的明文数据
     */
    @PostMapping("/smartDecrypt")
    public R<Map<String, Object>> smartDecrypt(@RequestParam String data) {
        try {
            // 参数验证
            if (data == null || data.trim().isEmpty()) {
                return R.fail("数据不能为空");
            }

            String trimmedData = data.trim();

            // 检查是否为密文
            boolean isEncrypted = WalletPrivateKeyUtil.isEncrypted(trimmedData);

            // 智能处理
            String resultData = WalletPrivateKeyUtil.getDecryptedPrivateKey(trimmedData);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("originalData", data);
            result.put("isCiphertext", isEncrypted);
            result.put("resultData", resultData);
            result.put("algorithm", "AES-GCM");
            result.put("processed", isEncrypted ? "解密" : "直接返回");
            result.put("success", true);

            log.info("智能解密处理完成，是否为密文: {}, 数据长度: {}", isEncrypted, data.length());
            return R.ok("处理成功", result);

        } catch (Exception e) {
            log.error("智能解密失败，数据: {}", data, e);

            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("originalData", data);
            errorResult.put("error", e.getMessage());
            errorResult.put("success", false);

            return R.fail("处理失败: " + e.getMessage(), errorResult);
        }
    }

    /**
     * 批量解密私钥
     *
     * @param encryptedDataList 密文列表
     * @return 解密结果列表
     */
    @PostMapping("/batchDecrypt")
    public R<Map<String, Object>> batchDecrypt(@RequestBody String[] encryptedDataList) {
        try {
            if (encryptedDataList == null || encryptedDataList.length == 0) {
                return R.fail("密文列表不能为空");
            }

            Map<String, Object> results = new HashMap<>();
            Map<String, String> successResults = new HashMap<>();
            Map<String, String> errorResults = new HashMap<>();

            for (String encryptedData : encryptedDataList) {
                if (encryptedData != null && !encryptedData.trim().isEmpty()) {
                    try {
                        String decrypted = WalletPrivateKeyUtil.getDecryptedPrivateKey(encryptedData.trim());
                        successResults.put(encryptedData, decrypted);
                    } catch (Exception e) {
                        errorResults.put(encryptedData, e.getMessage());
                        log.warn("批量解密中单个数据失败: {}", encryptedData, e);
                    }
                }
            }

            results.put("success", successResults);
            results.put("errors", errorResults);
            results.put("total", encryptedDataList.length);
            results.put("successCount", successResults.size());
            results.put("errorCount", errorResults.size());
            results.put("algorithm", "AES-GCM");

            log.info("批量解密完成，总数: {}, 成功: {}, 失败: {}",
                encryptedDataList.length, successResults.size(), errorResults.size());

            return R.ok("批量解密完成", results);

        } catch (Exception e) {
            log.error("批量解密失败", e);
            return R.fail("批量解密失败: " + e.getMessage());
        }
    }

    /**
     * 获取加密配置状态
     *
     * @return 加密配置信息
     */
    @GetMapping("/encryptionConfig")
    public R<Map<String, Object>> getEncryptionConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("enabled", walletPrivateKeyProperties.isEnabled());
            config.put("algorithm", walletPrivateKeyProperties.getAlgorithm());
            config.put("encoding", walletPrivateKeyProperties.getEncoding());
            config.put("secretKeyLength", walletPrivateKeyProperties.getSecretKey().length());
            config.put("configSource", "yml配置文件");
            config.put("version", "1.0.0");

            return R.ok("获取配置成功", config);

        } catch (Exception e) {
            log.error("获取加密配置失败", e);
            return R.fail("获取配置失败: " + e.getMessage());
        }
    }


}
