package org.dromara.wallet.wallet.transfer.service;

import org.dromara.wallet.wallet.transfer.config.TransactionConfirmationConfig;
import org.dromara.wallet.wallet.transfer.dto.TransactionConfirmationResult;

/**
 * 统一交易确认服务接口
 *
 * <p>为不同区块链提供统一的交易确认机制，包括：</p>
 * <ul>
 *   <li>TRON链：基于固定延迟的简化确认</li>
 *   <li>EVM链：基于区块确认数的轮询确认</li>
 *   <li>Solana链：基于commitment level的状态确认</li>
 * </ul>
 *
 * <p>设计原则：</p>
 * <ul>
 *   <li>统一的方法签名和返回值</li>
 *   <li>配置驱动的确认参数</li>
 *   <li>保持各链特有逻辑的灵活性</li>
 *   <li>向后兼容现有实现</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
public interface TransactionConfirmationService {

    /**
     * 统一的交易确认方法
     *
     * <p>根据配置参数执行交易确认，支持：</p>
     * <ul>
     *   <li>超时控制</li>
     *   <li>重试机制</li>
     *   <li>确认数要求</li>
     *   <li>链特定的确认逻辑</li>
     * </ul>
     *
     * @param txHash 交易哈希/签名
     * @param config 确认配置参数
     * @return 确认结果，包含成功状态、确认数、错误信息等
     * @throws IllegalArgumentException 当参数无效时
     */
    TransactionConfirmationResult confirmTransaction(String txHash, TransactionConfirmationConfig config);

    /**
     * 获取默认确认配置
     *
     * <p>返回适合当前区块链的默认确认配置，包括：</p>
     * <ul>
     *   <li>推荐的超时时间</li>
     *   <li>合适的检查间隔</li>
     *   <li>默认的确认数要求</li>
     *   <li>重试策略</li>
     * </ul>
     *
     * @return 默认确认配置
     */
    default TransactionConfirmationConfig getDefaultConfirmationConfig() {
        return TransactionConfirmationConfig.builder()
            .timeoutSeconds(30)
            .checkIntervalSeconds(3)
            .maxRetries(3)
            .requiredConfirmations(1)
            .enableConfirmation(true)
            .confirmationFailureCausesTransferFailure(false)
            .build();
    }

    /**
     * 检查是否支持指定的确认配置
     *
     * <p>验证配置参数是否适用于当前区块链，例如：</p>
     * <ul>
     *   <li>超时时间是否合理</li>
     *   <li>确认数是否支持</li>
     *   <li>特殊参数是否有效</li>
     * </ul>
     *
     * @param config 待验证的配置
     * @return 是否支持该配置
     */
    default boolean isConfigurationSupported(TransactionConfirmationConfig config) {
        if (config == null) {
            return false;
        }

        // 基本参数验证
        return config.getTimeoutSeconds() > 0
            && config.getCheckIntervalSeconds() > 0
            && config.getMaxRetries() >= 0
            && config.getRequiredConfirmations() > 0;
    }

    /**
     * 获取区块链类型标识
     *
     * <p>返回当前实现对应的区块链类型，用于：</p>
     * <ul>
     *   <li>日志记录和调试</li>
     *   <li>性能监控和统计</li>
     *   <li>错误处理和报告</li>
     * </ul>
     *
     * @return 区块链类型（如："TRON", "EVM", "SOLANA"）
     */
    String getBlockchainType();
}
