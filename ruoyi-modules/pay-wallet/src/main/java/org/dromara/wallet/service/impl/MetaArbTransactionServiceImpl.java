package org.dromara.wallet.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.dromara.wallet.domain.bo.MetaArbTransactionBo;
import org.dromara.wallet.domain.vo.MetaArbTransactionVo;
import org.dromara.wallet.domain.MetaArbTransaction;
import org.dromara.wallet.mapper.MetaArbTransactionMapper;
import org.dromara.wallet.service.IMetaArbTransactionService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * ARB区块高度交易明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MetaArbTransactionServiceImpl implements IMetaArbTransactionService {

    private final MetaArbTransactionMapper baseMapper;

    /**
     * 查询ARB区块高度交易明细
     *
     * @param id 主键
     * @return ARB区块高度交易明细
     */
    @Override
    public MetaArbTransactionVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询ARB区块高度交易明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return ARB区块高度交易明细分页列表
     */
    @Override
    public TableDataInfo<MetaArbTransactionVo> queryPageList(MetaArbTransactionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MetaArbTransaction> lqw = buildQueryWrapper(bo);
        Page<MetaArbTransactionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的ARB区块高度交易明细列表
     *
     * @param bo 查询条件
     * @return ARB区块高度交易明细列表
     */
    @Override
    public List<MetaArbTransactionVo> queryList(MetaArbTransactionBo bo) {
        LambdaQueryWrapper<MetaArbTransaction> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MetaArbTransaction> buildQueryWrapper(MetaArbTransactionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MetaArbTransaction> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MetaArbTransaction::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getTxid()), MetaArbTransaction::getTxid, bo.getTxid());
        lqw.eq(bo.getBlockheight() != null, MetaArbTransaction::getBlockheight, bo.getBlockheight());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress()), MetaArbTransaction::getAddress, bo.getAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getFromaddress()), MetaArbTransaction::getFromaddress, bo.getFromaddress());
        lqw.eq(StringUtils.isNotBlank(bo.getContract()), MetaArbTransaction::getContract, bo.getContract());
        lqw.eq(bo.getAmount() != null, MetaArbTransaction::getAmount, bo.getAmount());
        lqw.eq(bo.getFee() != null, MetaArbTransaction::getFee, bo.getFee());
        lqw.eq(bo.getTimestamp() != null, MetaArbTransaction::getTimestamp, bo.getTimestamp());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), MetaArbTransaction::getType, bo.getType());
        lqw.eq(bo.getIssync() != null, MetaArbTransaction::getIssync, bo.getIssync());
        return lqw;
    }

    /**
     * 新增ARB区块高度交易明细
     *
     * @param bo ARB区块高度交易明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MetaArbTransactionBo bo) {
        MetaArbTransaction add = MapstructUtils.convert(bo, MetaArbTransaction.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改ARB区块高度交易明细
     *
     * @param bo ARB区块高度交易明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MetaArbTransactionBo bo) {
        MetaArbTransaction update = MapstructUtils.convert(bo, MetaArbTransaction.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MetaArbTransaction entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除ARB区块高度交易明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
