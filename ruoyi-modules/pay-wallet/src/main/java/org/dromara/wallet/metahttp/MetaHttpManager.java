package org.dromara.wallet.metahttp;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.domain.vo.MetaSolanaTransactionVo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * meta项目http请求管理
 *
 * <AUTHOR>
 * @date 2025/5/8 15:28
 **/
@Slf4j
@Component
public class MetaHttpManager {
    private final ObjectMapper objectMapper;
    private final HttpClient httpClient;

    /**
     * BK服务地址，用于系统配置等接口
     * 对应于配置文件中的 meta.api.bk
     */
    @Value("${meta.api.bk:http://localhost:18888/}")
    private String bkServiceUrl;

    /**
     * EVM和SOLANA服务地址，用于钱包入账等接口
     * 对应于配置文件中的 meta.api.bsc
     */
    @Value("${meta.api.bsc:http://localhost:8088/}")
    private String bscServiceUrl;

    /**
     * Wallet服务地址
     */
    @Value("${meta.api.wallet:http://localhost:8080/}")
    private String walletServiceUrl;

    /**
     *
     */
    public MetaHttpManager(ObjectMapper objectMapper, HttpClient httpClient) {
        this.objectMapper = objectMapper;
        this.httpClient = httpClient;
    }

    /**
     * 在所有属性注入完成后执行初始化
     */
    @PostConstruct
    public void init() {
        log.info("MetaHttpManager初始化完成，BK服务地址: {}, BSC服务地址: {}", bkServiceUrl, bscServiceUrl);
    }

    /**
     * 根据key获取指定配置 (使用BK服务)
     * POST <a href="http://localhost:18888/meta/system/config/configKeyByRemote/">...</a>{{configKey}}
     * Content-Type: application/json
     * {
     * "timestamp": 0,
     * "nonce": "",
     * "data": "",
     * "sign": ""
     * }
     */
    public String getConfig(String configKey) {
        try {
            // 构建请求路径
            String path = "/meta/system/config/configKeyByRemote";

            // 使用BK服务发送请求
            HashMap<String, String> params = new HashMap<>();
            params.put("configKey", configKey);

            // 使用httpClient指定BK服务地址
            String responseJson = httpClient.get(bkServiceUrl, path, params);

            // 解析响应
            JsonNode jsonNode = objectMapper.readTree(responseJson);
            // 通常响应会包含code、msg和data字段，code=200时，优先从data字段获取值，没有则从msg字段获取
            if (jsonNode.has("code") && jsonNode.get("code").asInt() == 200) {
                if (jsonNode.has("data")) {
                    return jsonNode.get("data").asText();
                } else if (jsonNode.has("msg")) {
                    return jsonNode.get("msg").asText();
                }
            }

            log.warn("获取配置失败，configKey: {}, 响应: {}", configKey, responseJson);
            return null;
        } catch (Exception e) {
            log.error("获取配置异常，configKey: {}", configKey, e);
            return null;
        }
    }

    /**
     * SOLANA入账请求 (使用EVM和SOLANA服务)
     */
    public boolean creditSolana(MetaSolanaTransactionVo metaSolanaTransactionVo) {
        return true;
//        try {
//            // 构建请求路径
//            String path = "/meta/walletManager/coinData";
//
//            // 使用BSC服务发送请求
//            String responseJson = httpClient.postWithBody(bscServiceUrl, path, metaSolanaTransactionVo);
//
//            // 解析响应
//            JsonNode jsonNode = objectMapper.readTree(responseJson);
//
//            // 通常响应会包含code、msg和data字段，code=200时表示成功
//            if (jsonNode.has("code") && jsonNode.get("code").asInt() == 200) {
//                return true;
//            }
//            log.warn("入账请求失败，响应: {}", responseJson);
//            return false;
//        } catch (Exception e) {
//            // 简化错误日志，只记录交易ID和错误信息
//            String txid = metaSolanaTransactionVo != null ? metaSolanaTransactionVo.getTxid() : "unknown";
//            log.error("入账请求异常 - txid: {} - 错误: {}", txid, e.getMessage());
//            return false;
//        }
    }

    /**
     * tron入账请求（使用Wallet服务）
     */
    public boolean creditTron(String txId) {
        try {
            // 构建请求路径
            String path = "/wallet/api/wallet/v3/credit/tron";

            // 使用Wallet服务发送请求
            String responseJson = httpClient.get(walletServiceUrl, path, null);

            // 解析响应
            JsonNode jsonNode = objectMapper.readTree(responseJson);

            // 通常响应会包含code、msg和data字段，code=200时表示成功
            if (jsonNode.has("code") && jsonNode.get("code").asInt() == 200) {
                return true;
            }
            log.warn("入账请求失败，响应: {}", responseJson);
            return false;
        } catch (Exception e) {
            log.error("入账请求异常 - txid: {} - 错误: {}", txId, e.getMessage());
            return false;
        }
    }

    /**
     * bsc入账请求（使用EVM和SOLANA服务）
     */
    public boolean creditBsc(String txId) {
        try {
            // 构建请求路径
            String path = "/meta/walletManager/coinData";

            // 使用BSC服务发送请求
            String responseJson = httpClient.get(bscServiceUrl, path, null);

            // 解析响应
            JsonNode jsonNode = objectMapper.readTree(responseJson);

            // 通常响应会包含code、msg和data字段，code=200时表示成功
            if (jsonNode.has("code") && jsonNode.get("code").asInt() == 200) {
                return true;
            }
            log.warn("入账请求失败，响应: {}", responseJson);
            return false;
        } catch (Exception e) {
            log.error("入账请求异常 - txid: {} - 错误: {}", txId, e.getMessage());
            return false;
        }
    }


    /**
     * arb入账请求（使用EVM和SOLANA服务）
     */
    public boolean creditArb(String txId) {
        try {
            // 构建请求路径
            String path = "/meta/walletManager/coinData";

            // 使用BSC服务发送请求
            String responseJson = httpClient.get(bscServiceUrl, path, null);

            // 解析响应
            JsonNode jsonNode = objectMapper.readTree(responseJson);

            // 通常响应会包含code、msg和data字段，code=200时表示成功
            if (jsonNode.has("code") && jsonNode.get("code").asInt() == 200) {
                return true;
            }
            log.warn("入账请求失败，响应: {}", responseJson);
            return false;
        } catch (Exception e) {
            log.error("入账请求异常 - txid: {} - 错误: {}", txId, e.getMessage());
            return false;
        }
    }

    /**
     * base入账请求（使用EVM和SOLANA服务）
     */
    public boolean creditBase(String txId) {
        try {
            // 构建请求路径
            String path = "/meta/walletManager/coinData";

            // 使用BSC服务发送请求
            String responseJson = httpClient.get(bscServiceUrl, path, null);

            // 解析响应
            JsonNode jsonNode = objectMapper.readTree(responseJson);

            // 通常响应会包含code、msg和data字段，code=200时表示成功
            if (jsonNode.has("code") && jsonNode.get("code").asInt() == 200) {
                return true;
            }
            log.warn("入账请求失败，响应: {}", responseJson);
            return false;
        } catch (Exception e) {
            log.error("入账请求异常 - txid: {} - 错误: {}", txId, e.getMessage());
            return false;
        }
    }


}
