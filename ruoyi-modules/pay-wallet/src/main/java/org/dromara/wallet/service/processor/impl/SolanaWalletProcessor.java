package org.dromara.wallet.service.processor.impl;

import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.ChainType;
import org.dromara.wallet.config.facade.SolanaConfigFacade;
import org.dromara.wallet.domain.bo.WalletCoinRecBo;
import org.dromara.wallet.service.IWalletCoinRecService;
import org.dromara.wallet.service.processor.WalletChainProcessor;
import org.dromara.wallet.service.processor.WalletProcessResult;
import org.dromara.wallet.wallet.helper.SolanaHelper;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * Solana链钱包处理器
 * 负责处理Solana链的钱包余额记录
 *
 * <p>处理内容包括：</p>
 * <ul>
 *   <li>SOL原生代币余额</li>
 *   <li>SPL合约代币余额</li>
 *   <li>租户信息处理</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SolanaWalletProcessor implements WalletChainProcessor {

    private final SolanaConfigFacade solanaConfigFacade;
    private final SolanaHelper solanaHelper;
    @Lazy
    @Resource
    private IWalletCoinRecService walletCoinRecService;

    @Override
    public ChainType getSupportedChainType() {
        return ChainType.SOLANA;
    }

    @Override
    public void processWallet(String walletAddress, String walletTenantId) {
        if (walletAddress == null || walletAddress.isEmpty()) {
            log.error("Solana钱包地址不能为空");
            return;
        }

        log.debug("开始处理Solana钱包: 地址={}, 租户ID={}", walletAddress, walletTenantId);

        try {
            WalletProcessResult result = new WalletProcessResult(walletAddress, "SOLANA");

            // 处理SOL原生代币
            processSolNativeToken(walletAddress, walletTenantId, result);

            // 处理SPL代币
            processSplTokens(walletAddress, walletTenantId, result);

            result.finish();
            if (result.hasFailures()) {
                log.warn(result.getSummary());
            } else {
                log.info(result.getSummary());
            }

            // 详细日志（debug级别）
            if (log.isDebugEnabled()) {
                log.debug(result.getDetailedSummary());
            }

        } catch (Exception e) {
            log.error("Solana钱包处理失败: 地址={}, 租户ID={}, 错误={}",
                walletAddress, walletTenantId, e.getMessage(), e);
            throw new RuntimeException("Solana钱包处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理SOL原生代币
     */
    private void processSolNativeToken(String walletAddress, String walletTenantId, WalletProcessResult result) {
        try {
            // 使用统一入口查询SOL余额
            BigDecimal solBalance = solanaHelper.balanceGetForRead(walletAddress, "SOL");

            if (solBalance != null) {
                WalletCoinRecBo balanceRecord = new WalletCoinRecBo();
                balanceRecord.setChainType("SOLANA");
                balanceRecord.setWalletAddress(walletAddress);
                balanceRecord.setTokenAddress("_SOL_NATIVE_");
                balanceRecord.setTokenSymbol("SOL");
                balanceRecord.setBalance(solBalance);
                balanceRecord.setDecimals(9); // SOL默认9位小数
                balanceRecord.setCreateBy(1111L);
                balanceRecord.setTenantId(walletTenantId); // 设置租户ID

                walletCoinRecService.insertByBo(balanceRecord);
                result.addSuccess("SOL", solBalance);
            }
        } catch (Exception e) {
            result.addFailure("SOL");
            log.debug("处理SOL原生代币失败: {}, error: {}", walletAddress, e.getMessage());
        }
    }

    /**
     * 处理SPL代币
     */
    private void processSplTokens(String walletAddress, String walletTenantId, WalletProcessResult result) {
        // 获取所有启用的代币
        for (String tokenSymbol : solanaConfigFacade.getEnabledTokenSymbols()) {
            if ("SOL".equalsIgnoreCase(tokenSymbol)) {
                continue; // 原生代币已经处理过了
            }

            try {
                String contractAddress = solanaConfigFacade.getContractAddress(tokenSymbol);
                if (contractAddress == null || contractAddress.trim().isEmpty()) {
                    log.debug("Solana代币{}缺少合约地址，跳过处理", tokenSymbol);
                    result.addFailure(tokenSymbol);
                    continue;
                }

                // 使用统一入口查询代币余额
                BigDecimal balance = solanaHelper.balanceGetForRead(walletAddress, tokenSymbol);

                if (balance != null) {
                    WalletCoinRecBo balanceRecord = new WalletCoinRecBo();
                    balanceRecord.setChainType("SOLANA");
                    balanceRecord.setWalletAddress(walletAddress);
                    balanceRecord.setTokenAddress(contractAddress);
                    balanceRecord.setTokenSymbol(tokenSymbol);
                    balanceRecord.setBalance(balance);
                    balanceRecord.setDecimals(solanaConfigFacade.getContractDecimals(tokenSymbol));
                    balanceRecord.setCreateBy(1111L);
                    balanceRecord.setTenantId(walletTenantId); // 设置租户ID

                    walletCoinRecService.insertByBo(balanceRecord);
                    result.addSuccess(tokenSymbol, balance);
                }
            } catch (Exception e) {
                result.addFailure(tokenSymbol);
                log.debug("处理Solana代币{}失败: {}, error: {}", tokenSymbol, walletAddress, e.getMessage());
            }
        }
    }
}
