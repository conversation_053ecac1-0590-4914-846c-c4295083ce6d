package org.dromara.wallet.controller;

import com.baomidou.lock.LockInfo;
import com.baomidou.lock.LockTemplate;
import com.baomidou.lock.executor.RedissonLockExecutor;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.web.core.BaseController;
import org.dromara.wallet.config.facade.*;
import org.dromara.wallet.service.WalletScanService;
import org.redisson.api.RLock;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 统一区块链扫描控制器
 * 整合持续扫描和手动扫描功能，支持EVM链和TRON链
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>持续扫描：支持多链独立扫描管理，进度持久化</li>
 *   <li>手动扫描：支持交易哈希/ID触发扫描，区块范围扫描</li>
 *   <li>多链支持：EVM链（BSC、ARB、BASE）和TRON链</li>
 *   <li>统一接口：提供一致的REST API设计</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/30
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/scan")
@RequiredArgsConstructor
public class WalletScanController extends BaseController {

    private final WalletScanService scanService;
    private final BscConfigFacade bscConfigFacade;
    private final ArbConfigFacade arbConfigFacade;
    private final BaseConfigFacade baseConfigFacade;
    private final TronConfigFacade tronConfigFacade;
    private final LockTemplate lockTemplate;

    /**
     * 分布式锁相关常量
     */
    private static final String SCAN_LOCK_KEY_PREFIX = "blockchain:scan:auto_start:";
    private static final long LOCK_WAIT_TIME = 1000L; // 管理接口等待锁时间：1秒
    private static final long LOCK_LEASE_TIME = 30000L; // 管理接口锁持有时间：30秒

    // ============ 持续扫描接口 ============

    /**
     * 启动指定链的持续扫描
     */
    @PostMapping("/continuous/{chainType}/start")
    @Log(title = "启动持续扫描", businessType = BusinessType.OTHER)
    public R<String> startContinuousScan(
        @PathVariable @NotBlank(message = "链类型不能为空")
        @Pattern(regexp = "^(BSC|ARB|BASE|TRON)$", message = "不支持的链类型")
        String chainType,
        @RequestParam BigInteger startBlock) {
        try {
            if ("TRON".equals(chainType)) {
                if (!tronConfigFacade.isEnabled()) {
                    return R.fail("TRON链配置未启用");
                }
                boolean success = scanService.startTronChainScan(startBlock);
                return success ? R.ok("TRON链持续扫描任务启动成功") : R.fail("TRON链持续扫描任务启动失败");
            } else {
                // EVM链处理
                EvmConfigFacade configFacade = getEvmConfigFacade(chainType);
                if (configFacade == null) {
                    return R.fail("不支持的链类型: " + chainType);
                }
                if (!configFacade.isEnabled()) {
                    return R.fail(chainType + "链配置未启用");
                }
                boolean success = scanService.startEvmChainScan(configFacade, startBlock);
                return success ? R.ok(chainType + "链持续扫描任务启动成功") : R.fail(chainType + "链持续扫描任务启动失败");
            }
        } catch (Exception e) {
            log.error("启动{}链持续扫描失败", chainType, e);
            return R.fail("启动" + chainType + "链持续扫描失败: " + e.getMessage());
        }
    }

    /**
     * 启动指定链的持续扫描（高级参数）
     */
    @PostMapping("/continuous/{chainType}/start/advanced")
    @Log(title = "启动持续扫描(高级)", businessType = BusinessType.OTHER)
    public R<String> startContinuousScanAdvanced(
        @PathVariable @NotBlank(message = "链类型不能为空")
        @Pattern(regexp = "^(BSC|ARB|BASE|TRON)$", message = "不支持的链类型")
        String chainType,
        @RequestParam BigInteger startBlock,
        @RequestParam(defaultValue = "10000")
        @Min(value = 5000, message = "扫描周期不能小于5秒")
        @Max(value = 60000, message = "扫描周期不能大于60秒")
        long scanPeriod,
        @RequestParam(defaultValue = "true") boolean enableLogScanning) {
        try {
            if ("TRON".equals(chainType)) {
                if (!tronConfigFacade.isEnabled()) {
                    return R.fail("TRON链配置未启用");
                }
                boolean success = scanService.startTronChainScan(startBlock, scanPeriod);
                return success ? R.ok("TRON链持续扫描任务启动成功，扫描周期: " + scanPeriod + "ms")
                    : R.fail("TRON链持续扫描任务启动失败");
            } else {
                // EVM链处理
                EvmConfigFacade configFacade = getEvmConfigFacade(chainType);
                if (configFacade == null) {
                    return R.fail("不支持的链类型: " + chainType);
                }
                if (!configFacade.isEnabled()) {
                    return R.fail(chainType + "链配置未启用");
                }
                boolean success = scanService.startEvmChainScan(configFacade, startBlock, scanPeriod, enableLogScanning);
                return success ? R.ok(chainType + "链持续扫描任务启动成功，扫描周期: " + scanPeriod + "ms")
                    : R.fail(chainType + "链持续扫描任务启动失败");
            }
        } catch (Exception e) {
            log.error("启动{}链持续扫描失败", chainType, e);
            return R.fail("启动" + chainType + "链持续扫描失败: " + e.getMessage());
        }
    }

    /**
     * 停止指定链的持续扫描
     */
    @PostMapping("/continuous/{chainType}/stop")
    @Log(title = "停止持续扫描", businessType = BusinessType.OTHER)
    public R<String> stopContinuousScan(
        @PathVariable @NotBlank(message = "链类型不能为空")
        @Pattern(regexp = "^(BSC|ARB|BASE|TRON)$", message = "不支持的链类型")
        String chainType) {
        try {
            boolean success = scanService.stopChainScan(chainType);
            return success ? R.ok(chainType + "链持续扫描任务停止成功")
                : R.fail(chainType + "链持续扫描任务停止失败");
        } catch (Exception e) {
            log.error("停止{}链持续扫描失败", chainType, e);
            return R.fail("停止" + chainType + "链持续扫描失败: " + e.getMessage());
        }
    }

    /**
     * 停止所有持续扫描任务
     */
    @PostMapping("/continuous/stop/all")
    @Log(title = "停止所有持续扫描", businessType = BusinessType.OTHER)
    public R<String> stopAllContinuousScans() {
        try {
            int stoppedCount = scanService.stopAllScans();
            return R.ok("成功停止" + stoppedCount + "个持续扫描任务");
        } catch (Exception e) {
            log.error("停止所有持续扫描任务失败", e);
            return R.fail("停止所有持续扫描任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询指定链的持续扫描状态
     */
    @GetMapping("/continuous/{chainType}/status")
    public R<Map<String, Object>> getContinuousScanStatus(
        @PathVariable @NotBlank(message = "链类型不能为空")
        @Pattern(regexp = "^(BSC|ARB|BASE|TRON)$", message = "不支持的链类型")
        String chainType) {
        try {
            Map<String, Object> status = new HashMap<>();

            status.put("chainName", chainType);
            status.put("isScanning", scanService.isChainScanning(chainType));
            status.put("currentBlock", scanService.getChainCurrentBlockHeight(chainType));

            // 获取配置信息
            if ("TRON".equals(chainType)) {
                status.put("configEnabled", tronConfigFacade.isEnabled());
                status.put("autoScanEnabled", tronConfigFacade.isAutoScanEnabled());
                status.put("autoScanStartBlock", tronConfigFacade.getAutoScanStartBlock());
                status.put("autoScanPeriod", tronConfigFacade.getAutoScanPeriod());
            } else {
                EvmConfigFacade configFacade = getEvmConfigFacade(chainType);
                if (configFacade != null) {
                    status.put("configEnabled", configFacade.isEnabled());
                    status.put("autoScanEnabled", configFacade.isAutoScanEnabled());
                    status.put("autoScanStartBlock", configFacade.getAutoScanStartBlock());
                    status.put("autoScanPeriod", configFacade.getAutoScanPeriod());
                }
            }

            // 添加分布式锁状态信息
            String lockKey = SCAN_LOCK_KEY_PREFIX + chainType.toLowerCase();
            status.put("distributedLockStatus", getDistributedLockStatusInternal(lockKey));

            return R.ok(status);
        } catch (Exception e) {
            log.error("查询{}链持续扫描状态失败", chainType, e);
            return R.fail("查询" + chainType + "链持续扫描状态失败: " + e.getMessage());
        }
    }

    /**
     * 查询所有链的持续扫描状态
     */
    @GetMapping("/continuous/status/all")
    public R<Map<String, Object>> getAllContinuousScanStatus() {
        try {
            Map<String, Object> status = new HashMap<>();

            // 获取活跃的扫描链
            Set<String> activeChains = scanService.getActiveScanChains();
            status.put("activeChains", activeChains);
            status.put("activeCount", activeChains.size());

            // 获取各链的详细状态
            Map<String, Object> chainDetails = new HashMap<>();
            for (String chainName : activeChains) {
                Map<String, Object> chainInfo = new HashMap<>();
                chainInfo.put("isScanning", scanService.isChainScanning(chainName));
                chainInfo.put("currentBlock", scanService.getChainCurrentBlockHeight(chainName));
                chainDetails.put(chainName, chainInfo);
            }
            status.put("chainDetails", chainDetails);

            return R.ok(status);
        } catch (Exception e) {
            log.error("查询所有链持续扫描状态失败", e);
            return R.fail("查询所有链持续扫描状态失败: " + e.getMessage());
        }
    }

    // ============ 手动扫描接口 ============

    /**
     * 通过交易哈希触发EVM链手动扫描
     */
    @PostMapping("/manual/evm/{chainType}/tx/{txHash}")
    @Log(title = "EVM链交易哈希手动扫描", businessType = BusinessType.OTHER)
    public R<String> scanEvmByTxHash(
        @PathVariable @NotBlank(message = "链类型不能为空")
        @Pattern(regexp = "^(BSC|ARB|BASE)$", message = "不支持的链类型")
        String chainType,

        @PathVariable @NotBlank(message = "交易哈希不能为空")
        @Pattern(regexp = "^0x[a-fA-F0-9]{64}$", message = "交易哈希格式不正确")
        String txHash,

        @RequestParam(defaultValue = "0")
        @Min(value = 0, message = "上下文区块数量不能小于0")
        @Max(value = 10, message = "上下文区块数量不能大于10")
        int contextBlocks,

        @RequestParam(defaultValue = "60")
        @Min(value = 10, message = "超时时间不能小于10秒")
        @Max(value = 300, message = "超时时间不能大于300秒")
        int timeoutSeconds) {

        try {
            log.info("收到{}链交易哈希手动扫描请求: {}, 上下文区块: {}, 超时: {}秒",
                chainType, txHash, contextBlocks, timeoutSeconds);

            contextBlocks = Math.max(1, contextBlocks);
            WalletScanService.ScanResult result =
                scanService.scanEvmByTxHash(chainType, txHash, contextBlocks, timeoutSeconds);

            if (result.isSuccess()) {
                return R.ok(result.getMessage());
            } else {
                return R.fail(result.getMessage());
            }

        } catch (Exception e) {
            log.error("{}链交易哈希{}手动扫描异常", chainType, txHash, e);
            return R.fail("手动扫描异常: " + e.getMessage());
        }
    }

    /**
     * 手动扫描EVM链指定区块范围
     */
    @PostMapping("/manual/evm/{chainType}/blocks/{fromBlock}/{toBlock}")
    @Log(title = "EVM链区块范围手动扫描", businessType = BusinessType.OTHER)
    public R<String> scanEvmBlockRange(
        @PathVariable @NotBlank(message = "链类型不能为空")
        @Pattern(regexp = "^(BSC|ARB|BASE)$", message = "不支持的链类型")
        String chainType,

        @PathVariable @Min(value = 0, message = "起始区块号不能小于0")
        long fromBlock,

        @PathVariable @Min(value = 0, message = "结束区块号不能小于0")
        long toBlock,

        @RequestParam(defaultValue = "60")
        @Min(value = 10, message = "超时时间不能小于10秒")
        @Max(value = 300, message = "超时时间不能大于300秒")
        int timeoutSeconds) {

        try {
            log.info("收到{}链区块范围手动扫描请求: {} - {}, 超时: {}秒",
                chainType, fromBlock, toBlock, timeoutSeconds);

            WalletScanService.ScanResult result =
                scanService.scanEvmBlockRange(chainType,
                    BigInteger.valueOf(fromBlock), BigInteger.valueOf(toBlock), timeoutSeconds);

            if (result.isSuccess()) {
                return R.ok(result.getMessage());
            } else {
                return R.fail(result.getMessage());
            }

        } catch (Exception e) {
            log.error("{}链区块范围{} - {}手动扫描异常", chainType, fromBlock, toBlock, e);
            return R.fail("手动扫描异常: " + e.getMessage());
        }
    }

    /**
     * 通过交易ID触发TRON链手动扫描
     */
    @PostMapping("/manual/tron/tx/{txId}")
    @Log(title = "TRON链交易ID手动扫描", businessType = BusinessType.OTHER)
    public R<String> scanTronByTxId(
        @PathVariable @NotBlank(message = "交易ID不能为空")
        @Pattern(regexp = "^[a-fA-F0-9]{64}$", message = "交易ID格式不正确")
        String txId,

        @RequestParam(defaultValue = "0")
        @Min(value = 0, message = "上下文区块数量不能小于0")
        @Max(value = 10, message = "上下文区块数量不能大于10")
        int contextBlocks,

        @RequestParam(defaultValue = "60")
        @Min(value = 10, message = "超时时间不能小于10秒")
        @Max(value = 300, message = "超时时间不能大于300秒")
        int timeoutSeconds) {

        try {
            log.info("收到TRON链交易ID手动扫描请求: {}, 上下文区块: {}, 超时: {}秒",
                txId, contextBlocks, timeoutSeconds);

            WalletScanService.ScanResult result =
                scanService.scanTronByTxId(txId, contextBlocks, timeoutSeconds);

            if (result.isSuccess()) {
                return R.ok(result.getMessage());
            } else {
                return R.fail(result.getMessage());
            }

        } catch (Exception e) {
            log.error("TRON链交易ID{}手动扫描异常", txId, e);
            return R.fail("手动扫描异常: " + e.getMessage());
        }
    }

    /**
     * 手动扫描TRON链指定区块范围
     */
    @PostMapping("/manual/tron/blocks/{fromBlock}/{toBlock}")
    @Log(title = "TRON链区块范围手动扫描", businessType = BusinessType.OTHER)
    public R<String> scanTronBlockRange(
        @PathVariable @Min(value = 0, message = "起始区块号不能小于0")
        long fromBlock,

        @PathVariable @Min(value = 0, message = "结束区块号不能小于0")
        long toBlock,

        @RequestParam(defaultValue = "60")
        @Min(value = 10, message = "超时时间不能小于10秒")
        @Max(value = 300, message = "超时时间不能大于300秒")
        int timeoutSeconds) {

        try {
            log.info("收到TRON链区块范围手动扫描请求: {} - {}, 超时: {}秒",
                fromBlock, toBlock, timeoutSeconds);

            WalletScanService.ScanResult result =
                scanService.scanTronBlockRange(
                    BigInteger.valueOf(fromBlock), BigInteger.valueOf(toBlock), timeoutSeconds);

            if (result.isSuccess()) {
                return R.ok(result.getMessage());
            } else {
                return R.fail(result.getMessage());
            }

        } catch (Exception e) {
            log.error("TRON链区块范围{} - {}手动扫描异常", fromBlock, toBlock, e);
            return R.fail("手动扫描异常: " + e.getMessage());
        }
    }

    // ============ 进度管理接口 ============

    /**
     * 手动保存指定链的扫描进度
     */
    @PostMapping("/progress/{chainType}/save")
    @Log(title = "保存扫描进度", businessType = BusinessType.OTHER)
    public R<String> saveScanProgress(
        @PathVariable @NotBlank(message = "链类型不能为空")
        @Pattern(regexp = "^(BSC|ARB|BASE|TRON)$", message = "不支持的链类型")
        String chainType) {
        try {
            boolean success = scanService.saveCurrentScanProgress(chainType);

            if (success) {
                return R.ok("成功保存" + chainType + "链的扫描进度");
            } else {
                return R.fail("保存" + chainType + "链扫描进度失败");
            }
        } catch (Exception e) {
            log.error("保存{}链扫描进度失败", chainType, e);
            return R.fail("保存" + chainType + "链扫描进度失败: " + e.getMessage());
        }
    }

    /**
     * 查询指定链的扫描进度信息
     */
    @GetMapping("/progress/{chainType}")
    public R<WalletScanService.ScanProgressInfo> getScanProgress(
        @PathVariable @NotBlank(message = "链类型不能为空")
        @Pattern(regexp = "^(BSC|ARB|BASE|TRON)$", message = "不支持的链类型")
        String chainType) {
        try {
            WalletScanService.ScanProgressInfo progressInfo = scanService.getScanProgressInfo(chainType);
            return R.ok(progressInfo);
        } catch (Exception e) {
            log.error("查询{}链扫描进度失败", chainType, e);
            return R.fail("查询" + chainType + "链扫描进度失败: " + e.getMessage());
        }
    }

    /**
     * 查询所有链的扫描进度信息
     */
    @GetMapping("/progress/all")
    public R<Map<String, WalletScanService.ScanProgressInfo>> getAllScanProgress() {
        try {
            Map<String, WalletScanService.ScanProgressInfo> allProgress = new HashMap<>();

            // 获取所有活跃的扫描链
            for (String chainName : scanService.getActiveScanChains()) {
                WalletScanService.ScanProgressInfo progressInfo = scanService.getScanProgressInfo(chainName);
                allProgress.put(chainName, progressInfo);
            }

            return R.ok(allProgress);
        } catch (Exception e) {
            log.error("查询所有链扫描进度失败", e);
            return R.fail("查询所有链扫描进度失败: " + e.getMessage());
        }
    }

    // ============ 系统信息接口 ============

    /**
     * 获取支持的EVM链列表
     */
    @GetMapping("/chains/evm")
    public R<List<String>> getSupportedEvmChains() {
        try {
            List<String> chains = scanService.getSupportedEvmChains();
            return R.ok(chains);
        } catch (Exception e) {
            log.error("获取支持的EVM链列表失败", e);
            return R.fail("获取失败: " + e.getMessage());
        }
    }

    /**
     * 检查链是否支持
     */
    @GetMapping("/chains/{chainType}/supported")
    public R<Boolean> isChainSupported(
        @PathVariable @NotBlank(message = "链类型不能为空") String chainType) {
        try {
            boolean supported = scanService.isChainSupported(chainType);
            return R.ok(supported);
        } catch (Exception e) {
            log.error("检查链{}支持状态失败", chainType, e);
            return R.fail("检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统状态
     */
    @GetMapping("/status")
    public R<SystemStatus> getSystemStatus() {
        try {
            List<String> supportedEvmChains = scanService.getSupportedEvmChains();
            boolean tronSupported = scanService.isChainSupported("TRON");

            SystemStatus status = new SystemStatus();
            status.setSupportedEvmChains(supportedEvmChains);
            status.setTronSupported(tronSupported);
            status.setTotalSupportedChains(supportedEvmChains.size() + (tronSupported ? 1 : 0));
            status.setTimestamp(System.currentTimeMillis());

            return R.ok(status);
        } catch (Exception e) {
            log.error("获取系统状态失败", e);
            return R.fail("获取失败: " + e.getMessage());
        }
    }

    // ============ 分布式锁管理接口 ============

    /**
     * 查询指定链的分布式锁状态
     */
    @GetMapping("/lock/{chainType}/status")
    public R<Map<String, Object>> getChainLockStatus(
        @PathVariable @NotBlank(message = "链类型不能为空")
        @Pattern(regexp = "^(BSC|ARB|BASE|TRON)$", message = "不支持的链类型")
        String chainType) {
        try {
            String lockKey = SCAN_LOCK_KEY_PREFIX + chainType.toLowerCase();
            Map<String, Object> lockStatus = getDistributedLockStatusInternal(lockKey);
            lockStatus.put("chainName", chainType);
            return R.ok(lockStatus);
        } catch (Exception e) {
            log.error("查询{}链分布式锁状态失败", chainType, e);
            return R.fail("查询" + chainType + "链分布式锁状态失败: " + e.getMessage());
        }
    }

    /**
     * 强制释放指定链的分布式锁（紧急情况使用）
     */
    @PostMapping("/lock/{chainType}/force-unlock")
    @Log(title = "强制释放分布式锁", businessType = BusinessType.OTHER)
    public R<String> forceUnlockChain(
        @PathVariable @NotBlank(message = "链类型不能为空")
        @Pattern(regexp = "^(BSC|ARB|BASE|TRON)$", message = "不支持的链类型")
        String chainType) {
        try {
            String lockKey = SCAN_LOCK_KEY_PREFIX + chainType.toLowerCase();
            RLock lock = RedisUtils.getClient().getLock(lockKey);

            if (!lock.isLocked()) {
                return R.ok(chainType + "链锁未被持有，无需释放");
            }

            // 强制解锁
            lock.forceUnlock();
            log.warn("已强制释放{}链扫描分布式锁: {}", chainType, lockKey);

            return R.ok("已强制释放" + chainType + "链分布式锁");
        } catch (Exception e) {
            log.error("强制释放{}链分布式锁失败", chainType, e);
            return R.fail("强制释放" + chainType + "链分布式锁失败: " + e.getMessage());
        }
    }

    /**
     * 手动启动指定链扫描（带分布式锁保护）
     */
    @PostMapping("/lock/{chainType}/start-with-lock")
    @Log(title = "带锁启动扫描", businessType = BusinessType.OTHER)
    public R<String> startChainScanWithLock(
        @PathVariable @NotBlank(message = "链类型不能为空")
        @Pattern(regexp = "^(BSC|ARB|BASE|TRON)$", message = "不支持的链类型")
        String chainType,
        @RequestParam(defaultValue = "0") long startBlock,
        @RequestParam(defaultValue = "15000") long period) {
        try {
            log.info("尝试获取分布式锁并手动启动{}链扫描", chainType);

            String lockKey = SCAN_LOCK_KEY_PREFIX + chainType.toLowerCase();
            LockInfo lockInfo = lockTemplate.lock(lockKey, LOCK_WAIT_TIME, LOCK_LEASE_TIME, RedissonLockExecutor.class);

            if (lockInfo == null) {
                return R.fail("无法获取" + chainType + "链分布式锁，可能其他节点正在执行扫描任务");
            }

            try {
                if (scanService.isChainScanning(chainType)) {
                    return R.fail(chainType + "链扫描任务已在运行中");
                }

                BigInteger startBlockBig = BigInteger.valueOf(Math.max(0, startBlock));
                boolean success;

                if ("TRON".equals(chainType)) {
                    success = scanService.startTronChainScan(startBlockBig, period);
                } else {
                    // EVM链处理
                    EvmConfigFacade configFacade = getEvmConfigFacade(chainType);
                    if (configFacade == null) {
                        return R.fail("不支持的链: " + chainType);
                    }
                    success = scanService.startEvmChainScan(configFacade, startBlockBig, period, true);
                }

                if (success) {
                    log.info("手动启动{}链扫描成功，起始区块: {}, 扫描周期: {}ms", chainType, startBlockBig, period);
                    return R.ok(chainType + "链扫描任务启动成功");
                } else {
                    return R.fail(chainType + "链扫描任务启动失败");
                }

            } finally {
                lockTemplate.releaseLock(lockInfo);
                log.info("已释放{}链扫描分布式锁", chainType);
            }

        } catch (Exception e) {
            log.error("手动启动{}链扫描失败", chainType, e);
            return R.fail("手动启动" + chainType + "链扫描失败: " + e.getMessage());
        }
    }

    // ============ 私有辅助方法 ============

    /**
     * 根据链类型获取EVM配置门面
     */
    private EvmConfigFacade getEvmConfigFacade(String chainType) {
        switch (chainType.toUpperCase()) {
            case "BSC":
                return bscConfigFacade;
            case "ARB":
                return arbConfigFacade;
            case "BASE":
                return baseConfigFacade;
            default:
                return null;
        }
    }

    /**
     * 获取分布式锁状态的内部方法
     */
    private Map<String, Object> getDistributedLockStatusInternal(String lockKey) {
        Map<String, Object> lockStatus = new HashMap<>();
        try {
            RLock lock = RedisUtils.getClient().getLock(lockKey);
            boolean isLocked = lock.isLocked();

            lockStatus.put("lockKey", lockKey);
            lockStatus.put("isLocked", isLocked);

            if (isLocked) {
                lockStatus.put("remainTimeToLive", lock.remainTimeToLive());
                lockStatus.put("holdCount", lock.getHoldCount());
            }
        } catch (Exception e) {
            lockStatus.put("error", "获取锁状态失败: " + e.getMessage());
        }
        return lockStatus;
    }

    // ============ 内部类定义 ============

    /**
     * 系统状态类
     */
    public static class SystemStatus {
        private List<String> supportedEvmChains;
        private boolean tronSupported;
        private int totalSupportedChains;
        private long timestamp;

        // Getters and Setters
        public List<String> getSupportedEvmChains() {
            return supportedEvmChains;
        }

        public void setSupportedEvmChains(List<String> supportedEvmChains) {
            this.supportedEvmChains = supportedEvmChains;
        }

        public boolean isTronSupported() {
            return tronSupported;
        }

        public void setTronSupported(boolean tronSupported) {
            this.tronSupported = tronSupported;
        }

        public int getTotalSupportedChains() {
            return totalSupportedChains;
        }

        public void setTotalSupportedChains(int totalSupportedChains) {
            this.totalSupportedChains = totalSupportedChains;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }
    }
}
