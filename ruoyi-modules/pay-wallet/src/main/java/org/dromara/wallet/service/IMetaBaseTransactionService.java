package org.dromara.wallet.service;

import org.dromara.wallet.domain.vo.MetaBaseTransactionVo;
import org.dromara.wallet.domain.bo.MetaBaseTransactionBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * base区块高度交易明细Service接口
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface IMetaBaseTransactionService {

    /**
     * 查询base区块高度交易明细
     *
     * @param id 主键
     * @return base区块高度交易明细
     */
    MetaBaseTransactionVo queryById(Long id);

    /**
     * 分页查询base区块高度交易明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return base区块高度交易明细分页列表
     */
    TableDataInfo<MetaBaseTransactionVo> queryPageList(MetaBaseTransactionBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的base区块高度交易明细列表
     *
     * @param bo 查询条件
     * @return base区块高度交易明细列表
     */
    List<MetaBaseTransactionVo> queryList(MetaBaseTransactionBo bo);

    /**
     * 新增base区块高度交易明细
     *
     * @param bo base区块高度交易明细
     * @return 是否新增成功
     */
    Boolean insertByBo(MetaBaseTransactionBo bo);

    /**
     * 修改base区块高度交易明细
     *
     * @param bo base区块高度交易明细
     * @return 是否修改成功
     */
    Boolean updateByBo(MetaBaseTransactionBo bo);

    /**
     * 校验并批量删除base区块高度交易明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
