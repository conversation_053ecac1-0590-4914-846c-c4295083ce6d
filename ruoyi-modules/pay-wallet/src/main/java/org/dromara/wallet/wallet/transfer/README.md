# 统一转账架构

## 概述

本模块提供了跨链转账的统一架构，解决了TRON、EVM、Solana三条链转账逻辑混乱的问题。通过策略模式和统一接口，实现了：

- **单一入口点**：`UnifiedTransferService` 提供统一的转账接口
- **自动检测**：自动识别链类型和代币类型（原生/合约）
- **统一手续费**：强制手续费钱包支持，统一的手续费处理策略
- **统一异常**：标准化的错误代码和异常处理
- **统一返回值**：标准化的转账结果格式

## 架构设计

```
wallet/transfer/
├── service/
│   └── UnifiedTransferService.java     # 统一转账服务（主入口）
├── dto/
│   ├── TransferRequest.java            # 统一转账请求模型
│   └── BlockchainTransferResult.java   # 统一转账结果模型
└── strategy/
    ├── BlockchainTransferStrategy.java # 转账策略接口
    └── impl/
        ├── TronTransferStrategy.java   # TRON转账策略
        ├── EvmTransferStrategy.java    # EVM转账策略
        └── SolanaTransferStrategy.java # Solana转账策略
```

## 使用方式

### 1. 基础转账

```java
@Autowired
private UnifiedTransferService unifiedTransferService;

// 方式一：使用TransferRequest对象
TransferRequest request = TransferRequest.builder()
    .privateKey("your_private_key")
    .toAddress("target_address")
    .amount("100.5")
    .tokenSymbol("USDT")
    .chainName("TRON")
    .build();

BlockchainTransferResult result = unifiedTransferService.transfer(request);

// 方式二：使用简化方法
BlockchainTransferResult result = unifiedTransferService.transfer(
    "your_private_key",
    "target_address", 
    "100.5",
    "USDT",
    "TRON"
);
```

### 2. 批量转账

```java
List<TransferRequest> requests = Arrays.asList(
    TransferRequest.basic("key1", "addr1", "100", "USDT", "TRON"),
    TransferRequest.basic("key2", "addr2", "50", "BNB", "BSC"),
    TransferRequest.basic("key3", "addr3", "200", "SOL", "SOLANA")
);

List<BlockchainTransferResult> results = unifiedTransferService.batchTransfer(requests);
```

### 3. 高级配置

```java
// 禁用手续费钱包
TransferRequest request = TransferRequest.withoutFeeWallet(
    "private_key", "to_address", "100", "USDT", "TRON"
);

// 快速转账（不等待确认）
TransferRequest request = TransferRequest.quickTransfer(
    "private_key", "to_address", "100", "USDT", "TRON"
);

// 自定义Gas参数（仅EVM链）
TransferRequest request = TransferRequest.builder()
    .privateKey("private_key")
    .toAddress("to_address")
    .amount("100")
    .tokenSymbol("USDT")
    .chainName("BSC")
    .customGasPrice(new BigDecimal("5")) // 5 Gwei
    .customGasLimit(21000L)
    .build();
```

## 支持的区块链

| 链名称 | 支持的别名 | 原生代币 | 合约代币标准 |
|--------|------------|----------|--------------|
| TRON | TRX | TRX | TRC20 |
| BSC | BNB, BINANCE | BNB | BEP20 |
| Arbitrum | ARB, ARBITRUM | ETH | ERC20 |
| Base | BASE | ETH | ERC20 |
| Ethereum | ETH, ETHEREUM | ETH | ERC20 |
| Solana | SOL, SOLANA | SOL | SPL |

## 错误处理

### 错误代码

```java
// 常用错误代码
BlockchainTransferResult.ERROR_INVALID_PARAMS        // 参数错误
BlockchainTransferResult.ERROR_INSUFFICIENT_BALANCE  // 余额不足
BlockchainTransferResult.ERROR_INSUFFICIENT_FEE      // 手续费不足
BlockchainTransferResult.ERROR_NETWORK_ERROR         // 网络错误
BlockchainTransferResult.ERROR_TIMEOUT               // 超时
BlockchainTransferResult.ERROR_UNSUPPORTED_CHAIN     // 不支持的链
BlockchainTransferResult.ERROR_UNSUPPORTED_TOKEN     // 不支持的代币
```

### 错误处理示例

```java
BlockchainTransferResult result = unifiedTransferService.transfer(request);

if (result.isSuccess()) {
    log.info("转账成功: {}", result.getTxHash());
} else {
    // 根据错误类型处理
    if (result.isInsufficientBalanceError()) {
        // 处理余额不足
    } else if (result.isNetworkError()) {
        // 处理网络错误，可以重试
    } else if (result.isTimeoutError()) {
        // 处理超时，可以查询交易状态
    }
    
    log.error("转账失败: {} - {}", result.getErrorCode(), result.getErrorMessage());
}
```

## 结果信息

```java
BlockchainTransferResult result = unifiedTransferService.transfer(request);

// 基础信息
boolean success = result.isSuccess();
String txHash = result.getTxHash();
String chainName = result.getChainName();

// 手续费信息
boolean feeProvided = result.isFeeProvided();
BigDecimal feeAmount = result.getFeeAmount();
String feeTokenSymbol = result.getFeeTokenSymbol();

// 性能信息
Long executionTime = result.getExecutionTimeMs();
boolean waitedForConfirmation = result.isWaitedForConfirmation();

// 便捷方法
String summary = result.getSummary();
String statusDesc = result.getStatusDescription();
```

## 扩展新链

要添加新的区块链支持，只需：

1. 实现 `BlockchainTransferStrategy` 接口
2. 添加 `@Component` 注解
3. 实现具体的转账逻辑

```java
@Component
public class NewChainTransferStrategy implements BlockchainTransferStrategy {
    
    @Override
    public String getChainName() {
        return "NEW_CHAIN";
    }
    
    @Override
    public boolean supports(String chainName) {
        return "NEW_CHAIN".equalsIgnoreCase(chainName);
    }
    
    @Override
    public BlockchainTransferResult execute(TransferRequest request) {
        // 实现转账逻辑
    }
}
```

## 与现有代码的兼容性

- **保持兼容**：现有的 `TronHelper`、`EvmHelper`、`SolanaHelper` 保持不变
- **渐进迁移**：可以逐步将现有代码迁移到统一接口
- **双重支持**：新旧接口可以并存，不影响现有功能

## 性能优化

- **策略缓存**：转账策略映射缓存，提高查找效率
- **并行处理**：批量转账支持并行处理
- **异常优化**：统一的异常转换，减少重复处理
- **参数验证**：前置参数验证，减少无效请求

## 编译状态

✅ **编译成功** - 所有转账策略类已修复编译错误，代码可以正常编译运行。

### 修复的问题

1. **导入路径错误** - 修复了策略类中的包导入路径
2. **方法签名不匹配** - 修复了Helper类方法调用的参数顺序
3. **缺失方法** - 在BlockchainTransferSuccess中添加了Helper类需要的方法：
   - `simple(String, String)` - 创建简单成功结果
   - `withFee(String, boolean, BigDecimal, String)` - 创建带手续费的成功结果
   - `isCompleted()` - 检查是否完成
4. **Record类方法调用** - 修复了策略类中对Record类方法的调用

## 最佳实践

1. **使用Builder模式**：推荐使用 `TransferRequest.builder()` 构建请求
2. **错误处理**：始终检查 `result.isSuccess()` 并处理错误情况
3. **日志记录**：利用 `result.getSummary()` 记录详细的转账信息
4. **批量操作**：对于多笔转账，使用 `batchTransfer()` 提高效率
5. **手续费钱包**：默认启用手续费钱包，确保转账成功率

## 下一步建议

1. **集成测试**：创建集成测试验证统一转账服务的功能
2. **性能测试**：测试批量转账的性能表现
3. **监控集成**：添加转账成功率和性能监控
4. **文档完善**：根据实际使用情况完善API文档
