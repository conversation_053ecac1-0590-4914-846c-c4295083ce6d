package org.dromara.wallet.metahttp;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.dromara.common.json.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.util.Map;

/**
 * 基于RestTemplate的HTTP客户端实现
 */
@Component
public class RestTemplateHttpClient implements HttpClient {
    private static final Logger log = LoggerFactory.getLogger(RestTemplateHttpClient.class);
    private final ObjectMapper objectMapper;
    private final RestTemplate restTemplate;

    public RestTemplateHttpClient(RestTemplate restTemplate, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }

    @Override
    public String get(String baseUrl, String path, Map<String, String> params) throws Exception {
        try {
            // 构建URL，包含查询参数
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(baseUrl + path);
            if (params != null && !params.isEmpty()) {
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    uriBuilder.queryParam(entry.getKey(), entry.getValue());
                }
            }

            // 创建GET请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建请求实体
            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);

            // 执行请求
            ResponseEntity<String> response = restTemplate.exchange(
                uriBuilder.build().toUri(),
                HttpMethod.GET,
                requestEntity,
                String.class
            );

            String result = response.getBody();
            log.debug("GET请求响应: {}", result);
            return result;
        } catch (Exception e) {
            // 简化错误日志
            log.error("GET请求失败: {} - {}", baseUrl + path, e.getMessage());
            throw e;
        }
    }

    @Override
    public String post(String baseUrl, String path, Object data) throws Exception {
        try {
            // 构建URL
            String url = baseUrl + path;

            // 创建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 将请求数据序列化为JSON
            String jsonBody = null;
            if (data != null) {
                jsonBody = objectMapper.writeValueAsString(data);
            }

            // 创建请求实体
            HttpEntity<String> requestEntity = new HttpEntity<>(jsonBody, headers);

            // 执行请求
            ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                String.class
            );

            String result = response.getBody();
            log.debug("POST请求响应: {}", result);
            return result;
        } catch (Exception e) {
            // 简化错误日志
            log.error("POST请求失败: {} - {}", baseUrl + path, e.getMessage());
            throw e;
        }
    }

    @Override
    public String postWithBody(String baseUrl, String path, Object data) throws IOException {
        try {
            // 构建URL
            String url = baseUrl + path;

            // 创建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 将数据转换为JSON
            String jsonBody;
            if (data instanceof String) {
                jsonBody = (String) data;
            } else {
                jsonBody = JsonUtils.toJsonString(data);
            }

            // 创建请求实体
            HttpEntity<String> requestEntity = new HttpEntity<>(jsonBody, headers);

            // 执行请求
            ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                String.class
            );

            String result = response.getBody();
            log.debug("POST请求响应: {}", result);
            return result;
        } catch (Exception e) {
            // 简化错误日志，只记录基本信息
            log.error("POST请求失败: {} - {}", baseUrl + path, e.getMessage());
            throw new IOException("POST请求失败", e);
        }
    }
}
