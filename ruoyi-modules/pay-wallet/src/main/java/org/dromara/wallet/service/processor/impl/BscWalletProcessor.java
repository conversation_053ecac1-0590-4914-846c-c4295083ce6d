package org.dromara.wallet.service.processor.impl;

import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.ChainType;
import org.dromara.wallet.config.facade.BscConfigFacade;
import org.dromara.wallet.domain.bo.WalletCoinRecBo;
import org.dromara.wallet.service.IWalletCoinRecService;
import org.dromara.wallet.service.processor.WalletChainProcessor;
import org.dromara.wallet.service.processor.WalletProcessResult;
import org.dromara.wallet.wallet.helper.EvmHelper;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * BSC链钱包处理器
 * 负责处理BSC链的钱包余额记录
 *
 * <p>处理内容包括：</p>
 * <ul>
 *   <li>BNB原生代币余额</li>
 *   <li>BEP20合约代币余额</li>
 *   <li>租户信息处理</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BscWalletProcessor implements WalletChainProcessor {

    private final BscConfigFacade bscConfigFacade;
    private final EvmHelper evmHelper;
    @Lazy
    @Resource
    private IWalletCoinRecService walletCoinRecService;

    @Override
    public ChainType getSupportedChainType() {
        return ChainType.BSC;
    }

    @Override
    public void processWallet(String walletAddress, String walletTenantId) {
        if (walletAddress == null || walletAddress.isEmpty()) {
            log.error("BSC钱包地址不能为空");
            return;
        }

        log.debug("开始处理BSC钱包: 地址={}, 租户ID={}", walletAddress, walletTenantId);

        try {
            WalletProcessResult result = new WalletProcessResult(walletAddress, "BSC");

            // 处理BNB原生代币
            processBscNativeToken(walletAddress, walletTenantId, result);

            // 处理BEP20代币
            processBscTokens(walletAddress, walletTenantId, result);

            result.finish();
            if (result.hasFailures()) {
                log.warn(result.getSummary());
            } else {
                log.info(result.getSummary());
            }

            // 详细日志（debug级别）
            if (log.isDebugEnabled()) {
                log.debug(result.getDetailedSummary());
            }

        } catch (Exception e) {
            log.error("BSC钱包处理失败: 地址={}, 租户ID={}, 错误={}",
                walletAddress, walletTenantId, e.getMessage(), e);
            throw new RuntimeException("BSC钱包处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理BNB原生代币
     */
    private void processBscNativeToken(String walletAddress, String walletTenantId, WalletProcessResult result) {
        try {
            // 使用统一入口查询BNB余额
            BigDecimal nativeBalance = evmHelper.balanceGetForRead(walletAddress, "BNB", bscConfigFacade);
            if (nativeBalance != null) {
                WalletCoinRecBo balanceRecord = new WalletCoinRecBo();
                balanceRecord.setChainType("BSC");
                balanceRecord.setWalletAddress(walletAddress);
                balanceRecord.setTokenAddress("_BNB_NATIVE_");
                balanceRecord.setTokenSymbol("BNB");
                balanceRecord.setBalance(nativeBalance);
                balanceRecord.setDecimals(18); // BNB默认18位小数
                balanceRecord.setCreateBy(1111L);
                balanceRecord.setTenantId(walletTenantId); // 设置租户ID

                walletCoinRecService.insertByBo(balanceRecord);
                result.addSuccess("BNB", nativeBalance);
            }
        } catch (Exception e) {
            result.addFailure("BNB");
            log.debug("处理BNB原生代币失败: {}, error: {}", walletAddress, e.getMessage());
        }
    }

    /**
     * 处理BEP20代币
     */
    private void processBscTokens(String walletAddress, String walletTenantId, WalletProcessResult result) {
        // 获取所有启用的代币
        for (String tokenSymbol : bscConfigFacade.getEnabledTokenSymbols()) {
            if ("BNB".equalsIgnoreCase(tokenSymbol)) {
                continue; // 原生代币已经处理过了
            }

            try {
                // 使用统一入口查询代币余额
                BigDecimal balance = evmHelper.balanceGetForRead(walletAddress, tokenSymbol, bscConfigFacade);

                if (balance != null) {
                    WalletCoinRecBo balanceRecord = new WalletCoinRecBo();
                    balanceRecord.setChainType("BSC");
                    balanceRecord.setWalletAddress(walletAddress);
                    balanceRecord.setTokenAddress(bscConfigFacade.getContractAddress(tokenSymbol));
                    balanceRecord.setTokenSymbol(tokenSymbol);
                    balanceRecord.setBalance(balance);
                    balanceRecord.setDecimals(bscConfigFacade.getContractDecimals(tokenSymbol));
                    balanceRecord.setCreateBy(1111L);
                    balanceRecord.setTenantId(walletTenantId); // 设置租户ID

                    walletCoinRecService.insertByBo(balanceRecord);
                    result.addSuccess(tokenSymbol, balance);
                }
            } catch (Exception e) {
                result.addFailure(tokenSymbol);
                log.debug("处理BSC代币失败: {} {}, error: {}", walletAddress, tokenSymbol, e.getMessage());
            }
        }
    }
}
