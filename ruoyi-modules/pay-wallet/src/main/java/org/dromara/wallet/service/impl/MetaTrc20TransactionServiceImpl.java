package org.dromara.wallet.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.dromara.wallet.domain.bo.MetaTrc20TransactionBo;
import org.dromara.wallet.domain.vo.MetaTrc20TransactionVo;
import org.dromara.wallet.domain.MetaTrc20Transaction;
import org.dromara.wallet.mapper.MetaTrc20TransactionMapper;
import org.dromara.wallet.service.IMetaTrc20TransactionService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * TRON区块高度交易明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MetaTrc20TransactionServiceImpl implements IMetaTrc20TransactionService {

    private final MetaTrc20TransactionMapper baseMapper;

    /**
     * 查询TRON区块高度交易明细
     *
     * @param id 主键
     * @return TRON区块高度交易明细
     */
    @Override
    public MetaTrc20TransactionVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询TRON区块高度交易明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return TRON区块高度交易明细分页列表
     */
    @Override
    public TableDataInfo<MetaTrc20TransactionVo> queryPageList(MetaTrc20TransactionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MetaTrc20Transaction> lqw = buildQueryWrapper(bo);
        Page<MetaTrc20TransactionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的TRON区块高度交易明细列表
     *
     * @param bo 查询条件
     * @return TRON区块高度交易明细列表
     */
    @Override
    public List<MetaTrc20TransactionVo> queryList(MetaTrc20TransactionBo bo) {
        LambdaQueryWrapper<MetaTrc20Transaction> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MetaTrc20Transaction> buildQueryWrapper(MetaTrc20TransactionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MetaTrc20Transaction> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MetaTrc20Transaction::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getTxid()), MetaTrc20Transaction::getTxid, bo.getTxid());
        lqw.eq(bo.getBlockheight() != null, MetaTrc20Transaction::getBlockheight, bo.getBlockheight());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress()), MetaTrc20Transaction::getAddress, bo.getAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getFromaddress()), MetaTrc20Transaction::getFromaddress, bo.getFromaddress());
        lqw.eq(StringUtils.isNotBlank(bo.getContract()), MetaTrc20Transaction::getContract, bo.getContract());
        lqw.eq(bo.getAmount() != null, MetaTrc20Transaction::getAmount, bo.getAmount());
        lqw.eq(bo.getFee() != null, MetaTrc20Transaction::getFee, bo.getFee());
        lqw.eq(bo.getTimestamp() != null, MetaTrc20Transaction::getTimestamp, bo.getTimestamp());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), MetaTrc20Transaction::getType, bo.getType());
        lqw.eq(bo.getIssync() != null, MetaTrc20Transaction::getIssync, bo.getIssync());
        return lqw;
    }

    /**
     * 新增TRON区块高度交易明细
     *
     * @param bo TRON区块高度交易明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MetaTrc20TransactionBo bo) {
        MetaTrc20Transaction add = MapstructUtils.convert(bo, MetaTrc20Transaction.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改TRON区块高度交易明细
     *
     * @param bo TRON区块高度交易明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MetaTrc20TransactionBo bo) {
        MetaTrc20Transaction update = MapstructUtils.convert(bo, MetaTrc20Transaction.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MetaTrc20Transaction entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除TRON区块高度交易明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
