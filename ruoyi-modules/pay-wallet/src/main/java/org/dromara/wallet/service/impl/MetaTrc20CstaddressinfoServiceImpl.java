package org.dromara.wallet.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.dromara.wallet.domain.bo.MetaTrc20CstaddressinfoBo;
import org.dromara.wallet.domain.vo.MetaTrc20CstaddressinfoVo;
import org.dromara.wallet.domain.MetaTrc20Cstaddressinfo;
import org.dromara.wallet.mapper.MetaTrc20CstaddressinfoMapper;
import org.dromara.wallet.service.IMetaTrc20CstaddressinfoService;

import java.util.*;
import java.util.stream.Collectors;

/**
 * TRON客户热钱包地址信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MetaTrc20CstaddressinfoServiceImpl implements IMetaTrc20CstaddressinfoService {

    private final MetaTrc20CstaddressinfoMapper baseMapper;

    /**
     * 查询TRON客户热钱包地址信息
     *
     * @param address 钱包地址
     * @return TRON客户热钱包地址信息
     */
    @Override
    public MetaTrc20Cstaddressinfo queryByAddress(String address) {
        return baseMapper.selectOne(new LambdaQueryWrapper<MetaTrc20Cstaddressinfo>().eq(MetaTrc20Cstaddressinfo::getCstAddress, address));
    }

    /**
     * 查询TRON客户热钱包地址信息
     *
     * @param id 主键
     * @return TRON客户热钱包地址信息
     */
    @Override
    public MetaTrc20CstaddressinfoVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询TRON客户热钱包地址信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return TRON客户热钱包地址信息分页列表
     */
    @Override
    public TableDataInfo<MetaTrc20CstaddressinfoVo> queryPageList(MetaTrc20CstaddressinfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MetaTrc20Cstaddressinfo> lqw = buildQueryWrapper(bo);
        Page<MetaTrc20CstaddressinfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的TRON客户热钱包地址信息列表
     *
     * @param bo 查询条件
     * @return TRON客户热钱包地址信息列表
     */
    @Override
    public List<MetaTrc20CstaddressinfoVo> queryList(MetaTrc20CstaddressinfoBo bo) {
        LambdaQueryWrapper<MetaTrc20Cstaddressinfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MetaTrc20Cstaddressinfo> buildQueryWrapper(MetaTrc20CstaddressinfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MetaTrc20Cstaddressinfo> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MetaTrc20Cstaddressinfo::getId);
        lqw.eq(bo.getCstId() != null, MetaTrc20Cstaddressinfo::getCstId, bo.getCstId());
        lqw.eq(StringUtils.isNotBlank(bo.getCstAddress()), MetaTrc20Cstaddressinfo::getCstAddress, bo.getCstAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getCstTrc20private()), MetaTrc20Cstaddressinfo::getCstTrc20private, bo.getCstTrc20private());
        lqw.eq(StringUtils.isNotBlank(bo.getCstHexaddress()), MetaTrc20Cstaddressinfo::getCstHexaddress, bo.getCstHexaddress());
        return lqw;
    }

    /**
     * 新增TRON客户热钱包地址信息
     *
     * @param bo TRON客户热钱包地址信息
     * @return 是否新增成功
     */
    @Override
    @CacheEvict(value = "tronCstAddressInfo", key = "'allHashSet'")
    public Boolean insertByBo(MetaTrc20CstaddressinfoBo bo) {
        MetaTrc20Cstaddressinfo add = MapstructUtils.convert(bo, MetaTrc20Cstaddressinfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改TRON客户热钱包地址信息
     *
     * @param bo TRON客户热钱包地址信息
     * @return 是否修改成功
     */
    @Override
    @CacheEvict(value = "tronCstAddressInfo", key = "'allHashSet'")
    public Boolean updateByBo(MetaTrc20CstaddressinfoBo bo) {
        MetaTrc20Cstaddressinfo update = MapstructUtils.convert(bo, MetaTrc20Cstaddressinfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MetaTrc20Cstaddressinfo entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除TRON客户热钱包地址信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    @Cacheable(value = "tronCstAddressInfo", key = "'allHashSet'", unless = "#result == null")
    public Set<String> queryAllAddressSet() {
        HashSet<String> addressSet = new HashSet<>();
        TenantHelper.ignore(()->{
            List<MetaTrc20CstaddressinfoVo> metaTrc20CstaddressinfoVos = baseMapper.selectVoList();
            Set<String> addressSet2 = metaTrc20CstaddressinfoVos.stream().map(MetaTrc20CstaddressinfoVo::getCstAddress).collect(Collectors.toSet());
            addressSet.addAll(addressSet2);
        });

        return addressSet;
    }
}
