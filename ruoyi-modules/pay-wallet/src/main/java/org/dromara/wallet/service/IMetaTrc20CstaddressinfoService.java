package org.dromara.wallet.service;

import org.dromara.wallet.domain.MetaTrc20Cstaddressinfo;
import org.dromara.wallet.domain.vo.MetaTrc20CstaddressinfoVo;
import org.dromara.wallet.domain.bo.MetaTrc20CstaddressinfoBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * TRON客户热钱包地址信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface IMetaTrc20CstaddressinfoService {

    MetaTrc20Cstaddressinfo queryByAddress(String address);

    /**
     * 查询TRON客户热钱包地址信息
     *
     * @param id 主键
     * @return TRON客户热钱包地址信息
     */
    MetaTrc20CstaddressinfoVo queryById(Long id);

    /**
     * 分页查询TRON客户热钱包地址信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return TRON客户热钱包地址信息分页列表
     */
    TableDataInfo<MetaTrc20CstaddressinfoVo> queryPageList(MetaTrc20CstaddressinfoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的TRON客户热钱包地址信息列表
     *
     * @param bo 查询条件
     * @return TRON客户热钱包地址信息列表
     */
    List<MetaTrc20CstaddressinfoVo> queryList(MetaTrc20CstaddressinfoBo bo);

    /**
     * 新增TRON客户热钱包地址信息
     *
     * @param bo TRON客户热钱包地址信息
     * @return 是否新增成功
     */
    Boolean insertByBo(MetaTrc20CstaddressinfoBo bo);

    /**
     * 修改TRON客户热钱包地址信息
     *
     * @param bo TRON客户热钱包地址信息
     * @return 是否修改成功
     */
    Boolean updateByBo(MetaTrc20CstaddressinfoBo bo);

    /**
     * 校验并批量删除TRON客户热钱包地址信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Set<String> queryAllAddressSet();

}
