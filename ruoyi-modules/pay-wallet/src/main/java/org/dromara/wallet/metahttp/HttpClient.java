package org.dromara.wallet.metahttp;

import java.io.IOException;
import java.util.Map;

/**
 * HTTP客户端接口
 * 定义通用的HTTP请求方法，支持不同实现
 */
public interface HttpClient {
    /**
     * 发送GET请求
     *
     * @param baseUrl 基础URL
     * @param path    请求路径
     * @param params  请求参数
     * @return 响应内容
     * @throws Exception 请求异常
     */
    String get(String baseUrl, String path, Map<String, String> params) throws Exception;

    /**
     * 发送POST请求，使用HTTP头部签名
     *
     * @param baseUrl 基础URL
     * @param path    请求路径
     * @param data    请求数据对象
     * @return 响应内容
     * @throws Exception 请求异常
     */
    String post(String baseUrl, String path, Object data) throws Exception;

    /**
     * 发送POST请求，使用请求体签名
     *
     * @param baseUrl 基础URL
     * @param path    请求路径
     * @param data    请求数据
     * @return 响应内容
     * @throws IOException 请求异常
     */
    String postWithBody(String baseUrl, String path, Object data) throws IOException;
}
