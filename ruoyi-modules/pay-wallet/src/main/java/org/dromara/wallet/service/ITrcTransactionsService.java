package org.dromara.wallet.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.wallet.domain.bo.TrcTransactionsBo;
import org.dromara.wallet.domain.vo.TrcTransactionsVo;

import java.util.Collection;
import java.util.List;

/**
 * TRON链交易记录Service接口
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
public interface ITrcTransactionsService {

    /**
     * 查询TRON链交易记录
     *
     * @param id 主键
     * @return TRON链交易记录
     */
    TrcTransactionsVo queryById(Long id);

    /**
     * 根据交易ID查询交易记录
     *
     * @param txid 交易ID
     * @return TRON链交易记录
     */
    TrcTransactionsVo queryByTxid(String txid);

    /**
     * 根据钱包地址查询交易记录
     *
     * @param address 钱包地址
     * @return TRON链交易记录列表
     */
    List<TrcTransactionsVo> queryByAddress(String address);

    /**
     * 分页查询TRON链交易记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return TRON链交易记录分页列表
     */
    TableDataInfo<TrcTransactionsVo> queryPageList(TrcTransactionsBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的TRON链交易记录列表
     *
     * @param bo 查询条件
     * @return TRON链交易记录列表
     */
    List<TrcTransactionsVo> queryList(TrcTransactionsBo bo);

    /**
     * 新增TRON链交易记录
     *
     * @param bo TRON链交易记录
     * @return 是否新增成功
     */
    Boolean insertByBo(TrcTransactionsBo bo);

    /**
     * 修改TRON链交易记录
     *
     * @param bo TRON链交易记录
     * @return 是否修改成功
     */
    Boolean updateByBo(TrcTransactionsBo bo);

    /**
     * 校验并批量删除TRON链交易记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
