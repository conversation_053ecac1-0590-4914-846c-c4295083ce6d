package org.dromara.wallet.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.dromara.wallet.domain.bo.MetaTransferRecBo;
import org.dromara.wallet.domain.vo.MetaTransferRecVo;
import org.dromara.wallet.domain.MetaTransferRec;
import org.dromara.wallet.mapper.MetaTransferRecMapper;
import org.dromara.wallet.service.IMetaTransferRecService;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.ArrayList;

/**
 * 转账记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MetaTransferRecServiceImpl implements IMetaTransferRecService {

    private final MetaTransferRecMapper baseMapper;


    /**
     * 根据外部id查询转账记录
     *
     * @param originalId 外部id
     * @return 转账记录
     */
    @Override
    public MetaTransferRecVo queryByOriginalId(Long originalId){
        return baseMapper.selectVoOne(new LambdaQueryWrapper<MetaTransferRec>().eq(MetaTransferRec::getOriginalId, originalId));
    }

    /**
     * 查询转账记录
     *
     * @param id 主键
     * @return 转账记录
     */
    @Override
    public MetaTransferRecVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询转账记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 转账记录分页列表
     */
    @Override
    public TableDataInfo<MetaTransferRecVo> queryPageList(MetaTransferRecBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MetaTransferRec> lqw = buildQueryWrapper(bo);
        Page<MetaTransferRecVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的转账记录列表
     *
     * @param bo 查询条件
     * @return 转账记录列表
     */
    @Override
    public List<MetaTransferRecVo> queryList(MetaTransferRecBo bo) {
        LambdaQueryWrapper<MetaTransferRec> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MetaTransferRec> buildQueryWrapper(MetaTransferRecBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MetaTransferRec> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MetaTransferRec::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionHash()), MetaTransferRec::getTransactionHash, bo.getTransactionHash());
        lqw.like(StringUtils.isNotBlank(bo.getChainName()), MetaTransferRec::getChainName, bo.getChainName());
        lqw.eq(StringUtils.isNotBlank(bo.getOriginalTable()), MetaTransferRec::getOriginalTable, bo.getOriginalTable());
        lqw.eq(bo.getOriginalId() != null, MetaTransferRec::getOriginalId, bo.getOriginalId());
        lqw.eq(bo.getConfirmationStatus() != null, MetaTransferRec::getConfirmationStatus, bo.getConfirmationStatus());
        lqw.eq(bo.getRequiredConfirmations() != null, MetaTransferRec::getRequiredConfirmations, bo.getRequiredConfirmations());
        lqw.eq(bo.getActualConfirmations() != null, MetaTransferRec::getActualConfirmations, bo.getActualConfirmations());
        lqw.eq(bo.getBlockHeight() != null, MetaTransferRec::getBlockHeight, bo.getBlockHeight());
        lqw.eq(StringUtils.isNotBlank(bo.getBlockHash()), MetaTransferRec::getBlockHash, bo.getBlockHash());
        lqw.eq(bo.getStartTime() != null, MetaTransferRec::getStartTime, bo.getStartTime());
        lqw.eq(bo.getEndTime() != null, MetaTransferRec::getEndTime, bo.getEndTime());
        lqw.eq(bo.getConfirmationTimeMs() != null, MetaTransferRec::getConfirmationTimeMs, bo.getConfirmationTimeMs());
        lqw.eq(bo.getRetryCount() != null, MetaTransferRec::getRetryCount, bo.getRetryCount());
        lqw.eq(bo.getMaxRetries() != null, MetaTransferRec::getMaxRetries, bo.getMaxRetries());
        lqw.eq(bo.getNextRetryTime() != null, MetaTransferRec::getNextRetryTime, bo.getNextRetryTime());
        lqw.eq(bo.getTimeoutSeconds() != null, MetaTransferRec::getTimeoutSeconds, bo.getTimeoutSeconds());
        lqw.eq(bo.getCheckIntervalSeconds() != null, MetaTransferRec::getCheckIntervalSeconds, bo.getCheckIntervalSeconds());
        lqw.eq(StringUtils.isNotBlank(bo.getErrorMessage()), MetaTransferRec::getErrorMessage, bo.getErrorMessage());
        lqw.eq(StringUtils.isNotBlank(bo.getExtraInfo()), MetaTransferRec::getExtraInfo, bo.getExtraInfo());
        lqw.eq(StringUtils.isNotBlank(bo.getRequestId()), MetaTransferRec::getRequestId, bo.getRequestId());
        lqw.eq(StringUtils.isNotBlank(bo.getTransferType()), MetaTransferRec::getTransferType, bo.getTransferType());
        lqw.eq(bo.getEventPriority() != null, MetaTransferRec::getEventPriority, bo.getEventPriority());

        // 支持批量 requestId 查询
        if (params != null && params.containsKey("requestIdList")) {
            Object requestIdListObj = params.get("requestIdList");
            if (requestIdListObj instanceof List<?> rawList) {
                List<String> requestIdList = new ArrayList<>();
                for (Object item : rawList) {
                    if (item instanceof String) {
                        requestIdList.add((String) item);
                    }
                }
                if (!requestIdList.isEmpty()) {
                    lqw.in(MetaTransferRec::getRequestId, requestIdList);
                }
            }
        }

        return lqw;
    }

    /**
     * 新增转账记录
     *
     * @param bo 转账记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MetaTransferRecBo bo) {
        MetaTransferRec add = MapstructUtils.convert(bo, MetaTransferRec.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改转账记录
     *
     * @param bo 转账记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MetaTransferRecBo bo) {
        MetaTransferRec update = MapstructUtils.convert(bo, MetaTransferRec.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MetaTransferRec entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除转账记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
