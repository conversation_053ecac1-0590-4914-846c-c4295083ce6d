package org.dromara.wallet.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.wallet.domain.MetaBep20Cstaddressinfo;
import org.dromara.wallet.domain.bo.MetaBep20CstaddressinfoBo;
import org.dromara.wallet.domain.vo.MetaBep20CstaddressinfoVo;
import org.dromara.wallet.mapper.MetaBep20CstaddressinfoMapper;
import org.dromara.wallet.service.IMetaBep20CstaddressinfoService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ETH客户热钱包地址信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MetaBep20CstaddressinfoServiceImpl implements IMetaBep20CstaddressinfoService {

    private final MetaBep20CstaddressinfoMapper baseMapper;

    /**
     * 查询ETH客户热钱包地址信息
     *
     * @param address 钱包地址
     * @return ETH客户热钱包地址信息
     */
    @Override
    public MetaBep20Cstaddressinfo queryByAddress(String address) {
        return baseMapper.selectOne(new LambdaQueryWrapper<MetaBep20Cstaddressinfo>().eq(MetaBep20Cstaddressinfo::getCstAddress, address));
    }

    /**
     * 查询ETH客户热钱包地址信息
     *
     * @param id 主键
     * @return ETH客户热钱包地址信息
     */
    @Override
    public MetaBep20CstaddressinfoVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询ETH客户热钱包地址信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return ETH客户热钱包地址信息分页列表
     */
    @Override
    public TableDataInfo<MetaBep20CstaddressinfoVo> queryPageList(MetaBep20CstaddressinfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MetaBep20Cstaddressinfo> lqw = buildQueryWrapper(bo);
        Page<MetaBep20CstaddressinfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    @Cacheable(value = "evmCstAddressInfo", key = "'allHashSet'", unless = "#result == null")
    public HashSet<String> queryAllAddressSet() {

        HashSet<String> addressHashSet = new HashSet<>();
        TenantHelper.ignore(() -> {
            addressHashSet.addAll(baseMapper.selectVoList()
                .stream().map(MetaBep20CstaddressinfoVo::getCstAddress)
                .map(String::toLowerCase)  // 转换为小写
                .collect(Collectors.toSet()));
        });
        return addressHashSet;

    }

    /**
     * 查询符合条件的ETH客户热钱包地址信息列表
     *
     * @param bo 查询条件
     * @return ETH客户热钱包地址信息列表
     */
    @Override
    public List<MetaBep20CstaddressinfoVo> queryList(MetaBep20CstaddressinfoBo bo) {
        LambdaQueryWrapper<MetaBep20Cstaddressinfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MetaBep20Cstaddressinfo> buildQueryWrapper(MetaBep20CstaddressinfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MetaBep20Cstaddressinfo> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MetaBep20Cstaddressinfo::getId);
        lqw.eq(bo.getCstId() != null, MetaBep20Cstaddressinfo::getCstId, bo.getCstId());
        lqw.eq(StringUtils.isNotBlank(bo.getCstAddress()), MetaBep20Cstaddressinfo::getCstAddress, bo.getCstAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getCstTrc20private()), MetaBep20Cstaddressinfo::getCstTrc20private, bo.getCstTrc20private());
        lqw.eq(StringUtils.isNotBlank(bo.getCstHexaddress()), MetaBep20Cstaddressinfo::getCstHexaddress, bo.getCstHexaddress());
        return lqw;
    }

    /**
     * 新增ETH客户热钱包地址信息
     *
     * @param bo ETH客户热钱包地址信息
     * @return 是否新增成功
     */
    @Override
    @CacheEvict(value = "evmCstAddressInfo", key = "'allHashSet'")
    public Boolean insertByBo(MetaBep20CstaddressinfoBo bo) {
        MetaBep20Cstaddressinfo add = MapstructUtils.convert(bo, MetaBep20Cstaddressinfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改ETH客户热钱包地址信息
     *
     * @param bo ETH客户热钱包地址信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MetaBep20CstaddressinfoBo bo) {
        MetaBep20Cstaddressinfo update = MapstructUtils.convert(bo, MetaBep20Cstaddressinfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MetaBep20Cstaddressinfo entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除ETH客户热钱包地址信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
