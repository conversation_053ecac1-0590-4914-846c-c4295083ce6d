package org.dromara.wallet.service.processor;

import org.dromara.wallet.config.ChainType;

/**
 * 钱包链处理器接口
 * 统一各链的钱包处理逻辑，采用策略模式设计
 * 
 * <p>每个区块链实现自己的处理逻辑，包括：</p>
 * <ul>
 *   <li>原生代币余额查询和记录</li>
 *   <li>合约代币余额查询和记录</li>
 *   <li>租户信息处理</li>
 *   <li>错误处理和日志记录</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface WalletChainProcessor {
    
    /**
     * 获取支持的链类型
     * 
     * @return 支持的链类型枚举
     */
    ChainType getSupportedChainType();
    
    /**
     * 处理钱包地址的代币余额记录
     * 
     * <p>此方法应该完成以下操作：</p>
     * <ul>
     *   <li>验证钱包地址有效性</li>
     *   <li>查询原生代币余额</li>
     *   <li>查询所有启用的合约代币余额</li>
     *   <li>将余额记录保存到数据库</li>
     *   <li>处理租户信息</li>
     *   <li>记录处理结果和统计信息</li>
     * </ul>
     * 
     * @param walletAddress 钱包地址
     * @param walletTenantId 钱包租户ID，用于多租户隔离
     */
    void processWallet(String walletAddress, String walletTenantId);
    
    /**
     * 获取处理器名称
     * 用于日志记录和调试
     * 
     * @return 处理器名称
     */
    default String getProcessorName() {
        return getSupportedChainType().getCode().toUpperCase() + "WalletProcessor";
    }
    
    /**
     * 检查是否支持指定的链类型
     * 
     * @param chainType 要检查的链类型
     * @return 是否支持
     */
    default boolean supports(ChainType chainType) {
        return getSupportedChainType() == chainType;
    }
}
