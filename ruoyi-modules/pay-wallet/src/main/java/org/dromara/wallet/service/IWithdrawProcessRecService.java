package org.dromara.wallet.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.wallet.domain.WithdrawProcessRec;
import org.dromara.wallet.domain.bo.WithdrawProcessRecBo;
import org.dromara.wallet.domain.vo.WithdrawProcessRecVo;

import java.util.Collection;
import java.util.List;

/**
 * 提款处理记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
public interface IWithdrawProcessRecService {


    WithdrawProcessRec queryByOrderId(String orderId);

    /**
     * 查询提款处理记录
     *
     * @param id 主键
     * @return 提款处理记录
     */
    WithdrawProcessRecVo queryById(Long id);

    /**
     * 分页查询提款处理记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 提款处理记录分页列表
     */
    TableDataInfo<WithdrawProcessRecVo> queryPageList(WithdrawProcessRecBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的提款处理记录列表
     *
     * @param bo 查询条件
     * @return 提款处理记录列表
     */
    List<WithdrawProcessRecVo> queryList(WithdrawProcessRecBo bo);

    /**
     * 新增提款处理记录
     *
     * @param bo 提款处理记录
     * @return 是否新增成功
     */
    Boolean insertByBo(WithdrawProcessRecBo bo);

    /**
     * 修改提款处理记录
     *
     * @param bo 提款处理记录
     * @return 是否修改成功
     */
    Boolean updateByBo(WithdrawProcessRecBo bo);

    /**
     * 校验并批量删除提款处理记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
