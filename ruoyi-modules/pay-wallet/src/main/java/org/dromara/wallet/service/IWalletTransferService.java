package org.dromara.wallet.service;

import org.dromara.wallet.domain.dto.WalletTransferBo;
import org.dromara.wallet.wallet.transfer.dto.UnifiedTransferResult;

/**
 * 钱包转账服务接口
 * 提供统一的转账和归集功能
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
public interface IWalletTransferService {

    /**
     * 执行钱包转账操作
     * 支持归集和转账两种模式：
     * - 归集模式：toAddress为空，目标地址从配置获取
     * - 转账模式：toAddress必填，目标地址由用户指定
     *
     * @param transferBo 转账请求参数
     * @return 转账结果
     */
    UnifiedTransferResult executeTransfer(WalletTransferBo transferBo);
}
