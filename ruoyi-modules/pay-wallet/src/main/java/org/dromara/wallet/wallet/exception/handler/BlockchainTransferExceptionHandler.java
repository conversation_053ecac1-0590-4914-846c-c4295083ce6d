package org.dromara.wallet.wallet.exception.handler;

import cn.hutool.http.HttpStatus;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.wallet.wallet.exception.BlockchainTransferException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 区块链转账异常处理器
 *
 * <p>专门处理区块链转账相关的异常，提供差异化的错误响应。</p>
 * <p>根据errorCode和原始异常类型进行分类处理，记录详细的错误日志。</p>
 *
 * <p>设计特性：</p>
 * <ul>
 *   <li>统一处理所有区块链转账异常</li>
 *   <li>根据错误码提供差异化的HTTP状态码和错误消息</li>
 *   <li>详细记录原始异常信息，便于问题排查</li>
 *   <li>支持网络、配置、业务等不同类型异常的特殊处理</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Slf4j
@RestControllerAdvice
public class BlockchainTransferExceptionHandler {

    /**
     * 区块链转账异常处理
     *
     * <p>统一处理所有区块链转账相关的异常，提供差异化的错误响应。</p>
     * <p>根据errorCode和原始异常类型进行分类处理，记录详细的错误日志。</p>
     *
     * @param e       区块链转账异常
     * @param request HTTP请求对象
     * @return 统一的错误响应
     */
    @ExceptionHandler(BlockchainTransferException.class)
    public R<Void> handleBlockchainTransferException(BlockchainTransferException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        Throwable originalCause = e.getCause();

        // 记录详细的错误信息
        log.error("区块链转账失败 - 请求地址: {}, 链: {}, 错误码: {}, 手续费使用: {}, 手续费金额: {}, 原始异常: {}, 错误信息: {}",
            requestURI,
            e.getChainName(),
            e.getErrorCode(),
            e.isFeeWalletUsed(),
            e.getFeeAttempted(),
            originalCause != null ? originalCause.getClass().getSimpleName() : "无",
            e.getMessage());

        // 根据原始异常类型进行特殊处理和日志记录
        analyzeOriginalException(e, originalCause);

        // 根据错误码返回不同的HTTP状态码和错误消息
        return buildErrorResponse(e, originalCause);
    }

    /**
     * 分析原始异常类型并记录特殊日志
     *
     * @param e             区块链转账异常
     * @param originalCause 原始异常
     */
    private void analyzeOriginalException(BlockchainTransferException e, Throwable originalCause) {
        if (originalCause == null) {
            return;
        }

        String originalExceptionType = originalCause.getClass().getSimpleName();
        String originalMessage = originalCause.getMessage();

        // 网络相关异常
        if (isNetworkException(originalExceptionType)) {
            log.warn("区块链网络连接异常 - 链: {}, 异常类型: {}, 消息: {}",
                e.getChainName(), originalExceptionType, originalMessage);
        }
        // JSON解析异常
        else if (isJsonParseException(originalExceptionType)) {
            log.error("区块链API响应解析异常 - 链: {}, 可能是API格式变化, 异常: {}",
                e.getChainName(), originalMessage);
        }
        // 区块链特定异常
        else if (isBlockchainSpecificException(originalExceptionType)) {
            log.debug("区块链特定异常详情 - 链: {}, 异常类型: {}, 消息: {}",
                e.getChainName(), originalExceptionType, originalMessage);
        }
        // 余额不足相关异常
        else if (isInsufficientFundsException(originalExceptionType, originalMessage)) {
            log.info("余额不足异常 - 链: {}, 异常类型: {}, 消息: {}",
                e.getChainName(), originalExceptionType, originalMessage);
        }
        // 其他未分类异常
        else {
            log.warn("未分类的原始异常 - 链: {}, 异常类型: {}, 消息: {}",
                e.getChainName(), originalExceptionType, originalMessage);
        }
    }

    /**
     * 构建错误响应
     *
     * @param e             区块链转账异常
     * @param originalCause 原始异常
     * @return 错误响应
     */
    private R<Void> buildErrorResponse(BlockchainTransferException e, Throwable originalCause) {
        return switch (e.getErrorCode()) {
            case "INSUFFICIENT_BALANCE" -> {
                log.info("用户余额不足 - 链: {}, 尝试金额: {}", e.getChainName(), e.getFeeAttempted());
                yield R.fail(HttpStatus.HTTP_BAD_REQUEST, "余额不足，无法完成转账");
            }
            case "FEE_WALLET_DISABLED" -> {
                log.warn("手续费钱包未启用 - 链: {}", e.getChainName());
                yield R.fail(HttpStatus.HTTP_BAD_REQUEST, "手续费钱包未启用，无法完成转账");
            }
            case "FEE_WALLET_CONFIG_MISSING" -> {
                log.error("手续费钱包配置缺失 - 链: {}", e.getChainName());
                yield R.fail(HttpStatus.HTTP_INTERNAL_ERROR, "手续费钱包配置缺失，请联系管理员");
            }
            case "FEE_WALLET_INSUFFICIENT_BALANCE" -> {
                log.error("手续费钱包余额不足 - 链: {}, 错误: {}", e.getChainName(), e.getMessage());
                yield R.fail(HttpStatus.HTTP_INTERNAL_ERROR, "手续费钱包余额不足，请联系管理员");
            }
            case "FEE_WALLET_TRANSFER_FAILED" -> {
                log.error("手续费转账失败 - 链: {}, 错误: {}", e.getChainName(), e.getMessage());
                yield R.fail(HttpStatus.HTTP_INTERNAL_ERROR, "手续费转账失败，请稍后重试或联系管理员");
            }
            case "FEE_WALLET_FAILED" -> {
                log.error("手续费钱包操作失败 - 链: {}, 手续费金额: {}", e.getChainName(), e.getFeeAttempted());
                yield R.fail(HttpStatus.HTTP_INTERNAL_ERROR, "手续费钱包操作失败，请联系管理员");
            }
            case "VERIFICATION_FAILED" -> {
                log.warn("转账验证失败 - 链: {}, 可能需要稍后查询", e.getChainName());
                yield R.fail(HttpStatus.HTTP_ACCEPTED, "转账已提交但验证失败，请稍后查询交易状态");
            }
            case "TRANSACTION_EXECUTION_FAILED" -> {
                log.error("交易执行失败 - 链: {}, 错误: {}", e.getChainName(), e.getMessage());
                yield R.fail(HttpStatus.HTTP_BAD_REQUEST, "交易执行失败: " + extractUserFriendlyError(e.getMessage()));
            }
            case "UNSUPPORTED_TOKEN" -> {
                log.warn("不支持的代币 - 链: {}, 错误: {}", e.getChainName(), e.getMessage());
                yield R.fail(HttpStatus.HTTP_BAD_REQUEST, "不支持的代币类型");
            }
            case "CONFIGURATION_ERROR" -> {
                log.error("区块链配置错误 - 链: {}, 错误: {}", e.getChainName(), e.getMessage());
                yield R.fail(HttpStatus.HTTP_INTERNAL_ERROR, "系统配置错误，请联系管理员");
            }
            case "NETWORK_TIMEOUT" -> {
                log.warn("区块链网络超时 - 链: {}", e.getChainName());
                yield R.fail(408, "网络超时，请稍后重试");
            }
            case "TRANSFER_FAILED" -> handleTransferFailedError(e, originalCause);
            case "SERVICE_ERROR" -> {
                log.error("区块链服务异常 - 链: {}", e.getChainName());
                yield R.fail(HttpStatus.HTTP_INTERNAL_ERROR, "转账服务异常，请稍后重试");
            }
            default -> {
                log.error("未知的区块链转账错误码 - 链: {}, 错误码: {}", e.getChainName(), e.getErrorCode());
                yield R.fail(HttpStatus.HTTP_INTERNAL_ERROR, "转账失败: " + e.getMessage());
            }
        };
    }

    /**
     * 处理转账失败错误，根据原始异常提供更具体的错误信息
     *
     * @param e             区块链转账异常
     * @param originalCause 原始异常
     * @return 错误响应
     */
    private R<Void> handleTransferFailedError(BlockchainTransferException e, Throwable originalCause) {
        if (originalCause != null) {
            String originalType = originalCause.getClass().getSimpleName();
            String originalMessage = originalCause.getMessage();

            // 余额不足
            if (isInsufficientFundsException(originalType, originalMessage)) {
                return R.fail(HttpStatus.HTTP_BAD_REQUEST, "余额不足，无法完成转账");
            }
            // 网络超时
            else if (isNetworkException(originalType)) {
                return R.fail(408, "转账超时，请稍后重试");
            }
            // Gas费用相关
            else if (isGasRelatedError(originalType, originalMessage)) {
                return R.fail(HttpStatus.HTTP_BAD_REQUEST, "Gas费用不足或设置错误");
            }
            // 合约执行失败
            else if (isContractExecutionError(originalType, originalMessage)) {
                return R.fail(HttpStatus.HTTP_BAD_REQUEST, "智能合约执行失败，请检查转账参数");
            }
        }

        return R.fail(HttpStatus.HTTP_INTERNAL_ERROR, "转账失败，请重试");
    }

    // ==================== 异常类型判断方法 ====================

    /**
     * 判断是否为网络相关异常
     */
    private boolean isNetworkException(String exceptionType) {
        return exceptionType.contains("Timeout") ||
               exceptionType.contains("Connect") ||
               exceptionType.contains("Socket") ||
               exceptionType.contains("Network");
    }

    /**
     * 判断是否为JSON解析异常
     */
    private boolean isJsonParseException(String exceptionType) {
        return exceptionType.contains("Json") ||
               exceptionType.contains("Parse") ||
               exceptionType.contains("Serialization");
    }

    /**
     * 判断是否为区块链特定异常
     */
    private boolean isBlockchainSpecificException(String exceptionType) {
        return exceptionType.contains("Tron") ||
               exceptionType.contains("Evm") ||
               exceptionType.contains("Solana") ||
               exceptionType.contains("Ethereum") ||
               exceptionType.contains("Bitcoin");
    }

    /**
     * 判断是否为余额不足异常
     * 增强版：支持识别ERC-6093标准的ERC20InsufficientBalance错误码
     */
    private boolean isInsufficientFundsException(String exceptionType, String message) {
        if (message == null) {
            return false;
        }

        String lowerMessage = message.toLowerCase();

        // 检查ERC-6093标准的ERC20InsufficientBalance错误码 (0xe450d38c)
        if (lowerMessage.contains("0xe450d38c")) {
            return true;
        }

        // 检查TRON特有的余额不足错误信息
        if (lowerMessage.contains("validate transfer error") ||
            lowerMessage.contains("account does not exist") ||
            lowerMessage.contains("contract_validate_error") ||
            lowerMessage.contains("insufficient_balance") ||
            lowerMessage.contains("not enough")) {
            return true;
        }

        // 检查传统的余额不足错误信息
        return exceptionType.contains("Insufficient") ||
               lowerMessage.contains("insufficient") ||
               lowerMessage.contains("balance") ||
               lowerMessage.contains("funds") ||
               lowerMessage.contains("余额不足");
    }

    /**
     * 判断是否为Gas相关错误
     */
    private boolean isGasRelatedError(String exceptionType, String message) {
        if (message == null) {
            return false;
        }

        String lowerMessage = message.toLowerCase();
        return lowerMessage.contains("gas") ||
               lowerMessage.contains("fee") ||
               lowerMessage.contains("energy") ||
               lowerMessage.contains("bandwidth");
    }

    /**
     * 判断是否为合约执行错误
     */
    private boolean isContractExecutionError(String exceptionType, String message) {
        if (message == null) {
            return false;
        }

        String lowerMessage = message.toLowerCase();
        return lowerMessage.contains("contract") ||
               lowerMessage.contains("revert") ||
               lowerMessage.contains("execution") ||
               lowerMessage.contains("transaction failed");
    }

    /**
     * 提取用户友好的错误信息
     * 增强版：支持识别ERC-6093标准的ERC20InsufficientBalance错误码
     */
    private String extractUserFriendlyError(String errorMessage) {
        if (errorMessage == null) {
            return "未知错误";
        }

        // 优先处理ERC-6093标准的ERC20InsufficientBalance错误码 (0xe450d38c)
        if (errorMessage.toLowerCase().contains("0xe450d38c")) {
            return parseErc20InsufficientBalanceError(errorMessage);
        }

        // 处理TRON特有的错误信息
        if (errorMessage.toLowerCase().contains("validate transfer error")) {
            return "TRON转账验证失败，可能是余额不足或参数错误";
        }

        if (errorMessage.toLowerCase().contains("account does not exist")) {
            return "TRON账户不存在或余额为零";
        }

        if (errorMessage.toLowerCase().contains("contract_validate_error")) {
            return "TRON合约验证失败，请检查余额和参数";
        }

        // 处理常见的智能合约错误
        if (errorMessage.contains("REVERT opcode executed")) {
            return "智能合约执行被回滚，可能是余额不足或条件不满足";
        }

        if (errorMessage.contains("OUT_OF_ENERGY")) {
            return "能量不足，请确保账户有足够的TRX或能量";
        }

        if (errorMessage.contains("OUT_OF_TIME")) {
            return "交易执行超时";
        }

        if (errorMessage.contains("INSUFFICIENT_BALANCE")) {
            return "余额不足";
        }

        if (errorMessage.contains("CONTRACT_VALIDATE_ERROR")) {
            return "合约验证失败，请检查转账参数";
        }

        // 如果包含"执行错误:"，提取后面的内容
        int errorIndex = errorMessage.indexOf("执行错误:");
        if (errorIndex != -1) {
            String extractedError = errorMessage.substring(errorIndex + 5).trim();
            if (!extractedError.isEmpty()) {
                return extractedError;
            }
        }

        // 默认返回简化的错误信息
        return "交易执行失败，请检查转账参数或稍后重试";
    }

    /**
     * 解析ERC20InsufficientBalance错误的用户友好信息
     * 错误格式：0xe450d38c + 发送方地址(32字节) + 当前余额(32字节) + 尝试转账金额(32字节)
     *
     * @param errorMessage 包含错误码的错误信息
     * @return 用户友好的错误描述
     */
    private String parseErc20InsufficientBalanceError(String errorMessage) {
        try {
            // 查找错误码位置
            int errorCodeIndex = errorMessage.toLowerCase().indexOf("0xe450d38c");
            if (errorCodeIndex == -1) {
                return "代币余额不足";
            }

            // 提取错误数据部分
            String errorData = errorMessage.substring(errorCodeIndex + 10); // 跳过"0xe450d38c"
            if (errorData.length() < 192) { // 3 * 64 = 192个十六进制字符
                return "代币余额不足";
            }

            // 解析当前余额和尝试转账金额（简化版，只显示是否为零余额）
            String currentBalanceHex = errorData.substring(64, 128);
            java.math.BigInteger currentBalance = new java.math.BigInteger(currentBalanceHex, 16);

            if (currentBalance.equals(java.math.BigInteger.ZERO)) {
                return "代币余额不足：当前余额为0";
            } else {
                return "代币余额不足：余额不够支付转账金额";
            }

        } catch (Exception parseException) {
            // 解析失败时返回通用错误信息
            return "代币余额不足";
        }
    }
}
