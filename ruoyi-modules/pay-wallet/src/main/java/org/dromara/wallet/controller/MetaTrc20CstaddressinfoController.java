package org.dromara.wallet.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.wallet.domain.vo.MetaTrc20CstaddressinfoVo;
import org.dromara.wallet.domain.bo.MetaTrc20CstaddressinfoBo;
import org.dromara.wallet.service.IMetaTrc20CstaddressinfoService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * TRON客户热钱包地址信息
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/wallet/trc20Cstaddressinfo")
public class MetaTrc20CstaddressinfoController extends BaseController {

    private final IMetaTrc20CstaddressinfoService metaTrc20CstaddressinfoService;

    /**
     * 查询TRON客户热钱包地址信息列表
     */
    @SaCheckPermission("wallet:trc20Cstaddressinfo:list")
    @GetMapping("/list")
    public TableDataInfo<MetaTrc20CstaddressinfoVo> list(MetaTrc20CstaddressinfoBo bo, PageQuery pageQuery) {
        return metaTrc20CstaddressinfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出TRON客户热钱包地址信息列表
     */
    @SaCheckPermission("wallet:trc20Cstaddressinfo:export")
    @Log(title = "TRON客户热钱包地址信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MetaTrc20CstaddressinfoBo bo, HttpServletResponse response) {
        List<MetaTrc20CstaddressinfoVo> list = metaTrc20CstaddressinfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "TRON客户热钱包地址信息", MetaTrc20CstaddressinfoVo.class, response);
    }

    /**
     * 获取TRON客户热钱包地址信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("wallet:trc20Cstaddressinfo:query")
    @GetMapping("/{id}")
    public R<MetaTrc20CstaddressinfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(metaTrc20CstaddressinfoService.queryById(id));
    }

    /**
     * 新增TRON客户热钱包地址信息
     */
    @SaCheckPermission("wallet:trc20Cstaddressinfo:add")
    @Log(title = "TRON客户热钱包地址信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MetaTrc20CstaddressinfoBo bo) {
        return toAjax(metaTrc20CstaddressinfoService.insertByBo(bo));
    }

    /**
     * 修改TRON客户热钱包地址信息
     */
    @SaCheckPermission("wallet:trc20Cstaddressinfo:edit")
    @Log(title = "TRON客户热钱包地址信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MetaTrc20CstaddressinfoBo bo) {
        return toAjax(metaTrc20CstaddressinfoService.updateByBo(bo));
    }

    /**
     * 删除TRON客户热钱包地址信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("wallet:trc20Cstaddressinfo:remove")
    @Log(title = "TRON客户热钱包地址信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(metaTrc20CstaddressinfoService.deleteWithValidByIds(List.of(ids), true));
    }
}
