package org.dromara.wallet.wallet.transfer.exception;

import org.dromara.wallet.wallet.exception.WalletException;

/**
 * Solana转账异常分类体系
 *
 * <p>提供Solana链转账过程中的详细异常分类，支持：</p>
 * <ul>
 *   <li>异常类型自动识别和分类</li>
 *   <li>差异化重试策略支持</li>
 *   <li>详细错误信息和恢复建议</li>
 *   <li>监控告警级别自动判定</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
public class SolanaTransferException extends WalletException {

    /**
     * 异常类型枚举
     */
    public enum ErrorType {
        // 网络相关异常
        NETWORK_ERROR("网络异常", true, 3, "WARN"),
        NETWORK_TIMEOUT("网络超时", true, 2, "WARN"),
        RPC_ERROR("RPC调用失败", true, 3, "ERROR"),
        
        // 余额和费用异常
        INSUFFICIENT_BALANCE("余额不足", false, 0, "INFO"),
        INSUFFICIENT_RENT("租金不足", true, 1, "WARN"),
        INSUFFICIENT_LAMPORTS("Lamports不足", true, 1, "WARN"),
        
        // 交易相关异常
        TRANSACTION_FAILED("交易执行失败", false, 1, "ERROR"),
        PROGRAM_ERROR("程序执行错误", false, 0, "ERROR"),
        INVALID_ADDRESS("地址格式错误", false, 0, "ERROR"),
        INVALID_AMOUNT("金额格式错误", false, 0, "ERROR"),
        BLOCKHASH_NOT_FOUND("区块哈希未找到", true, 2, "WARN"),
        
        // 账户相关异常
        ACCOUNT_NOT_FOUND("账户不存在", false, 0, "ERROR"),
        ACCOUNT_ALREADY_EXISTS("账户已存在", false, 0, "ERROR"),
        INVALID_ACCOUNT_OWNER("账户所有者无效", false, 0, "ERROR"),
        
        // 确认相关异常
        CONFIRMATION_TIMEOUT("确认超时", true, 2, "WARN"),
        CONFIRMATION_FAILED("确认失败", true, 1, "WARN"),
        
        // 配置和系统异常
        CONFIG_ERROR("配置错误", false, 0, "ERROR"),
        SYSTEM_ERROR("系统异常", true, 1, "CRITICAL"),
        
        // 未知异常
        UNKNOWN_ERROR("未知异常", true, 1, "ERROR");

        private final String description;
        private final boolean retryable;
        private final int maxRetries;
        private final String alertLevel;

        ErrorType(String description, boolean retryable, int maxRetries, String alertLevel) {
            this.description = description;
            this.retryable = retryable;
            this.maxRetries = maxRetries;
            this.alertLevel = alertLevel;
        }

        public String getDescription() { return description; }
        public boolean isRetryable() { return retryable; }
        public int getMaxRetries() { return maxRetries; }
        public String getAlertLevel() { return alertLevel; }
    }

    private final ErrorType errorType;
    private final String txHash;
    private final String recoveryAdvice;

    /**
     * 构造函数
     */
    public SolanaTransferException(ErrorType errorType, String message) {
        this(errorType, message, null, null, null);
    }

    public SolanaTransferException(ErrorType errorType, String message, Throwable cause) {
        this(errorType, message, cause, null, null);
    }

    public SolanaTransferException(ErrorType errorType, String message, String txHash) {
        this(errorType, message, null, txHash, null);
    }

    public SolanaTransferException(ErrorType errorType, String message, Throwable cause, String txHash, String recoveryAdvice) {
        super(message, cause);
        this.errorType = errorType;
        this.txHash = txHash;
        this.recoveryAdvice = recoveryAdvice != null ? recoveryAdvice : generateRecoveryAdvice(errorType);
    }

    // ==================== 静态工厂方法 ====================

    /**
     * 网络异常
     */
    public static SolanaTransferException networkError(String message, Throwable cause) {
        return new SolanaTransferException(ErrorType.NETWORK_ERROR, message, cause);
    }

    /**
     * 余额不足异常
     */
    public static SolanaTransferException insufficientBalance(String address, String requiredAmount) {
        String message = String.format("地址 %s 余额不足，需要 %s SOL", address, requiredAmount);
        return new SolanaTransferException(ErrorType.INSUFFICIENT_BALANCE, message);
    }

    /**
     * Lamports不足异常
     */
    public static SolanaTransferException insufficientLamports(String address, long requiredLamports) {
        String message = String.format("地址 %s Lamports不足，需要 %d lamports", address, requiredLamports);
        return new SolanaTransferException(ErrorType.INSUFFICIENT_LAMPORTS, message);
    }

    /**
     * 交易失败异常
     */
    public static SolanaTransferException transactionFailed(String txHash, String reason) {
        String message = String.format("交易执行失败: %s", reason);
        return new SolanaTransferException(ErrorType.TRANSACTION_FAILED, message, txHash);
    }

    /**
     * 程序错误异常
     */
    public static SolanaTransferException programError(String txHash, String programId, String errorCode) {
        String message = String.format("程序执行错误: programId=%s, errorCode=%s", programId, errorCode);
        return new SolanaTransferException(ErrorType.PROGRAM_ERROR, message, txHash);
    }

    /**
     * 确认超时异常
     */
    public static SolanaTransferException confirmationTimeout(String txHash, int timeoutSeconds) {
        String message = String.format("交易确认超时，等待时间超过 %d 秒", timeoutSeconds);
        return new SolanaTransferException(ErrorType.CONFIRMATION_TIMEOUT, message, txHash);
    }

    /**
     * 账户不存在异常
     */
    public static SolanaTransferException accountNotFound(String address) {
        String message = String.format("账户不存在: %s", address);
        return new SolanaTransferException(ErrorType.ACCOUNT_NOT_FOUND, message);
    }

    /**
     * 从通用异常自动识别类型
     */
    public static SolanaTransferException fromGenericException(Throwable cause, String txHash) {
        String message = cause.getMessage();
        if (message == null) {
            message = cause.getClass().getSimpleName();
        }

        ErrorType errorType = identifyErrorType(message);
        return new SolanaTransferException(errorType, message, cause, txHash, null);
    }

    // ==================== 辅助方法 ====================

    /**
     * 根据错误信息自动识别异常类型
     */
    private static ErrorType identifyErrorType(String errorMessage) {
        if (errorMessage == null) {
            return ErrorType.UNKNOWN_ERROR;
        }

        String lowerMessage = errorMessage.toLowerCase();

        // 网络相关
        if (lowerMessage.contains("timeout") || lowerMessage.contains("超时")) {
            return ErrorType.NETWORK_TIMEOUT;
        }
        if (lowerMessage.contains("network") || lowerMessage.contains("connection") || lowerMessage.contains("网络")) {
            return ErrorType.NETWORK_ERROR;
        }

        // 余额和费用相关
        if (lowerMessage.contains("insufficient funds") || lowerMessage.contains("余额不足")) {
            return ErrorType.INSUFFICIENT_BALANCE;
        }
        if (lowerMessage.contains("insufficient lamports") || lowerMessage.contains("lamports")) {
            return ErrorType.INSUFFICIENT_LAMPORTS;
        }
        if (lowerMessage.contains("rent") || lowerMessage.contains("租金")) {
            return ErrorType.INSUFFICIENT_RENT;
        }

        // 账户相关
        if (lowerMessage.contains("account not found") || lowerMessage.contains("账户不存在")) {
            return ErrorType.ACCOUNT_NOT_FOUND;
        }
        if (lowerMessage.contains("account already exists") || lowerMessage.contains("账户已存在")) {
            return ErrorType.ACCOUNT_ALREADY_EXISTS;
        }
        if (lowerMessage.contains("invalid account owner")) {
            return ErrorType.INVALID_ACCOUNT_OWNER;
        }

        // 交易相关
        if (lowerMessage.contains("program error") || lowerMessage.contains("程序错误")) {
            return ErrorType.PROGRAM_ERROR;
        }
        if (lowerMessage.contains("blockhash not found")) {
            return ErrorType.BLOCKHASH_NOT_FOUND;
        }
        if (lowerMessage.contains("invalid address") || lowerMessage.contains("地址")) {
            return ErrorType.INVALID_ADDRESS;
        }
        if (lowerMessage.contains("invalid amount") || lowerMessage.contains("金额")) {
            return ErrorType.INVALID_AMOUNT;
        }

        return ErrorType.UNKNOWN_ERROR;
    }

    /**
     * 生成恢复建议
     */
    private static String generateRecoveryAdvice(ErrorType errorType) {
        return switch (errorType) {
            case NETWORK_ERROR, NETWORK_TIMEOUT -> "检查网络连接，稍后重试";
            case RPC_ERROR -> "检查Solana节点状态，考虑切换节点";
            case INSUFFICIENT_BALANCE -> "充值SOL到发送地址";
            case INSUFFICIENT_LAMPORTS -> "充值SOL用于交易费用";
            case INSUFFICIENT_RENT -> "充值SOL用于账户租金";
            case TRANSACTION_FAILED -> "检查交易参数，确认网络状态";
            case PROGRAM_ERROR -> "检查程序调用参数和权限";
            case INVALID_ADDRESS -> "验证地址格式是否正确";
            case INVALID_AMOUNT -> "验证转账金额是否有效";
            case BLOCKHASH_NOT_FOUND -> "获取最新区块哈希重试";
            case ACCOUNT_NOT_FOUND -> "确认账户地址是否正确";
            case ACCOUNT_ALREADY_EXISTS -> "使用不同的账户地址";
            case INVALID_ACCOUNT_OWNER -> "检查账户所有者权限";
            case CONFIRMATION_TIMEOUT -> "延长确认等待时间或检查网络";
            case CONFIRMATION_FAILED -> "手动查询交易状态";
            case CONFIG_ERROR -> "检查Solana配置参数";
            case SYSTEM_ERROR -> "联系技术支持";
            default -> "查看详细错误信息，联系技术支持";
        };
    }

    // ==================== Getter 方法 ====================

    public ErrorType getErrorType() {
        return errorType;
    }

    public String getTxHash() {
        return txHash;
    }

    public String getRecoveryAdvice() {
        return recoveryAdvice;
    }

    public boolean isRetryable() {
        return errorType.isRetryable();
    }

    public int getMaxRetries() {
        return errorType.getMaxRetries();
    }

    public String getAlertLevel() {
        return errorType.getAlertLevel();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("SolanaTransferException{");
        sb.append("type=").append(errorType.name());
        sb.append(", description='").append(errorType.getDescription()).append("'");
        if (txHash != null) {
            sb.append(", txHash='").append(txHash).append("'");
        }
        sb.append(", message='").append(getMessage()).append("'");
        sb.append(", retryable=").append(isRetryable());
        sb.append(", recoveryAdvice='").append(recoveryAdvice).append("'");
        sb.append("}");
        return sb.toString();
    }
}
