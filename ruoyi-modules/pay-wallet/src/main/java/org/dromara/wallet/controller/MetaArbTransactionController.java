package org.dromara.wallet.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.wallet.domain.vo.MetaArbTransactionVo;
import org.dromara.wallet.domain.bo.MetaArbTransactionBo;
import org.dromara.wallet.service.IMetaArbTransactionService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * ARB区块高度交易明细
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/wallet/arbTransactions")
public class MetaArbTransactionController extends BaseController {

    private final IMetaArbTransactionService metaArbTransactionService;

    /**
     * 查询ARB区块高度交易明细列表
     */
    @SaCheckPermission("wallet:arbTransactions:list")
    @GetMapping("/list")
    public TableDataInfo<MetaArbTransactionVo> list(MetaArbTransactionBo bo, PageQuery pageQuery) {
        return metaArbTransactionService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出ARB区块高度交易明细列表
     */
    @SaCheckPermission("wallet:arbTransactions:export")
    @Log(title = "ARB区块高度交易明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MetaArbTransactionBo bo, HttpServletResponse response) {
        List<MetaArbTransactionVo> list = metaArbTransactionService.queryList(bo);
        ExcelUtil.exportExcel(list, "ARB区块高度交易明细", MetaArbTransactionVo.class, response);
    }

    /**
     * 获取ARB区块高度交易明细详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("wallet:arbTransactions:query")
    @GetMapping("/{id}")
    public R<MetaArbTransactionVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(metaArbTransactionService.queryById(id));
    }

    /**
     * 新增ARB区块高度交易明细
     */
    @SaCheckPermission("wallet:arbTransactions:add")
    @Log(title = "ARB区块高度交易明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MetaArbTransactionBo bo) {
        return toAjax(metaArbTransactionService.insertByBo(bo));
    }

    /**
     * 修改ARB区块高度交易明细
     */
    @SaCheckPermission("wallet:arbTransactions:edit")
    @Log(title = "ARB区块高度交易明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MetaArbTransactionBo bo) {
        return toAjax(metaArbTransactionService.updateByBo(bo));
    }

    /**
     * 删除ARB区块高度交易明细
     *
     * @param ids 主键串
     */
    @SaCheckPermission("wallet:arbTransactions:remove")
    @Log(title = "ARB区块高度交易明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(metaArbTransactionService.deleteWithValidByIds(List.of(ids), true));
    }
}
