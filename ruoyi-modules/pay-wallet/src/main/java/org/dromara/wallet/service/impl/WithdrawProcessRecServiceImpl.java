package org.dromara.wallet.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.wallet.domain.WithdrawProcessRec;
import org.dromara.wallet.domain.bo.WithdrawProcessRecBo;
import org.dromara.wallet.domain.vo.WithdrawProcessRecVo;
import org.dromara.wallet.mapper.WithdrawProcessRecMapper;
import org.dromara.wallet.service.IWithdrawProcessRecService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 提款处理记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WithdrawProcessRecServiceImpl implements IWithdrawProcessRecService {

    private final WithdrawProcessRecMapper baseMapper;


    /**
     * 根据orderId查询提款处理记录
     *
     * @param orderId 订单id（外部id）
     * @return 提款处理记录
     */
    @Override
    public WithdrawProcessRec queryByOrderId(String orderId) {
        return baseMapper.selectOne(new LambdaQueryWrapper<WithdrawProcessRec>().eq(WithdrawProcessRec::getOrderId, orderId));
    }

    /**
     * 查询提款处理记录
     *
     * @param id 主键
     * @return 提款处理记录
     */
    @Override
    public WithdrawProcessRecVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询提款处理记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 提款处理记录分页列表
     */
    @Override
    public TableDataInfo<WithdrawProcessRecVo> queryPageList(WithdrawProcessRecBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WithdrawProcessRec> lqw = buildQueryWrapper(bo);
        Page<WithdrawProcessRecVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的提款处理记录列表
     *
     * @param bo 查询条件
     * @return 提款处理记录列表
     */
    @Override
    public List<WithdrawProcessRecVo> queryList(WithdrawProcessRecBo bo) {
        LambdaQueryWrapper<WithdrawProcessRec> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WithdrawProcessRec> buildQueryWrapper(WithdrawProcessRecBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WithdrawProcessRec> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(WithdrawProcessRec::getId);
        lqw.eq(bo.getOrderId() != null, WithdrawProcessRec::getOrderId, bo.getOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getTxid()), WithdrawProcessRec::getTxid, bo.getTxid());
        lqw.eq(bo.getStatus() != null, WithdrawProcessRec::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增提款处理记录
     *
     * @param bo 提款处理记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WithdrawProcessRecBo bo) {
        WithdrawProcessRec add = MapstructUtils.convert(bo, WithdrawProcessRec.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            //触发转账事件

        }
        return flag;
    }

    /**
     * 修改提款处理记录
     *
     * @param bo 提款处理记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WithdrawProcessRecBo bo) {
        WithdrawProcessRec update = MapstructUtils.convert(bo, WithdrawProcessRec.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WithdrawProcessRec entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除提款处理记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
