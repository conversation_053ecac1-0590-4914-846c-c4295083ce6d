package org.dromara.wallet.wallet.transfer.monitor;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * EVM转账监控报告数据类
 *
 * <p>封装EVM兼容链转账监控的完整统计信息，用于：</p>
 * <ul>
 *   <li>监控数据的结构化存储</li>
 *   <li>告警系统的数据源</li>
 *   <li>运营报表的数据基础</li>
 *   <li>多链性能分析和优化决策</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
@Data
@Builder
public class EvmTransferMonitorReport {

    // ==================== 基础统计 ====================

    /**
     * 总转账次数
     */
    private long totalTransfers;

    /**
     * 成功转账次数
     */
    private long successfulTransfers;

    /**
     * 失败转账次数
     */
    private long failedTransfers;

    /**
     * 成功率（百分比）
     */
    private double successRate;

    /**
     * 失败率（百分比）
     */
    private double failureRate;

    // ==================== 性能指标 ====================

    /**
     * 平均转账时间（毫秒）
     */
    private double averageTransferTime;

    /**
     * 平均确认时间（毫秒）
     */
    private double averageConfirmationTime;

    /**
     * 最快转账时间（毫秒）
     */
    private long fastestTransferTime;

    /**
     * 最慢转账时间（毫秒）
     */
    private long slowestTransferTime;

    // ==================== 分类统计 ====================

    /**
     * 原生代币转账次数
     */
    private long nativeTransfers;

    /**
     * ERC20代币转账次数
     */
    private long tokenTransfers;

    /**
     * Gas费提供次数
     */
    private long gasProvisionCount;

    /**
     * 总Gas费消耗（ETH单位）
     */
    private BigDecimal totalGasConsumed;

    /**
     * 平均Gas费（ETH单位）
     */
    private BigDecimal averageGas;

    /**
     * Gas费提供率（百分比）
     */
    private double gasProvisionRate;

    // ==================== 多链统计 ====================

    /**
     * 各链转账统计
     */
    private Map<String, AtomicLong> chainTransferCount;

    /**
     * 异常类型统计
     */
    private Map<String, AtomicInteger> exceptionTypeCount;

    // ==================== 时间信息 ====================

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    // ==================== 报告生成方法 ====================

    /**
     * 生成详细的文本报告
     */
    public String generateDetailedReport() {
        StringBuilder report = new StringBuilder();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        report.append("=== EVM转账监控报告 ===\n");
        report.append("报告时间: ").append(lastUpdateTime.format(formatter)).append("\n\n");

        // 基础统计
        report.append("【基础统计】\n");
        report.append(String.format("总转账次数: %d\n", totalTransfers));
        report.append(String.format("成功转账: %d (%.2f%%)\n", successfulTransfers, successRate));
        report.append(String.format("失败转账: %d (%.2f%%)\n", failedTransfers, failureRate));
        report.append("\n");

        // 性能指标
        report.append("【性能指标】\n");
        report.append(String.format("平均转账时间: %.0f ms\n", averageTransferTime));
        report.append(String.format("平均确认时间: %.0f ms\n", averageConfirmationTime));
        report.append(String.format("最快转账时间: %d ms\n", fastestTransferTime));
        report.append(String.format("最慢转账时间: %d ms\n", slowestTransferTime));
        report.append("\n");

        // 分类统计
        report.append("【分类统计】\n");
        report.append(String.format("原生代币转账: %d\n", nativeTransfers));
        report.append(String.format("ERC20代币转账: %d\n", tokenTransfers));
        report.append(String.format("Gas费提供次数: %d (%.2f%%)\n", gasProvisionCount, gasProvisionRate));
        report.append(String.format("总Gas费消耗: %s ETH\n", totalGasConsumed.toPlainString()));
        report.append(String.format("平均Gas费: %s ETH\n", averageGas.toPlainString()));
        report.append("\n");

        // 多链统计
        if (chainTransferCount != null && !chainTransferCount.isEmpty()) {
            report.append("【多链统计】\n");
            chainTransferCount.entrySet().stream()
                    .sorted((e1, e2) -> Long.compare(e2.getValue().get(), e1.getValue().get()))
                    .forEach(entry -> report.append(String.format("%s链: %d次\n", 
                            entry.getKey(), entry.getValue().get())));
            report.append("\n");
        }

        // 异常统计
        if (exceptionTypeCount != null && !exceptionTypeCount.isEmpty()) {
            report.append("【异常统计】\n");
            exceptionTypeCount.entrySet().stream()
                    .sorted((e1, e2) -> e2.getValue().get() - e1.getValue().get())
                    .forEach(entry -> report.append(String.format("%s: %d次\n", 
                            entry.getKey(), entry.getValue().get())));
            report.append("\n");
        }

        return report.toString();
    }

    /**
     * 生成简要报告
     */
    public String generateSummaryReport() {
        return String.format(
                "EVM转账监控摘要 [%s]: 总计=%d, 成功率=%.2f%%, 平均耗时=%.0fms, Gas提供率=%.2f%%",
                lastUpdateTime.format(DateTimeFormatter.ofPattern("HH:mm:ss")),
                totalTransfers,
                successRate,
                averageTransferTime,
                gasProvisionRate
        );
    }

    /**
     * 生成告警级别的报告
     */
    public String generateAlertReport() {
        StringBuilder alert = new StringBuilder();
        alert.append("🚨 EVM转账异常告警 🚨\n");
        alert.append("时间: ").append(lastUpdateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");

        // 检查失败率
        if (failureRate > 10.0 && totalTransfers >= 10) {
            alert.append(String.format("⚠️ 失败率过高: %.2f%% (%d/%d)\n", 
                    failureRate, failedTransfers, totalTransfers));
        }

        // 检查转账时间
        if (averageTransferTime > 30000 && totalTransfers >= 5) {
            alert.append(String.format("⚠️ 转账耗时过长: 平均%.0fms\n", averageTransferTime));
        }

        // 检查最慢转账
        if (slowestTransferTime > 60000) {
            alert.append(String.format("⚠️ 发现超慢转账: %dms\n", slowestTransferTime));
        }

        // 检查Gas费异常
        if (averageGas.compareTo(new BigDecimal("0.01")) > 0) {
            alert.append(String.format("⚠️ Gas费过高: 平均%s ETH\n", averageGas.toPlainString()));
        }

        // 检查异常频率
        if (exceptionTypeCount != null && !exceptionTypeCount.isEmpty()) {
            int totalExceptions = exceptionTypeCount.values().stream()
                    .mapToInt(AtomicInteger::get)
                    .sum();
            if (totalExceptions > totalTransfers * 0.1) {
                alert.append(String.format("⚠️ 异常频率过高: %d次异常\n", totalExceptions));
                
                // 列出主要异常类型
                exceptionTypeCount.entrySet().stream()
                        .filter(entry -> entry.getValue().get() >= 3)
                        .sorted((e1, e2) -> e2.getValue().get() - e1.getValue().get())
                        .limit(3)
                        .forEach(entry -> alert.append(String.format("   - %s: %d次\n", 
                                entry.getKey(), entry.getValue().get())));
            }
        }

        return alert.toString();
    }

    /**
     * 检查是否需要告警
     */
    public boolean needsAlert() {
        // 失败率超过10%且有足够样本
        if (failureRate > 10.0 && totalTransfers >= 10) {
            return true;
        }

        // 平均转账时间超过30秒
        if (averageTransferTime > 30000 && totalTransfers >= 5) {
            return true;
        }

        // 最慢转账超过1分钟
        if (slowestTransferTime > 60000) {
            return true;
        }

        // Gas费过高
        if (averageGas.compareTo(new BigDecimal("0.01")) > 0 && gasProvisionCount >= 5) {
            return true;
        }

        // 异常频率过高
        if (exceptionTypeCount != null && !exceptionTypeCount.isEmpty()) {
            int totalExceptions = exceptionTypeCount.values().stream()
                    .mapToInt(AtomicInteger::get)
                    .sum();
            if (totalExceptions > totalTransfers * 0.1 && totalTransfers >= 10) {
                return true;
            }
        }

        // 长时间无活动（超过1小时且有历史转账）
        if (lastUpdateTime.isBefore(LocalDateTime.now().minusHours(1)) && totalTransfers > 0) {
            return true;
        }

        return false;
    }

    /**
     * 获取告警级别
     */
    public String getAlertLevel() {
        if (!needsAlert()) {
            return "INFO";
        }

        // 严重告警条件
        if (failureRate > 50.0 || averageTransferTime > 60000 || 
            averageGas.compareTo(new BigDecimal("0.05")) > 0) {
            return "CRITICAL";
        }

        // 错误告警条件
        if (failureRate > 25.0 || averageTransferTime > 45000 ||
            averageGas.compareTo(new BigDecimal("0.02")) > 0) {
            return "ERROR";
        }

        // 警告告警条件
        return "WARN";
    }

    /**
     * 生成JSON格式报告（用于API接口）
     */
    public String toJsonString() {
        return String.format(
                "{\"totalTransfers\":%d,\"successRate\":%.2f,\"failureRate\":%.2f," +
                "\"averageTransferTime\":%.0f,\"averageConfirmationTime\":%.0f," +
                "\"gasProvisionRate\":%.2f,\"averageGas\":\"%s\",\"lastUpdateTime\":\"%s\",\"alertLevel\":\"%s\"}",
                totalTransfers, successRate, failureRate,
                averageTransferTime, averageConfirmationTime,
                gasProvisionRate, averageGas.toPlainString(),
                lastUpdateTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                getAlertLevel()
        );
    }

    /**
     * 获取最活跃的链
     */
    public String getMostActiveChain() {
        if (chainTransferCount == null || chainTransferCount.isEmpty()) {
            return "N/A";
        }
        
        return chainTransferCount.entrySet().stream()
                .max((e1, e2) -> Long.compare(e1.getValue().get(), e2.getValue().get()))
                .map(Map.Entry::getKey)
                .orElse("N/A");
    }

    /**
     * 获取最常见的异常类型
     */
    public String getMostCommonException() {
        if (exceptionTypeCount == null || exceptionTypeCount.isEmpty()) {
            return "N/A";
        }
        
        return exceptionTypeCount.entrySet().stream()
                .max((e1, e2) -> Integer.compare(e1.getValue().get(), e2.getValue().get()))
                .map(Map.Entry::getKey)
                .orElse("N/A");
    }
}
