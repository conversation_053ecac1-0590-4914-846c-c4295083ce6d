# pay-wallet 钱包服务配置 - 新架构版本
# 按照"基础服务、交易处理、监控"三个维度重新组织配置

spring:
  datasource:
    dynamic:
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      seata: false
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${datasource.pay-wallet.url}
          username: ${datasource.pay-wallet.username}
          password: ${datasource.pay-wallet.password}

snail-job:
  enabled: true
  # 需要在 SnailJob 后台组管理创建对应名称的组,然后创建任务的时候选择对应的组,才能正确分派任务
  group: "pay_wallet_group"
  #  SnailJob 接入验证令牌
  token: "SJ_Wyz3dmsdbDOkDujOTSSoBjGQP1BMsVnj"
  server:
    # 从 nacos 获取服务
    server-name: ruoyi-snailjob-server
    # 服务名优先 ip垫底
    #host: **************
    port: 17888
  # 详见 sql/ry_job.sql `sj_namespace` 表 `unique_id`
  namespace: ${spring.profiles.active}
  # 随主应用端口飘逸
  port: 2${server.port}
  # 客户端ip指定
  host: **************
  # RPC类型: netty, grpc
  rpc-type: grpc

# ==================== SOLANA 配置 ====================
solana:
  # 基础服务配置
  base-service:
    enabled: true
    # 钱包基础配置
    wallet:
      main-address-list:
        - tenant-id: "*********"
          address: "65uLDf8bh8JjnJ48bkcVVbyapNgjvZURJU8Dah4HfCCz"
        - tenant-id: "system2"
          address: "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM"
        - tenant-id: "99"
          address: "65uLDf8bh8JjnJ48bkcVVbyapNgjvZURJU8Dah4HfCCz"
    # RPC连接配置
    rpc:
      endpoints:
        - https://aged-chaotic-telescope.solana-devnet.quiknode.pro/
      websocket-url: wss://aged-chaotic-telescope.solana-devnet.quiknode.pro/
      connection-timeout: 30
      read-timeout: 60
    # 合约配置
    contracts:
      sol:
        address: "So11111111111111111111111111111111111111112"
        decimals: 9
        symbol: "SOL"
        name: "Solana"
      usdt:
        address: "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"
        decimals: 6
        symbol: "USDT"
        name: "Tether USD"

  # 交易处理配置
  transaction-processing:
    # 确认配置
    confirmation:
      required-confirmations: 1
      timeout-seconds: 30
      check-interval-seconds: 2
      max-retries: 5
      enable-confirmation: true
    # 手续费配置
    fee:
      max-fee-lamports: 5000000  # 0.005 SOL
      priority-fee-enabled: true
      priority-fee-lamports: 1000
    # 转账限制
    limits:
      min-amount: 0.001
      max-amount: 10000.0
      daily-limit: 50000.0

  # 监控配置
  monitoring:
    enabled: false
    # 交易监控
    transaction:
      enabled: true
      check-interval: 30
      timeout: 300
      retry-count: 3
    # 余额监控
    balance:
      enabled: true
      check-interval: 300
      low-threshold: 0.1
    # 性能监控
    performance:
      enabled: true
      success-rate-warning: 95.0
      success-rate-error: 90.0
      avg-time-warning: 15000
      avg-time-error: 25000
    # 告警配置
    alerts:
      enabled: true
      min-level: "WARN"
      failure-rate-threshold: 10.0

# ==================== TRON 配置 ====================
tron:
  # 基础服务配置
  base-service:
    enabled: true
    # 钱包基础配置
    wallet:
      main-address-list:
        - tenant-id: "*********"
          address: "TBCDpHjD6KdUgJJJ9jxkYNKiGfjsFTiDYU"
        - tenant-id: "system2"
          address: "TMuA6YqfCeX8EhbfYEg5y7S4DqzSJireY9"
      fee-wallet:
        enabled: true
        private-key: "5fd1cd1a7ca6f2b4ce1bf6fce73e21ba8972c4a16216740c8763f09a8bf3be12"
        address: "TJEv4KfgVafkvTqpjsLr97MhQfzDLdYtqJ"
    # API连接配置
    api:
      enabled: true
      mode: "http"  # API模式：rpc 或 http
      network-type: "TESTNET"
      primary-endpoint: "https://api.shasta.trongrid.io"
      backup-endpoints:
        - "https://api.shasta.tronex.io"
      scan-endpoint: "https://shastapi.tronscan.org"
      api-keys:
        - "4081171d-142f-4428-ad11-875e14a7fb3a"
      connection-timeout: 30000
      read-timeout: 60000
      user-agent: "TronWallet/1.0"
    # 合约配置
    contracts:
      usdt:
        address: "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"
        decimals: 6
        symbol: "USDT"
        name: "Tether USD"
      usdc:
        address: "TWxMbkt8ubE1Me6fF2C13jGC39ZtxN6PVe"
        decimals: 6
        symbol: "USDC"
        name: "USD Coin"

  # 交易处理配置
  transaction-processing:
    # 确认配置
    confirmation:
      required-confirmations: 1
      timeout-seconds: 30
      check-interval-seconds: 3
      max-retries: 3
      enable-confirmation: true
    # 手续费配置
    fee:
      max-fee-limit: 5000000        # 5 TRX (最大费用限制)
      max-energy-burn: 50000        # 最大能量燃烧限制
      auto-burn-enabled: true       # 自动燃烧TRX支付能量
      auto-burn-bandwidth-enabled: true  # 自动燃烧TRX支付带宽
      # 能量代理配置
      energy-proxy:
        enabled: false
        url: ""
        key: "key"
        hour: 1
        value: ""
    # 转账限制
    limits:
      min-amount: 1.0
      max-amount: 10000.0
      daily-limit: 50000.0

  # 监控配置
  monitoring:
    enabled: false
    # 交易监控
    transaction:
      enabled: true
      check-interval: 30
      timeout: 300
      retry-count: 3
    # 余额监控
    balance:
      enabled: true
      check-interval: 300
      low-threshold: 10.0
    # 性能监控
    performance:
      enabled: true
      success-rate-warning: 95.0
      success-rate-error: 90.0
      avg-time-warning: 20000
      avg-time-error: 30000

# ==================== BSC 配置 ====================
bsc:
  # 基础服务配置
  base-service:
    enabled: true
    # 钱包基础配置
    wallet:
      main-address-list:
        - tenant-id: "*********"
          address: "******************************************"
      fee-wallet:
        enabled: true
        private-key: "1b4a7cde58f6796916b7b970b8d835a6795654de6233f1085b29c2821046f30f"
        address: "******************************************"
    # RPC连接配置
    rpc:
      enabled: true
      network-type: "TESTNET"
      chain-id: 97  # BSC测试网
      endpoint: "https://capable-sleek-sponge.bsc-testnet.quiknode.pro/e0946ba9546e5a1119b2b904ed407c0e7447bc5b/"
      connection-timeout: 30
      read-timeout: 60
    # 合约配置
    contracts:
      usdt:
        address: "******************************************"
        decimals: 18
        symbol: "USDT"
        name: "Tether USD"
      usdc:
        address: "******************************************"
        decimals: 18
        symbol: "USDC"
        name: "USD Coin"

  # 交易处理配置
  transaction-processing:
    # 确认配置
    confirmation:
      required-confirmations: 12
      timeout-seconds: 600
      check-interval-seconds: 15
      max-retries: 3
      enable-confirmation: true
    # Gas配置
    gas:
      max-gas-price: 20000000000  # 20 gwei
      max-gas-limit: 8000000      # 8M gas
      default-gas-price: 5000000000  # 5 gwei
      token-transfer-gas-limit: 100000
    # 转账限制
    limits:
      min-amount: 0.01
      max-amount: 10000.0
      daily-limit: 50000.0

  # 监控配置
  monitoring:
    enabled: false
    # 交易监控
    transaction:
      enabled: true
      check-interval: 15
      timeout: 600
      retry-count: 3
      required-confirmations: 12
    # 余额监控
    balance:
      enabled: true
      check-interval: 300
      low-bnb-threshold: 0.1
      low-token-threshold: 100.0
    # Gas监控
    gas:
      enabled: true
      check-interval: 60
      high-price-threshold: 50000000000  # 50 gwei
      anomaly-threshold: 0.01
    # 性能监控
    performance:
      enabled: true
      success-rate-warning: 95.0
      success-rate-error: 90.0
      avg-time-warning: 25000
      avg-time-error: 35000

# ==================== ARB 配置 ====================
arb:
  # 基础服务配置
  base-service:
    enabled: true
    # 钱包基础配置
    wallet:
      main-address-list:
        - tenant-id: "*********"
          address: "******************************************"
      fee-wallet:
        enabled: true
        private-key: "${ARB_FEE_WALLET_PRIVATE_KEY:******************************************123456789012345678901234}"
        address: "******************************************"
    # RPC连接配置
    rpc:
      enabled: true
      network-type: "TESTNET"
      chain-id: 421614  # Arbitrum测试网
      endpoint: "https://aged-chaotic-telescope.arbitrum-sepolia.quiknode.pro/"
      connection-timeout: 30
      read-timeout: 60
    # 合约配置
    contracts:
      usdt:
        address: "******************************************"
        decimals: 6
        symbol: "USDT"
        name: "Tether USD"
      usdc:
        address: "******************************************"
        decimals: 6
        symbol: "USDC"
        name: "USD Coin"

  # 交易处理配置
  transaction-processing:
    # 确认配置
    confirmation:
      required-confirmations: 12
      timeout-seconds: 300
      check-interval-seconds: 30
      max-retries: 3
      enable-confirmation: true
    # Gas配置
    gas:
      max-gas-price: 1000000000   # 1 gwei (Layer 2更便宜)
      max-gas-limit: 8000000      # 8M gas
      default-gas-price: 100000000  # 0.1 gwei
      token-transfer-gas-limit: 100000
    # 转账限制
    limits:
      min-amount: 0.01
      max-amount: 10000.0
      daily-limit: 50000.0

  # 监控配置
  monitoring:
    enabled: false
    # 交易监控
    transaction:
      enabled: true
      check-interval: 30
      timeout: 300
      retry-count: 3
    # 余额监控
    balance:
      enabled: true
      check-interval: 300
      low-threshold: 0.1
    # 性能监控
    performance:
      enabled: true
      success-rate-warning: 95.0
      success-rate-error: 90.0
      avg-time-warning: 25000
      avg-time-error: 35000

# ==================== BASE 配置 ====================
base:
  # 基础服务配置
  base-service:
    enabled: true
    # 钱包基础配置
    wallet:
      main-address-list:
        - tenant-id: "*********"
          address: "******************************************"
      fee-wallet:
        enabled: true
        private-key: "${BASE_FEE_WALLET_PRIVATE_KEY:******************************************123456789012345678901234}"
        address: "******************************************"
    # RPC连接配置
    rpc:
      enabled: true
      network-type: "TESTNET"
      chain-id: 84532  # Base测试网
      endpoint: "https://capable-sleek-sponge.base-sepolia.quiknode.pro/e0946ba9546e5a1119b2b904ed407c0e7447bc5b"
      connection-timeout: 30
      read-timeout: 60
    # 合约配置
    contracts:
      usdt:
        address: "******************************************"
        decimals: 6
        symbol: "USDT"
        name: "Tether USD"
      usdc:
        address: "******************************************"
        decimals: 6
        symbol: "USDC"
        name: "USD Coin"

  # 交易处理配置
  transaction-processing:
    # 确认配置
    confirmation:
      required-confirmations: 12
      timeout-seconds: 300
      check-interval-seconds: 30
      max-retries: 3
      enable-confirmation: true
    # Gas配置
    gas:
      max-gas-price: 1000000000   # 1 gwei (Layer 2更便宜)
      max-gas-limit: 8000000      # 8M gas
      default-gas-price: 100000000  # 0.1 gwei
      token-transfer-gas-limit: 100000
    # 转账限制
    limits:
      min-amount: 0.01
      max-amount: 10000.0
      daily-limit: 50000.0

  # 监控配置
  monitoring:
    enabled: false
    # 交易监控
    transaction:
      enabled: true
      check-interval: 30
      timeout: 300
      retry-count: 3
    # 余额监控
    balance:
      enabled: true
      check-interval: 300
      low-threshold: 0.1
    # 性能监控
    performance:
      enabled: true
      success-rate-warning: 95.0
      success-rate-error: 90.0
      avg-time-warning: 25000
      avg-time-error: 35000

# ==================== 全局配置 ====================
# 私钥加密配置
wallet-encryption:
  enabled: true
  secret-key: "${WALLET_ENCRYPTION_SECRET_KEY:MetaWallet123456}"
  algorithm: "AES"
  encoding: "BASE64"

# 全局转账配置
transfer:
  global:
    # 监控总开关
    monitoring-enabled: true
    alerting-enabled: true
    # 性能优化
    async-processing: true
    batch-processing-size: 100
    thread-pool-size: 4
    # 日志配置
    monitoring-log-level: "INFO"
    performance-log-enabled: true
    # 健康检查
    health-check-enabled: true
    health-check-interval-minutes: 5
