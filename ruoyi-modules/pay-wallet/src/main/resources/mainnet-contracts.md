# 主网合约地址清单

本文档记录了各个区块链主网上的代币合约地址，用于生产环境配置。

## 🔍 验证状态说明
- ✅ **已验证**: 官方确认的合约地址
- ⚠️ **需确认**: 需要进一步验证的地址
- ❌ **不支持**: 该网络暂不支持此代币

---

## 📋 主网合约地址

### 1. TRON 主网
- **网络**: TRON Mainnet
- **Chain ID**: N/A (TRON使用不同的标识系统)
- **RPC**: https://api.trongrid.io
- **浏览器**: https://tronscan.org

| 代币 | 合约地址 | 精度 | 状态 |
|------|----------|------|------|
| USDT | `TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t` | 6 | ✅ |
| USDC | `TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8` | 6 | ⚠️ |

### 2. BSC 主网 (Binance Smart Chain)
- **网络**: BSC Mainnet
- **Chain ID**: 56
- **RPC**: https://bsc-dataseed1.binance.org/
- **浏览器**: https://bscscan.com

| 代币 | 合约地址 | 精度 | 状态 |
|------|----------|------|------|
| USDT | `0x55d398326f99059fF775485246999027B3197955` | 18 | ✅ |
| USDC | `0x8AC76a51cc950d9822D68b83fE1Ad97B32Cd580d` | 18 | ⚠️ |

### 3. Arbitrum One 主网
- **网络**: Arbitrum One
- **Chain ID**: 42161
- **RPC**: https://arb1.arbitrum.io/rpc
- **浏览器**: https://arbiscan.io

| 代币 | 合约地址 | 精度 | 状态 |
|------|----------|------|------|
| USDT | `0xFd086bC7CD5C481DCC9C85ebE478A1C0b69FCbb9` | 6 | ✅ |
| USDC | `0xaf88d065e77c8cC2239327C5EDb3A432268e5831` | 6 | ✅ |

### 4. Base 主网
- **网络**: Base Mainnet
- **Chain ID**: 8453
- **RPC**: https://mainnet.base.org
- **浏览器**: https://basescan.org

| 代币 | 合约地址 | 精度 | 状态 |
|------|----------|------|------|
| USDT | N/A | N/A | ❌ |
| USDC | `0x833589fCD6eDb6E08f4c7C32D4f71b54bdA02913` | 6 | ✅ |

### 5. Avalanche C-Chain 主网
- **网络**: Avalanche C-Chain
- **Chain ID**: 43114
- **RPC**: https://api.avax.network/ext/bc/C/rpc
- **浏览器**: https://snowscan.xyz

| 代币 | 合约地址 | 精度 | 状态 |
|------|----------|------|------|
| USDT | `0x9702230A8Ea53601f5cD2dc00fDBc13d4dF4A8c7` | 6 | ✅ |
| USDC | `0xB97EF9Ef8734C71904D8002F8b6Bc66Dd9c48a6E` | 6 | ✅ |

### 6. Solana 主网
- **网络**: Solana Mainnet Beta
- **RPC**: https://api.mainnet-beta.solana.com
- **浏览器**: https://explorer.solana.com

| 代币 | Mint地址 | 精度 | 状态 |
|------|----------|------|------|
| SOL | `So11111111111111111111111111111111111111112` | 9 | ✅ |
| USDT | `Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB` | 6 | ✅ |
| USDC | `EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v` | 6 | ✅ |

---

## ⚠️ 重要提醒

### 部署前验证清单
1. **合约地址验证**: 在区块链浏览器中确认合约地址的有效性
2. **精度确认**: 验证代币的小数位数设置
3. **余额测试**: 使用小额资金进行转账测试
4. **API连通性**: 确认RPC端点的可用性和稳定性
5. **监控设置**: 配置适当的监控和告警机制

### 需要进一步确认的地址
- **TRON USDC**: `TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8` - 需要确认是否为官方合约
- **BSC USDC**: `0x8AC76a51cc950d9822D68b83fE1Ad97B32Cd580d` - 需要确认是否为最新合约

### 安全建议
1. 在正式部署前，使用测试网进行充分测试
2. 设置合理的交易限额和风控机制
3. 定期检查合约地址的有效性
4. 建立多重签名和审批流程
5. 配置实时监控和异常告警

---

## 📚 参考资源

- [Chainlist.org](https://chainlist.org/) - 区块链网络信息
- [CoinGecko API](https://www.coingecko.com/en/api) - 代币信息查询
- [DefiLlama](https://defillama.com/) - DeFi协议数据
- [Etherscan](https://etherscan.io/) - 以太坊区块链浏览器
- [BSCScan](https://bscscan.com/) - BSC区块链浏览器

---

*最后更新: 2024年1月*
*维护者: 开发团队*
