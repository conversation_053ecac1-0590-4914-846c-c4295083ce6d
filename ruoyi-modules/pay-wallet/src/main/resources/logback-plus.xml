<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <!-- 日志存放路径 -->
    <property name="log.path" value="logs/${project.artifactId}"/>
    <!-- 日志输出格式 -->
    <property name="console.log.pattern"
              value="%cyan(%d{yyyy-MM-dd HH:mm:ss}) %green([%thread]) %highlight(%-5level) %boldMagenta(%logger{36}%n) - %msg%n"/>

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${console.log.pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <include resource="logback-common.xml" />

    <include resource="logback-logstash.xml" />



    <!-- 自己的服务使用 debug 级别 -->
    <logger name="org.dromara.wallet.wallet.utils.TronTransactionSigner" level="INFO" additivity="false">
        <appender-ref ref="console"/>
    </logger>

    <logger name="org.dromara.wallet.wallet.utils.TronAddressUtils" level="INFO" additivity="false">
        <appender-ref ref="console"/>
    </logger>

    <logger name="org.dromara.wallet.wallet.scan" level="INFO" additivity="false">
        <appender-ref ref="console"/>
    </logger>

    <logger name="org.dromara.common.scanning" level="INFO" additivity="false">
        <appender-ref ref="console"/>
    </logger>

    <!-- 自己的服务使用 debug 级别 -->
    <logger name="org.dromara" level="DEBUG" additivity="false">
        <appender-ref ref="console"/>
    </logger>



    <!--系统操作日志-->
    <root level="info">
        <appender-ref ref="console"/>
    </root>
</configuration>
