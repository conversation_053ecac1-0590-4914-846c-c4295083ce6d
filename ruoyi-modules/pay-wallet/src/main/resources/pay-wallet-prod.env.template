# =============================================================================
# 生产环境配置模板 - pay-wallet-prod.yml 环境变量
# =============================================================================
# 使用说明：
# 1. 复制此文件为 .env 或直接在系统环境变量中配置
# 2. 填写所有必需的值，删除示例值
# 3. 确保私钥和敏感信息的安全性
# 4. 在生产环境中使用密钥管理系统（如 HashiCorp Vault、AWS Secrets Manager）
# =============================================================================

# -----------------------------------------------------------------------------
# 钱包加密配置
# -----------------------------------------------------------------------------
# 钱包私钥加密密钥（必须是强密码，建议32位以上）
WALLET_ENCRYPTION_SECRET_KEY=your_super_strong_encryption_key_here_32_chars_min

# -----------------------------------------------------------------------------
# SOLANA 主网配置
# -----------------------------------------------------------------------------
# Solana 主钱包地址
SOLANA_MAIN_ADDRESS_K99999999=your_solana_main_address_k99999999
SOLANA_MAIN_ADDRESS_SYSTEM2=your_solana_main_address_system2
SOLANA_MAIN_ADDRESS_99=your_solana_main_address_99

# Solana RPC 端点配置
SOLANA_RPC_ENDPOINT_PRIMARY=https://api.mainnet-beta.solana.com
SOLANA_RPC_ENDPOINT_BACKUP=https://solana-api.projectserum.com
SOLANA_WS_ENDPOINT=wss://api.mainnet-beta.solana.com

# -----------------------------------------------------------------------------
# TRON 主网配置
# -----------------------------------------------------------------------------
# TRON 主钱包地址
TRON_MAIN_ADDRESS_K99999999=your_tron_main_address_k99999999
TRON_MAIN_ADDRESS_SYSTEM2=your_tron_main_address_system2

# TRON 手续费钱包配置
TRON_FEE_WALLET_PRIVATE_KEY=your_tron_fee_wallet_private_key
TRON_FEE_WALLET_ADDRESS=your_tron_fee_wallet_address

# TRON API 密钥
TRON_API_KEY=your_tron_api_key

# -----------------------------------------------------------------------------
# BSC 主网配置
# -----------------------------------------------------------------------------
# BSC 主钱包地址
BSC_MAIN_ADDRESS_K99999999=your_bsc_main_address_k99999999

# BSC 手续费钱包配置
BSC_FEE_WALLET_PRIVATE_KEY=your_bsc_fee_wallet_private_key
BSC_FEE_WALLET_ADDRESS=your_bsc_fee_wallet_address

# BSC RPC 端点
BSC_RPC_ENDPOINT=https://bsc-dataseed1.binance.org/

# -----------------------------------------------------------------------------
# Arbitrum 主网配置
# -----------------------------------------------------------------------------
# Arbitrum 主钱包地址
ARB_MAIN_ADDRESS_K99999999=your_arbitrum_main_address_k99999999

# Arbitrum 手续费钱包配置
ARB_FEE_WALLET_PRIVATE_KEY=your_arbitrum_fee_wallet_private_key
ARB_FEE_WALLET_ADDRESS=your_arbitrum_fee_wallet_address

# Arbitrum RPC 端点
ARB_RPC_ENDPOINT=https://arb1.arbitrum.io/rpc

# -----------------------------------------------------------------------------
# Base 主网配置
# -----------------------------------------------------------------------------
# Base 主钱包地址
BASE_MAIN_ADDRESS_K99999999=your_base_main_address_k99999999

# Base 手续费钱包配置
BASE_FEE_WALLET_PRIVATE_KEY=your_base_fee_wallet_private_key
BASE_FEE_WALLET_ADDRESS=your_base_fee_wallet_address

# Base RPC 端点
BASE_RPC_ENDPOINT=https://mainnet.base.org

# -----------------------------------------------------------------------------
# Avalanche 主网配置
# -----------------------------------------------------------------------------
# Avalanche 主钱包地址
AVAX_MAIN_ADDRESS_K99999999=your_avalanche_main_address_k99999999

# Avalanche 手续费钱包配置
AVAX_FEE_WALLET_PRIVATE_KEY=your_avalanche_fee_wallet_private_key
AVAX_FEE_WALLET_ADDRESS=your_avalanche_fee_wallet_address

# Avalanche RPC 端点
AVAX_RPC_ENDPOINT=https://api.avax.network/ext/bc/C/rpc

# =============================================================================
# 安全提醒：
# 1. 所有私钥必须妥善保管，不得泄露
# 2. 建议使用硬件钱包或多重签名钱包
# 3. 定期轮换API密钥和加密密钥
# 4. 在生产环境中使用专业的密钥管理服务
# 5. 确保所有地址都经过充分测试
# =============================================================================
