<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.wallet.mapper.TronAddressMapper">

    <resultMap type="org.dromara.wallet.domain.vo.TrcCstaddressVo" id="TrcCstaddressResult">
    </resultMap>
    <select id="findAddressesNeedUpdate" resultType="org.dromara.wallet.domain.TrcCstaddress">
        SELECT a.*
        FROM meta_trc20_cstaddressinfo a
        WHERE NOT EXISTS (SELECT 1
                          FROM meta_wallet_coin_rec b
                          WHERE b.wallet_address = a.cst_address
                            AND b.chain_type = #{chainType}
                            AND b.create_time >= #{startTime})
        ORDER BY a.id
        LIMIT 10
    </select>


    <!-- 可根据实际需求添加特定的SQL查询 -->

</mapper>
