<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.wallet.mapper.MetaSolanacstaddressinfoMapper">

    <select id="findAddressesNeedUpdate" resultType="org.dromara.wallet.domain.MetaSolanaCstaddressinfo">
        SELECT a.*
        FROM meta_solana_cstaddressinfo a
        WHERE NOT EXISTS (SELECT 1
                          FROM meta_wallet_coin_rec b
                          WHERE b.wallet_address = a.cst_address
                            AND b.chain_type = #{chainType}
                            AND b.create_time >= #{startTime})
        ORDER BY a.id
        LIMIT 10
    </select>

    <select id="findAllAddressesForCollect" resultType="org.dromara.wallet.domain.MetaSolanaCstaddressinfo">
        SELECT a.*
        FROM meta_solana_cstaddressinfo a
        ORDER BY a.id
        <if test="maxCount != null">
            LIMIT #{maxCount}
        </if>
    </select>

    <select id="findAddressesByList" resultType="org.dromara.wallet.domain.MetaSolanaCstaddressinfo">
        SELECT a.*
        FROM meta_solana_cstaddressinfo a
        WHERE a.cst_address IN
        <foreach collection="addresses" item="address" open="(" separator="," close=")">
            #{address}
        </foreach>
        ORDER BY a.id
    </select>

</mapper>
