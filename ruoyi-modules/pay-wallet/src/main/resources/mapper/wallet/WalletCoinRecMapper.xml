<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.wallet.mapper.WalletCoinRecMapper">

    <resultMap type="org.dromara.wallet.domain.vo.WalletCoinRecVo" id="WalletCoinRecResult">
    </resultMap>

<!--    &lt;!&ndash; 基础查询字段 &ndash;&gt;-->
<!--    <sql id="selectWalletCoinRecVo">-->
<!--        SELECT-->
<!--            wcr.id,-->
<!--            wcr.wallet_address as walletAddress,-->
<!--            wcr.token_address as tokenAddress,-->
<!--            wcr.token_symbol as tokenSymbol,-->
<!--            wcr.raw_balance as rawBalance,-->
<!--            wcr.decimals,-->
<!--            wcr.chain_type as chainType,-->
<!--            wcr.create_time as createTime,-->
<!--            wcr.update_time as updateTime-->
<!--        FROM meta_wallet_coin_rec wcr-->
<!--    </sql>-->

    <!-- 使用子查询方式的最新记录查询（不使用窗口函数） -->
    <select id="selectLatestRecordsPage" resultType="org.dromara.wallet.domain.vo.WalletCoinRecVo">
        SELECT *
        FROM meta_wallet_coin_rec wcr
                 INNER JOIN (SELECT wallet_address,
                                    token_address,
                                    chain_type,
                                    MAX(id) as max_id
                             FROM meta_wallet_coin_rec ${ew.getCustomSqlSegment}
                             GROUP BY wallet_address, token_address, chain_type) latest
                            ON wcr.wallet_address = latest.wallet_address
                                AND wcr.token_address = latest.token_address
                                AND wcr.chain_type = latest.chain_type
                                AND wcr.id = latest.max_id
        ORDER BY wcr.balance DESC
    </select>

    <!-- 可根据实际需求添加特定的SQL查询 -->

</mapper>
