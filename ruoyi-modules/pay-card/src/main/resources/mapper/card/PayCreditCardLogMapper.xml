<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.card.mapper.PayCreditCardLogMapper">

    <select id="queryCreditCardLogPageList" resultType="org.dromara.card.domain.vo.PayCreditCardLogVo">
        SELECT
            t1.id AS id,
            t1.card_id AS cardId,
            t2.user_id AS userId,
            t1.transaction_id AS transactionId,
            t1.transaction_currency AS transactionCurrency,
            t1.transaction_amount AS transactionAmount,
            t1.card_currency AS cardCurrency,
            t1.card_transaction_amount AS cardTransactionAmount,
            t1.transaction_type AS transactionType,
            t1.transaction_status AS transactionStatus,
            t1.transaction_date AS transactionDate,
            t1.fee AS fee,
            t1.fee_currency AS feeCurrency,
            t1.merchant_name AS merchantName,
            t1.merchant_city AS merchantCity,
            t1.transaction_detail AS transactionDetail,
            t1.card_available_balance AS cardAvailableBalance,
            t1.card_available_balance_currency AS cardAvailableBalanceCurrency,
            t1.description AS description,
            t2.card_no AS cardNo
        FROM meta_credit_card_log t1
             LEFT JOIN meta_credit_card t2 ON t2.card_id = t1.card_id
        ${ew.getCustomSqlSegment}
    </select>

</mapper>
