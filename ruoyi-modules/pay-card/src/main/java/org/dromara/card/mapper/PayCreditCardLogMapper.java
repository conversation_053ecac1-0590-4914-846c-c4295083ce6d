package org.dromara.card.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.dromara.card.domain.PayCreditCardLog;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.card.domain.vo.PayCreditCardLogVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

@Mapper
public interface PayCreditCardLogMapper extends BaseMapperPlus<PayCreditCardLog, PayCreditCardLogVo> {

    Page<PayCreditCardLogVo> queryCreditCardLogPageList(@Param("page") Page<PayCreditCardLog> page,@Param(Constants.WRAPPER) QueryWrapper<PayCreditCardLog> lqw);
}
