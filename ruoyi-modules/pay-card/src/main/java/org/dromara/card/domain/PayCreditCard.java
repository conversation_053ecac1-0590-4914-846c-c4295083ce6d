package org.dromara.card.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import org.dromara.card.domain.vo.PayCreditCardVo;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName("meta_credit_card")
@AutoMappers({
    @AutoMapper(target = PayCreditCardVo.class)
})
public class PayCreditCard {

    /**
     * 卡ID
     */
    @TableId(value = "card_id")
    private String cardId;

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 持卡人姓名
     */
    private String cardHolder;

    /**
     * 信用卡号码
     */
    private String cardNo;

    /**
     * 信用卡标签
     */
    private String cardLabel;

    /**
     * 信用卡安全码
     */
    private String cvv2;

    /**
     * 信用卡有效期
     */
    private String expirationTime;

    /**
     * 信用卡别名
     */
    private String cardAlias;

    /**
     * 信用卡余额
     */
    private BigDecimal balance;

    /**
     * 信用卡额度
     */
    private BigDecimal limitAmount;

    /**
     * 信用卡状态
     */
    private String cardStatus;

    /**
     * 信用卡等级
     */
    private String cardLevel;

    /**
     * 信用卡类型
     */
    private String cardType;

    /**
     * 信用卡费用
     */
    private BigDecimal cardFee;

    /**
     * 地址1
     */
    private String addressLine1;

    /**
     * 地址2
     */
    private String addressLine2;

    /**
     * 城市
     */
    private String city;

    /**
     * 省份
     */
    private String province;

    /**
     * 国家
     */
    private String country;

    /**
     * 邮政编码
     */
    private String postalCode;

    /**
     * 查询时间
     */
    private LocalDateTime searchTime;

    /**
     * 交易时间
     */
    private  LocalDateTime tradeTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 是否加密 0-否 1- 是
     */
    private String encrypt;

    /**
     * 是否激活使用 N-否 Y- 是
     */
    private String isActive;

    /**
     * 卡片来源
     */
    private String source;

    /**
     * 币种
     */
    private String currency;

    /**
     * 月费
     */
    private String monthFee;

    /**
     * 充值费
     */
    private String rechargeFee;

    private String hashHolderInfo;

    /**
     * 卡片配置主键
     */
    private Long cardCfgId;

    /**
     * 旧卡未平移前余额
     */
    private BigDecimal oldBalance;

    /**
     * 卡临时id
     */
    private String tempCardId;

    /**
     * 实体卡pin
     */
    private String pin;

    /**
     * 卡的国家
     */
    private String billingCountry;

    /**
     * 展示的币种
     */
    private String showCurrency;

    /**
     * 展示的金额
     */
    private String showAmt;

    /**
     * 卡状态更新的时间
     */
    private Date statusUpdateTime;

    /**
     * 银行的卡id
     */
    private String bankCardId;
}
