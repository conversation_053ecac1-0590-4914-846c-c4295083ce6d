package org.dromara.card.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class PayCreditCardLogVo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 卡ID
     */
    private String cardId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 交易id
     */
    private String transactionId;

    /**
     * 交易时间
     */
    private String transactionDate;

    /**
     * 交易币种:货币三位代码
     */
    private String transactionCurrency;

    /**
     * 交易金额
     */
    private BigDecimal transactionAmount;

    /**
     * 卡币种:货币三位代码
     */
    private String cardCurrency;

    /**
     * 卡交易金额
     */
    private BigDecimal cardTransactionAmount;

    /**
     * 交易类型
     */
    private String TransactionType;

    /**
     * 交易状态：processing,success,fail
     */
    private String TransactionStatus;

    /**
     * 商户名称
     */
    private String merchantName;
    /**
     * 商户所在城市
     */
    private String merchantCity;

    /**
     * 描述码
     */
    private String respCode;

    /**
     * 描述
     */
    private String respCodeDesc;

    /**
     * 手续费金额
     */
    private BigDecimal fee;

    /**
     * 手续费卡币种
     */
    private String feeCurrency;

    /**
     * 交易详细信息
     */
    private String transactionDetail;

    /**
     * 卡可用余额
     */
    private BigDecimal cardAvailableBalance;

    /**
     * 卡可用余额币种
     */
    private String cardAvailableBalanceCurrency;

    /**
     * 描述
     */
    private String description;

    /**
     * 卡号
     */
    private  String cardNo;

    /**
     * 用户名
     */
    private String userName;

    /**
     * UID
     */
    private String nickName;
}
