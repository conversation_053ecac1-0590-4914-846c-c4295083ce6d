package org.dromara.card.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

@Data
public class PayCreditCardVo {

    private String cardId;

    private String bankCardId;

    /** 用户编号 */
    private BigInteger userId;

    /** 用户编号 */
    private String username;
    /** 用户编号 */
    private String nickName;

    /** 持卡人姓名 */
    private String cardHolder;

    /** 信用卡号码 */
    private String cardNo;

    /** 信用卡标签 */
    private String cardLabel;

    /** 信用卡安全码 */
    private String cvv2;

    /** 信用卡有效期 */
    private String expirationTime;

    /** 信用卡别名 */
    private String cardAlias;

    /** 信用卡余额 */
    private BigDecimal balance;

    /** 信用卡额度 */
    private BigDecimal limitAmount;

    /** 信用卡状态 */
    private String cardStatus;

    /** 信用卡等级 */
    private String cardLevel;

    /** 信用卡类型 */
    private String cardType;

    /** 信用卡费用 */
    private BigDecimal cardFee;

    /** 城市 */
    private String city;

    /** 省份 */
    private String province;

    /** 国家 */
    private String country;

    /** 邮政编码 */
    private String postalCode;

    /** 查询时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date searchTime;

    /** 交易时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private  Date tradeTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createdAt;

    /**
     * 渠道
     */
    private String source;

    /**
     * 卡币种
     */
    private String cardCurrency;

    /**
     * 是否已发放优惠券
     */
    private String isIssuedCoupon;
}
