package org.dromara.card.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.card.service.PayCardService;
import org.dromara.card.service.PayCreditCardLogService;
import org.dromara.card.domain.bo.PayCreditCardBo;
import org.dromara.card.domain.bo.PayCreditCardLogBo;
import org.dromara.card.domain.vo.PayCreditCardLogVo;
import org.dromara.card.domain.vo.PayCreditCardVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.common.core.constant.KazePayCommonConstants;
import org.dromara.common.core.utils.MyAesUtil;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.utils.vcc.AesUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.api.RemoteUserService;
import org.dromara.system.api.domain.bo.RemoteClientBo;
import org.dromara.system.api.domain.vo.RemoteUserVo;
import org.springframework.stereotype.Service;
import org.dromara.card.service.PayCreditCardService;

import java.util.*;

@Service
@RequiredArgsConstructor
public class PayCardServiceImpl implements PayCardService {

    private final PayCreditCardLogService payCreditCardLogService;
    private final PayCreditCardService payCreditCardService;

    @DubboReference
    private final RemoteUserService remoteUserService;


    /**
     * 获取卡交易明细列表
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<PayCreditCardLogVo> queryCreditCardLogPageList(PayCreditCardLogBo bo) {
        Map<Long, RemoteUserVo> userMap = new HashMap<>();
        RemoteClientBo remoteClientBo = new RemoteClientBo();

        // 如果存在条件查询用户名和昵称 -> 找到用户id -> 找到所属信用卡id -> 再去log里查询数据
        if (StringUtils.isNotBlank(bo.getUserName()) || StringUtils.isNotBlank(bo.getNickName())) {


            remoteClientBo.setUserName(bo.getUserName());
            remoteClientBo.setNickName(bo.getNickName());
            List<RemoteUserVo> userList = remoteUserService.selectUserListByParam(remoteClientBo);

            List<Long> userIds = new ArrayList<>(userList.size());

            for (RemoteUserVo user : userList) {
                userMap.put(user.getUserId(), user);
                userIds.add(user.getUserId());
            }

            PayCreditCardBo payCreditCardBo = new PayCreditCardBo();
            payCreditCardBo.setUserIds(userIds);
            if (!userIds.isEmpty()) {
                List<PayCreditCardVo> cardList = payCreditCardService.selectCardListByParam(payCreditCardBo);
                List<String> cardIds = cardList.stream()
                        .map(PayCreditCardVo::getCardId)
                        .toList();
                if (cardIds.isEmpty()) return TableDataInfo.build(new ArrayList<>());
                bo.setCardIds(cardIds);
            } else {
                return TableDataInfo.build(new ArrayList<>());
            }
        }

        Page<PayCreditCardLogVo> cardLogPage = payCreditCardLogService.queryCreditCardLogPageList(bo);
        List<PayCreditCardLogVo> cardLogVoList = cardLogPage.getRecords();

        // 如果没有条件查询用户名和昵称 -> 需组装用户数据
        if (!cardLogVoList.isEmpty() && (StringUtils.isBlank(bo.getUserName()) || StringUtils.isBlank(bo.getNickName()))){
            List<Long> userIds = cardLogVoList.stream()
                .map(PayCreditCardLogVo::getUserId)
                .toList();
            remoteClientBo.setIds(userIds);
            List<RemoteUserVo> userList = remoteUserService.selectUserListByParam(remoteClientBo);
            for (RemoteUserVo user : userList) {
                userMap.put(user.getUserId(), user);
            }
        }

        // 组装用户信息和卡号
        for (PayCreditCardLogVo cardLogVo : cardLogVoList) {
            RemoteUserVo user = userMap.get(cardLogVo.getUserId());
            if (user != null){
                cardLogVo.setUserName(user.getUserName());
                cardLogVo.setNickName(user.getNickName());
            }

            if (StringUtils.isNotEmpty(cardLogVo.getCardNo())) {
                String cardNo = "";
                if (!cardLogVo.getCardNo().contains("*")) {
                    cardNo = AesUtils.aesDecrypt(KazePayCommonConstants.CARD_KEY, cardLogVo.getCardNo());
                    String xx = "";

                    if (cardNo.length() > 8) {
                        for (int i = 0; i < cardNo.length() - 8; i++) {
                            xx = xx + "*";
                        }
                        cardNo = cardNo.substring(0, 4) + xx + cardNo.substring(cardNo.length() - 4, cardNo.length());
                    }
                } else {
                    cardNo = cardLogVo.getCardNo();
                }

                cardNo = MyAesUtil.encode((MyAesUtil.SALT + cardNo).getBytes());
                cardLogVo.setCardNo(cardNo);
            }
        }

        return TableDataInfo.build(cardLogPage);
    }
}
