package org.dromara.card.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("meta_credit_card_log")
@AutoMappers({
    @AutoMapper(target = PayCreditCard.class)
})
public class PayCreditCardLog {

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 卡号
     */
    private String cardId;

    /**
     * 交易id
     */
    private String transactionId;

    /**
     * 最新的交易id
     */
    private String newTransactionId;

    /**
     * 交易币种:货币三位代码
     */
    private String transactionCurrency;

    /**
     * 交易金额
     */
    private BigDecimal transactionAmount;

    /**
     * 卡币种:货币三位代码
     */
    private String cardCurrency;

    /**
     * 卡交易金额
     */
    private BigDecimal cardTransactionAmount;

    /**
     * 交易类型
     */
    private String transactionType;

    /**
     * 交易状态：processing,success,fail
     */
    private String transactionStatus;

    /**
     * 交易时间
     */
    private String transactionDate;

    /**
     * 手续费金额
     */
    private BigDecimal fee;

    /**
     * 手续费卡币种
     */
    private String feeCurrency;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户所在城市
     */
    private String merchantCity;

    /**
     * 交易详细信息
     */
    private String transactionDetail;

    /**
     * 描述
     */
    private String description;

    /**
     * 卡可用余额
     */
    private BigDecimal cardAvailableBalance;

    /**
     * 卡可用余额币种
     */
    private String cardAvailableBalanceCurrency;

    /**
     * 唯一编号
     */
    private String  requestNo;

    /**
     * 描述码
     */
    private String respCode;
}
