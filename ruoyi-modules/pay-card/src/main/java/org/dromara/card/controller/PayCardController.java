package org.dromara.card.controller;

import org.dromara.card.domain.bo.PayCreditCardLogBo;
import org.dromara.card.domain.vo.PayCreditCardLogVo;
import lombok.RequiredArgsConstructor;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.dromara.card.service.PayCardService;

/**
 * 卡片管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/payCard")
public class PayCardController {

    private final PayCardService payCardService;

    /**
     * 获取卡交易明细列表
     */
//    @ApiEncrypt
    @PostMapping("/listForCardLog")
//    @SaCheckPermission("txn:cardlog:list")
    public TableDataInfo<PayCreditCardLogVo> listForCoin(@RequestBody PayCreditCardLogBo bo) {
        return payCardService.queryCreditCardLogPageList(bo);
    }

}
