package org.dromara.card.domain.bo;

import lombok.Data;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.List;

@Data
public class PayCreditCardLogBo extends PageQuery {

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 用户名
     */
    private String userName;

    /**
     * UID
     */
    private String nickName;

    /**
     * 交易状态
     */
    private String txnStatus;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 交易类型
     */
    private String transactionType;

    /**
     * 卡ID列表
     */
    private List<String> cardIds;
}
