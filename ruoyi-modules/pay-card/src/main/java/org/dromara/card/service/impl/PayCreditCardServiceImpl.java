package org.dromara.card.service.impl;

import org.dromara.card.mapper.PayCreditCardMapper;
import org.dromara.card.service.PayCreditCardService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.dromara.card.domain.PayCreditCard;
import org.dromara.card.domain.bo.PayCreditCardBo;
import org.dromara.card.domain.vo.PayCreditCardVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class PayCreditCardServiceImpl implements PayCreditCardService {

    private final PayCreditCardMapper payCreditCardMapper;

    /**
     * 根据参数查询信用卡列表
     * @param bo
     * @return
     */
    @Override
    public List<PayCreditCardVo> selectCardListByParam(PayCreditCardBo bo) {
        LambdaQueryWrapper<PayCreditCard> lqw = new LambdaQueryWrapper<>();
        lqw.in(bo.getUserIds() != null && !bo.getUserIds().isEmpty(), PayCreditCard::getUserId, bo.getUserIds());
        return payCreditCardMapper.selectVoList(lqw);
    }
}
