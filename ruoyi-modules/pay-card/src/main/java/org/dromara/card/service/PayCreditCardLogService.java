package org.dromara.card.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.card.domain.bo.PayCreditCardLogBo;
import org.dromara.card.domain.vo.PayCreditCardLogVo;

public interface PayCreditCardLogService {

    /**
     * 根据条件分页查询信用卡日志
     * @param bo
     * @return
     */
    Page<PayCreditCardLogVo> queryCreditCardLogPageList(PayCreditCardLogBo bo);
}
