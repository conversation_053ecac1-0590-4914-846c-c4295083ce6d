package org.dromara.card.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.dromara.card.service.PayCreditCardLogService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.card.domain.PayCreditCardLog;
import org.dromara.card.domain.bo.PayCreditCardLogBo;
import org.dromara.card.domain.vo.PayCreditCardLogVo;
import lombok.RequiredArgsConstructor;
import org.dromara.card.mapper.PayCreditCardLogMapper;
import org.dromara.common.core.constant.KazePayCommonConstants;
import org.dromara.common.core.utils.ObjectUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.utils.vcc.AesUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PayCreditCardLogServiceImpl implements PayCreditCardLogService {

    private final PayCreditCardLogMapper payCreditCardLogMapper;


    /**
     * 根据条件分页查询信用卡日志
     * @param bo
     * @return
     */
    @Override
    public Page<PayCreditCardLogVo> queryCreditCardLogPageList(PayCreditCardLogBo bo) {
        QueryWrapper<PayCreditCardLog> lqw = Wrappers.query();
        if (StringUtils.isNotBlank(bo.getCardNo())){
            lqw.eq("t2.card_no", AesUtils.aesEncrypt(KazePayCommonConstants.CARD_KEY, bo.getCardNo()));
        }
        lqw.in(bo.getCardIds() != null && !bo.getCardIds().isEmpty(), "t1.card_id", bo.getCardIds())
            .eq(StringUtils.isNotBlank(bo.getTransactionType()), "t1.transaction_type", bo.getTransactionType())
            .ge(StringUtils.isNotBlank(bo.getStartDate()), "t1.transaction_date", bo.getStartDate())
            .le(StringUtils.isNotBlank(bo.getEndDate()), "t1.transaction_date", bo.getEndDate())
            .orderByDesc("t1.transaction_date");
        PageQuery pageQuery = new PageQuery(bo.getPageNum(), bo.getPageSize());
        return payCreditCardLogMapper.queryCreditCardLogPageList(pageQuery.build(), lqw);
    }
}
