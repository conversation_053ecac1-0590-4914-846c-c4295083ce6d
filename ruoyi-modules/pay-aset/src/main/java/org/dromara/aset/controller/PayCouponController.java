package org.dromara.aset.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.aset.domain.bo.PayCouponGenCfgBo;
import org.dromara.aset.domain.bo.PayCouponInfoBo;
import org.dromara.aset.domain.vo.PayCouponGenCfgVo;
import org.dromara.aset.domain.vo.PayCouponInfoVo;
import org.dromara.aset.service.PayCouponService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.DeleteGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.core.validate.coupon.GrantCoupon;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * 优惠券管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/payCoupon")
public class PayCouponController extends BaseController {

    private final PayCouponService payCouponService;


    /**
     * 获取优惠券配置列表
     */
//    @ApiEncrypt
    @PostMapping("/couponConfigPage")
//    @SaCheckPermission("coupon:config:list")
    public TableDataInfo<PayCouponGenCfgVo> couponConfigPage(@RequestBody PayCouponGenCfgBo bo) {
        return payCouponService.queryGenCfgPageList(bo);
    }

    /**
     * 获取优惠券信息列表
     */
//    @ApiEncrypt
    @PostMapping("/couponInfoPage")
//    @SaCheckPermission("coupon:info:list")
    public TableDataInfo<PayCouponInfoVo> couponInfoPage(@RequestBody PayCouponInfoBo bo) {
        return payCouponService.queryCouponInfoPageList(bo);
    }

    /**
     * 新增优惠券配置
     */
//    @ApiEncrypt
    @PostMapping("/insertCouponConfig")
//    @SaCheckPermission("coupon:config:insert")
    @Transactional(rollbackFor = Exception.class)
    public R<Void> insertCouponConfig(@RequestBody @Validated(value = AddGroup.class) PayCouponGenCfgBo bo) {
        return toAjax(payCouponService.insertCouponConfig(bo));
    }

    /**
     * 修改优惠券配置
     */
//    @ApiEncrypt
    @PostMapping("/updateCouponConfig")
//    @SaCheckPermission("coupon:config:update")
    @Transactional(rollbackFor = Exception.class)
    public R<Void> updateCouponConfig(@RequestBody @Validated(value = EditGroup.class) PayCouponGenCfgBo bo) {
        return toAjax(payCouponService.updateCouponConfig(bo));
    }

    /**
     * 删除优惠券配置
     */
    //    @ApiEncrypt
    @PostMapping("/deleteCouponConfig")
    //    @SaCheckPermission("coupon:config:delete")
    @Transactional(rollbackFor = Exception.class)
    public R<Void> deleteCouponConfig(@RequestBody @Validated(value = DeleteGroup.class) PayCouponGenCfgBo bo) {
        return toAjax(payCouponService.deleteCouponConfig(bo.getId()));
    }

    /**
     * 修改优惠券信息
     */
    //    @ApiEncrypt
    @PostMapping("/updateCouponInfo")
    //    @SaCheckPermission("coupon:info:update")
    @Transactional(rollbackFor = Exception.class)
    public R<Void> updateCouponInfo(@RequestBody @Validated(value = EditGroup.class) PayCouponInfoBo bo) {
        return toAjax(payCouponService.updateCouponInfo(bo));
    }

    /**
     * 发放优惠券
     */
    //    @ApiEncrypt
    @PostMapping("/grantCoupon")
    //    @SaCheckPermission("coupon:info:grant")
    @Transactional(rollbackFor = Exception.class)
    public R<Void> grantCoupon(@RequestBody @Validated(value = GrantCoupon.class) PayCouponInfoBo bo) {
        return toAjax(payCouponService.grantCoupon(bo));
    }

    /**
     * 核销
     */
    //    @ApiEncrypt
    @PostMapping("/deleteCouponInfo")
    //    @SaCheckPermission("coupon:info:delete")
    @Transactional(rollbackFor = Exception.class)
    public R<Void> deleteCouponInfo(@RequestBody @Validated(value = DeleteGroup.class) PayCouponInfoBo bo) {
        return toAjax(payCouponService.deleteCouponInfo(bo));
    }

}
