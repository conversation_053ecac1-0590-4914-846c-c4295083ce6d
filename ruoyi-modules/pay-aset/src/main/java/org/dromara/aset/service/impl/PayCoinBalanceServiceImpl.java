package org.dromara.aset.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.aset.domain.PayCoinBalance;
import org.dromara.aset.domain.bo.PayCoinBalanceBo;
import org.dromara.aset.domain.vo.PayCoinBalanceVo;
import org.dromara.aset.mapper.PayCoinBalanceMapper;
import org.dromara.aset.service.IPayCoinBalanceService;
import org.dromara.common.core.constant.CacheConstants;
import org.dromara.common.core.utils.MessageUtils;
import org.dromara.common.core.validate.coin.FundsOperation;
import org.dromara.common.redis.utils.RedisUtils;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2025/6/19
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PayCoinBalanceServiceImpl implements IPayCoinBalanceService {

    private final PayCoinBalanceMapper payCoinBalanceMapper;

    @Override
    public boolean processRefundFromFrozenFunds(PayCoinBalanceBo bo) {
        return payCoinBalanceMapper.processRefundFromFrozenFunds(bo) > 0;
    }

    /**
     * 扣减余额
     *
     * @param bo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deductBalance(@Validated(value = FundsOperation.class) PayCoinBalanceBo bo) {
        RLock lock = RedisUtils.getClient().getLock(CacheConstants.COIN_BALANCE_LOCK_KEY + bo.getUserId() + ":" + bo.getCurrency());

        boolean locked = false;
        try {
            locked = lock.tryLock(5, 5, TimeUnit.SECONDS);
            if (!locked) {
                throw new RuntimeException(MessageUtils.message("common.system.error"));
            }

            LambdaUpdateWrapper<PayCoinBalance> luw = new LambdaUpdateWrapper<>();
            luw.setSql("coin_balance = coin_balance - " + bo.getAmount());
            luw.eq(PayCoinBalance::getUserId, bo.getUserId());
            luw.eq(PayCoinBalance::getCoinCode, bo.getCurrency());
            luw.ge(PayCoinBalance::getCoinBalance, bo.getAmount());
            return payCoinBalanceMapper.update(luw) > 0;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(MessageUtils.message("common.system.error"));
        } finally {
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 增加余额
     *
     * @param bo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean IncreaseBalance(@Validated(value = FundsOperation.class) PayCoinBalanceBo bo) {
        RLock lock = RedisUtils.getClient().getLock(CacheConstants.COIN_BALANCE_LOCK_KEY + bo.getUserId() + ":" + bo.getCurrency());

        boolean locked = false;
        try {
            locked = lock.tryLock(5, 5, TimeUnit.SECONDS);
            if (!locked) {
                throw new RuntimeException(MessageUtils.message("common.system.error"));
            }

            LambdaUpdateWrapper<PayCoinBalance> luw = new LambdaUpdateWrapper<>();
            luw.setSql("coin_balance = coin_balance + " + bo.getAmount());
            luw.eq(PayCoinBalance::getUserId, bo.getUserId());
            luw.eq(PayCoinBalance::getCoinCode, bo.getCurrency());
            return payCoinBalanceMapper.update(luw) > 0;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(MessageUtils.message("common.system.error"));
        } finally {
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 根据条件查询钱包列表
     *
     * @param bo
     * @return
     */
    @Override
    public List<PayCoinBalanceVo> selectWalletListByParam(PayCoinBalanceBo bo) {
        return payCoinBalanceMapper.selectVoList(new LambdaQueryWrapper<PayCoinBalance>()
            .in(bo.getUserIds() != null && !bo.getUserIds().isEmpty(), PayCoinBalance::getUserId, bo.getUserIds()));
    }

    /**
     * 根据用户ID和币种查询钱包
     *
     * @param bo
     * @return
     */
    @Override
    public PayCoinBalanceVo selectOneByUserIdAndCurrency(PayCoinBalanceBo bo) {
        return payCoinBalanceMapper.selectVoOne(new LambdaQueryWrapper<PayCoinBalance>()
            .eq(PayCoinBalance::getUserId, bo.getUserId())
            .eq(PayCoinBalance::getCoinCode, bo.getCurrency()));
    }
}
