package org.dromara.aset.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.dromara.aset.domain.PayCoinTxnDtl;
import org.dromara.aset.domain.bo.PayCoinTxnDtlBo;
import org.dromara.aset.domain.vo.PayCoinTxnDtlVo;
import org.dromara.aset.mapper.PayCoinTxnDtlMapper;
import org.dromara.aset.service.IPayCoinTxnDtlService;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.wallet.api.RemoteWalletService;
import org.dromara.wallet.api.model.RemoteBatchTransferStatusRequest;
import org.dromara.wallet.api.model.RemoteBatchTransferStatusResponse;
import org.dromara.wallet.api.model.RemoteTransferStatusDto;
import org.dromara.wallet.api.model.RemoteWalletDto;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/6/25
 */
@Service
@RequiredArgsConstructor
@DubboService
@Slf4j
public class PayCoinTxnDtlServiceImpl implements IPayCoinTxnDtlService {

    private final PayCoinTxnDtlMapper baseMapper;

    @DubboReference
    private final RemoteWalletService remoteWalletService;

    @Override
    public boolean processRefundFromFrozenFunds(PayCoinTxnDtlBo bo) {
        return baseMapper.processRefundFromFrozenFunds(bo) > 0;
    }

    /**
     * 查询转账记录
     *
     * @param id 主键
     * @return 转账记录
     */
    @Override
    public PayCoinTxnDtlVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 汇总提款记录金额
     */
    @Override
    public BigDecimal sumWithdrawalAmount() {
        LambdaQueryWrapper<PayCoinTxnDtl> lqw = new LambdaQueryWrapper<>();

        // 查询提币记录 (c1010)
        lqw.eq(PayCoinTxnDtl::getTxnCode, "c1010")
           // 待审核状态 (0-待审核)
           .eq(PayCoinTxnDtl::getTxnStatus, 2);

        // 使用 MyBatis Plus 的聚合查询
        return baseMapper.selectObjs(lqw.select(PayCoinTxnDtl::getTxnAmount))
                        .stream()
                        .filter(Objects::nonNull)
                        .map(obj -> (BigDecimal) obj)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 查询提款记录（需要查询pay-api-wallet）
     * 集成批量查询转账状态功能
     */
    @Override
    public TableDataInfo<PayCoinTxnDtlVo> queryWithdrawals(PayCoinTxnDtlBo bo, PageQuery pageQuery) {
        try {
            // 1. 构建查询条件 - 只查询提币记录
            LambdaQueryWrapper<PayCoinTxnDtl> lqw = buildWithdrawalQueryWrapper(bo);

            // 2. 分页查询提币记录
            IPage<PayCoinTxnDtlVo> page = baseMapper.selectVoPage(pageQuery.build(), lqw);
            List<PayCoinTxnDtlVo> records = page.getRecords();

            if (records.isEmpty()) {
                return TableDataInfo.build(page);
            }

            // 3. 批量查询转账状态并填充扩展属性
            enrichWithTransferStatus(records);

            return TableDataInfo.build(page);

        } catch (Exception e) {
            throw new RuntimeException("查询提款记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查询提款记录（需要联表）
     */
    @Override
    public TableDataInfo<PayCoinTxnDtlVo> queryWithdrawalsOld(PayCoinTxnDtlBo bo, PageQuery pageQuery) {

        QueryWrapper<PayCoinTxnDtl> wrapper = Wrappers.query();
        wrapper.eq("c.txn_code", bo.getTxnCode())
            .eq("c.txn_status", bo.getTxnStatus())
            .and(w -> {
                if (bo.getRecStatus() == null || bo.getRecStatus() == -1) {
                    w.isNull("w.id");
                } else {
                    w.eq("w.status", bo.getRecStatus());
                }
            })
            .orderByAsc("c.create_time");

        Page<PayCoinTxnDtlVo> payCoinTxnDtlVoPage = baseMapper.selectWithdrawalPage(pageQuery.build(), wrapper);
        return TableDataInfo.build(payCoinTxnDtlVoPage);

    }

    /**
     * 根据条件分页查询币种交易记录
     *
     * @param bo        查询条件业务对象
     * @param pageQuery 分页查询参数
     * @return 分页查询结果
     */
    @Override
    public IPage<PayCoinTxnDtlVo> queryPageByParams(PayCoinTxnDtlBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PayCoinTxnDtl> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoPage(pageQuery.build(), lqw);
    }

    /**
     * 根据条件查询币种交易记录
     *
     * @param bo 查询条件业务对象
     * @return 币种交易记录列表
     */
    @Override
    public List<PayCoinTxnDtlVo> queryListByParams(PayCoinTxnDtlBo bo) {
        LambdaQueryWrapper<PayCoinTxnDtl> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 保存币种交易记录
     *
     * @param payCoinTxnDtl 币种交易记录
     * @return 保存结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCoinTxnDtl(PayCoinTxnDtl payCoinTxnDtl) {
        return baseMapper.insert(payCoinTxnDtl) > 0;
    }

    /**
     * 构建查询条件Wrapper
     *
     * @param bo 查询条件业务对象
     * @return LambdaQueryWrapper查询条件
     */
    private LambdaQueryWrapper<PayCoinTxnDtl> buildQueryWrapper(PayCoinTxnDtlBo bo) {
        LambdaQueryWrapper<PayCoinTxnDtl> lqw = new LambdaQueryWrapper<>();

        // 主键查询
        lqw.eq(bo.getTxnId() != null, PayCoinTxnDtl::getTxnId, bo.getTxnId());

        // 用户相关查询
        lqw.eq(bo.getUserId() != null, PayCoinTxnDtl::getUserId, bo.getUserId());
        lqw.in(bo.getUserIds() != null && !bo.getUserIds().isEmpty(), PayCoinTxnDtl::getUserId, bo.getUserIds());

        // 交易类型查询
        lqw.eq(StringUtils.isNotBlank(bo.getTxnCode()), PayCoinTxnDtl::getTxnCode, bo.getTxnCode());
        lqw.in(bo.getTxnTypeList() != null && !bo.getTxnTypeList().isEmpty(), PayCoinTxnDtl::getTxnCode, bo.getTxnTypeList());

        // 交易状态查询
        lqw.eq(bo.getTxnStatus() != null, PayCoinTxnDtl::getTxnStatus, bo.getTxnStatus());

        // 交易ID列表查询
        lqw.in(bo.getTxnIds() != null && !bo.getTxnIds().isEmpty(), PayCoinTxnDtl::getTxnId, bo.getTxnIds());

        // 时间范围查询
        lqw.ge(StringUtils.isNotBlank(bo.getStartDate()), PayCoinTxnDtl::getTxnTime, bo.getStartDate());
        lqw.le(StringUtils.isNotBlank(bo.getEndDate()), PayCoinTxnDtl::getTxnTime, bo.getEndDate());

        // 排序：按交易时间倒序
        lqw.orderByDesc(PayCoinTxnDtl::getTxnTime);

        return lqw;
    }

    /**
     * 构建提款记录查询条件
     */
    private LambdaQueryWrapper<PayCoinTxnDtl> buildWithdrawalQueryWrapper(PayCoinTxnDtlBo bo) {
        LambdaQueryWrapper<PayCoinTxnDtl> lqw = new LambdaQueryWrapper<>();

        // 基础查询条件
        if (bo.getUserId() != null) {
            lqw.eq(PayCoinTxnDtl::getUserId, bo.getUserId());
        }
        if (bo.getUserIds() != null && !bo.getUserIds().isEmpty()) {
            lqw.in(PayCoinTxnDtl::getUserId, bo.getUserIds());
        }

        // 交易类型：默认查询提币记录
        String txnCode = (bo.getTxnCode() != null && !bo.getTxnCode().isEmpty()) ? bo.getTxnCode() : "c1010";
        lqw.eq(PayCoinTxnDtl::getTxnCode, txnCode);

        // 交易状态
        if (bo.getTxnStatus() != null) {
            lqw.eq(PayCoinTxnDtl::getTxnStatus, bo.getTxnStatus());
        }

        // 时间范围查询
        if (bo.getStartDate() != null && !bo.getStartDate().isEmpty()) {
            lqw.ge(PayCoinTxnDtl::getTxnTime, bo.getStartDate());
        }
        if (bo.getEndDate() != null && !bo.getEndDate().isEmpty()) {
            lqw.le(PayCoinTxnDtl::getTxnTime, bo.getEndDate());
        }

        // 只查询有链上交易哈希的记录（即已发起转账的记录）
        lqw.isNotNull(PayCoinTxnDtl::getReceiptNo);
        lqw.ne(PayCoinTxnDtl::getReceiptNo, "");

        // 排序：按交易时间倒序
        lqw.orderByDesc(PayCoinTxnDtl::getTxnTime);

        return lqw;
    }

    /**
     * 批量查询转账状态并填充到PayCoinTxnDtlVo中
     */
    private void enrichWithTransferStatus(List<PayCoinTxnDtlVo> records) {
        if (records == null || records.isEmpty()) {
            return;
        }

        try {
            // 1. 提取所有的交易哈希作为requestId（这里假设receiptNo就是requestId）
            @NotNull List<String> requestIds = records.stream()
                .map(PayCoinTxnDtlVo::getTxnId)
                .filter(Objects::nonNull)
                .map(String::valueOf)
                .distinct()
                .collect(Collectors.toList());

            if (requestIds.isEmpty()) {
                return;
            }

            // 2. 构建批量查询请求
            RemoteBatchTransferStatusRequest request = RemoteBatchTransferStatusRequest.builder()
                .requestIds(requestIds)
                .includeDetails(true)
                .onlyFailures(false)
                .onlyProcessing(false)
                .build();

            // 3. 调用Dubbo接口批量查询
            RemoteBatchTransferStatusResponse response = remoteWalletService.batchQueryTransferStatus(request);

            if (response == null || !Boolean.TRUE.equals(response.getSuccess())) {
                return;
            }

            // 4. 将转账状态信息填充到PayCoinTxnDtlVo中
            Map<String, RemoteTransferStatusDto> statusMap = response.getResults();
            if (statusMap != null && !statusMap.isEmpty()) {
                fillTransferStatusToRecords(records, statusMap);
            }

        } catch (Exception e) {
            // 不抛出异常，避免影响主查询流程
        }
    }

    /**
     * 将转账状态信息填充到记录中
     */
    private void fillTransferStatusToRecords(List<PayCoinTxnDtlVo> records, Map<String, RemoteTransferStatusDto> statusMap) {
        for (PayCoinTxnDtlVo record : records) {
            String txnId = String.valueOf(record.getTxnId());

            RemoteTransferStatusDto status = statusMap.get(txnId);
            if (status == null) {
                // 未找到转账状态，设置默认的空状态
                RemoteTransferStatusDto notFoundStatus = RemoteTransferStatusDto.builder()
                    .requestId(txnId)
                    .status(-1)
                    .statusDescription("未找到转账记录")
                    .errorMessage("未找到指定交易哈希的转账记录")
                    .totalRecords(0)
                    .confirmedRecords(0)
                    .failedRecords(0)
                    .build();
                record.setTransferStatus(notFoundStatus);
            } else {
                // 直接设置完整的转账状态对象
                record.setTransferStatus(status);
            }
        }
    }

    /**
     * 安全提款执行方法
     * 根据txnId查询数据库获取完整提款信息，验证后调用钱包服务执行提款
     */
    @Override
    public String executeWithdrawal(Long txnId) {
        log.info("开始执行安全提款，txnId: {}", txnId);

        // 1. 参数验证
        if (txnId == null) {
            throw new IllegalArgumentException("交易ID不能为空");
        }

        // 2. 查询提款记录
        PayCoinTxnDtlVo txnDetail = queryById(txnId);
        if (txnDetail == null) {
            throw new IllegalArgumentException("提款记录不存在，txnId: " + txnId);
        }

        // 3. 业务验证
        validateWithdrawalRequest(txnDetail);

        // 4. 构建钱包提款请求
        RemoteWalletDto walletDto = buildWalletDto(txnDetail);

        // 5. 调用钱包服务执行提款
        try {
            String result = remoteWalletService.withdraw(walletDto);
            log.info("提款执行完成，txnId: {}, result: {}", txnId, result);
            return result != null ? result : "提款请求已提交";
        } catch (Exception e) {
            log.error("调用钱包服务执行提款失败，txnId: {}, error: {}", txnId, e.getMessage());
            throw new RuntimeException("提款执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证提款请求的业务规则
     */
    private void validateWithdrawalRequest(PayCoinTxnDtlVo txnDetail) {
        // 验证交易类型
        if (!"c1010".equals(txnDetail.getTxnCode())) {
            throw new IllegalArgumentException("非提币交易，无法执行提款操作");
        }

        // 验证交易状态 - 只允许待审核状态的记录执行提款
        if (!"2".equals(txnDetail.getTxnStatus())) {
            throw new IllegalArgumentException("交易状态不正确，只允许待审核状态的记录执行提款");
        }

        // 验证必要字段
        if (StringUtils.isBlank(txnDetail.getToAddress())) {
            throw new IllegalArgumentException("收款地址不能为空");
        }

        if (txnDetail.getTxnAmount() == null || txnDetail.getTxnAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("提款金额必须大于0");
        }

        if (StringUtils.isBlank(txnDetail.getTxnCoin())) {
            throw new IllegalArgumentException("代币类型不能为空");
        }

        if (StringUtils.isBlank(txnDetail.getCoinNet())) {
            throw new IllegalArgumentException("币种网络不能为空");
        }

        log.info("提款请求验证通过，txnId: {}", txnDetail.getTxnId());
    }

    /**
     * 构建钱包服务请求DTO
     */
    private RemoteWalletDto buildWalletDto(PayCoinTxnDtlVo txnDetail) {
        RemoteWalletDto walletDto = new RemoteWalletDto();

        // 设置基础信息
        walletDto.setToAddress(txnDetail.getToAddress());
        walletDto.setAmount(txnDetail.getTxnAmount());
        walletDto.setTokenSymbol(txnDetail.getTxnCoin());

        // 映射网络类型 - coinNet到chainName的转换
        String chainName = mapCoinNetToChainName(txnDetail.getCoinNet());
        walletDto.setChainName(chainName);

        // 设置请求ID为txnId
        walletDto.setRequestId(txnDetail.getTxnId().toString());

        log.info("构建钱包请求DTO完成，txnId: {}, toAddress: {}, amount: {}, tokenSymbol: {}, chainName: {}",
            txnDetail.getTxnId(), txnDetail.getToAddress(), txnDetail.getTxnAmount(),
            txnDetail.getTxnCoin(), chainName);

        return walletDto;
    }

    /**
     * 映射coinNet到chainName
     * 根据数据库中的coinNet字段映射到钱包服务需要的chainName
     */
    private String mapCoinNetToChainName(String coinNet) {
        if (StringUtils.isBlank(coinNet)) {
            throw new IllegalArgumentException("币种网络不能为空");
        }

        // 标准化处理
        String normalizedCoinNet = coinNet.toUpperCase().trim();

        // 映射规则
        return switch (normalizedCoinNet) {
            case "TRON", "TRX" -> "tron";
            case "BSC", "BNB", "BINANCE" -> "bsc";
            case "ARB", "ARBITRUM" -> "arb";
            case "BASE" -> "base";
            case "AVAX", "AVALANCHE" -> "avax";
            case "SOLANA", "SOL" -> "solana";
            default -> {
                log.warn("未知的币种网络: {}, 使用原值", coinNet);
                yield coinNet.toLowerCase();
            }
        };
    }

}
