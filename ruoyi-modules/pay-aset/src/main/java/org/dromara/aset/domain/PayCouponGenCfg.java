package org.dromara.aset.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import org.dromara.aset.api.domain.vo.RemoteCoinVo;
import org.dromara.aset.domain.bo.PayCouponGenCfgBo;
import org.dromara.aset.domain.vo.PayCouponGenCfgVo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/6/25
 */
@Data
@TableName("k_meta_coupon_gen_cfg")
@AutoMappers({
    @AutoMapper(target = PayCouponGenCfgBo.class),
    @AutoMapper(target = PayCouponInfo.class)
})
public class PayCouponGenCfg {

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 优惠券管理用户ID
     */
    private Long admUserId;

    /**
     * D-折扣券 M-满减券
     */
    private String couponType;

    /**
     * NEW-开卡、TOPUP卡充值、NODE-节点购买
     */
    private String couponScenarios;

    /**
     * 可使用卡类型
     */
    private String couponCardType;

    /**
     * 可使用卡等级产品
     */
    private String couponCardlevel;

    /**
     * 可使用节点产品
     */
    private String couponNode;

    /**
     * 折扣货币代码
     */
    private String discountCoin;

    /**
     * 折扣比例
     */
    private BigDecimal discountRate;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmt;

    /**
     * 最高抵扣金额
     */
    private BigDecimal maxDiscountAmt;

    /**
     * 使用最低消费金额
     */
    private BigDecimal minSpendAmt;

    /**
     * 生效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date eftDate;

    /**
     * 失效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expDate;

    /**
     * 总共发放数量
     */
    private Integer couponTot;

    /**
     * 已使用数量
     */
    private Integer couponUsed;

    /**
     * 剩余可用数量
     */
    private Integer couponAva;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 优惠券使用次数限制(0-无限使用)
     */
    private Integer couponUsageLimit;

    /**
     * 场景合集
     */
    @TableField(exist = false)
    private String[] couponScenariosList;

    /**
     * 可类型集合
     */
    @TableField(exist = false)
    private String[] couponCardTypeList;

    /**
     * 卡级别
     */
    @TableField(exist = false)
    private String[] couponCardlevelList;

    /**
     * 节点集合
     */
    @TableField(exist = false)
    private String[] couponNodeList;

    /**
     * 优惠券管理用户名
     */
    @TableField(exist = false)
    private String admUserName;

    /**
     * 优惠券码
     */
    @TableField(exist = false)
    private String couponNm;
}
