package org.dromara.aset.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.aset.domain.bo.PayTxnDtlCodeBo;
import org.dromara.aset.domain.vo.PayCoinTxnDtlVo;
import org.dromara.aset.domain.vo.PayTxnDtlCodeVo;

public interface PayTxnDtlCodeService {

    /**
     * 根据条件查询分页数据
     * @param bo
     * @return
     */
    Page<PayTxnDtlCodeVo> queryPageByParams(PayTxnDtlCodeBo bo);
}
