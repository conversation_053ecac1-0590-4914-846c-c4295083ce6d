package org.dromara.aset.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.aset.domain.PayTxnDtlUsd;
import org.dromara.aset.domain.bo.PayTxnDtlUsdBo;
import org.dromara.aset.domain.vo.PayTxnDtlUsdVo;
import org.dromara.aset.mapper.PayTxnDtlUsdMapper;
import org.dromara.aset.service.PayTxnDtlUsdService;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class PayTxnDtlUsdServiceImpl implements PayTxnDtlUsdService {

    private final PayTxnDtlUsdMapper payTxnDtlUsdMapper;

    /**
     * 根据条件查询USD交易记录
     * @param bo
     * @return
     */
    @Override
    public List<PayTxnDtlUsdVo> queryListByParams(PayTxnDtlUsdBo bo) {
        LambdaQueryWrapper<PayTxnDtlUsd> lqw = new LambdaQueryWrapper<>();
        lqw.eq(bo.getUserId() != null, PayTxnDtlUsd::getUserId, bo.getUserId())
            .eq(StringUtils.isNotBlank(bo.getTxnType()), PayTxnDtlUsd::getTxnType, bo.getTxnType())
            .in(bo.getTxnTypeList() != null && !bo.getTxnTypeList().isEmpty(), PayTxnDtlUsd::getTxnType, bo.getTxnTypeList())
            .in(bo.getUserIds() != null && !bo.getTxnTypeList().isEmpty(), PayTxnDtlUsd::getUserId, bo.getUserIds())
            .in(bo.getTxnIds() != null && !bo.getTxnIds().isEmpty(), PayTxnDtlUsd::getId, bo.getTxnIds())
            .ge(StringUtils.isNotBlank(bo.getStartDate()), PayTxnDtlUsd::getTxnTime, bo.getStartDate())
            .le(StringUtils.isNotBlank(bo.getEndDate()), PayTxnDtlUsd::getTxnTime, bo.getEndDate())
            .orderByDesc(PayTxnDtlUsd::getTxnTime);
        return payTxnDtlUsdMapper.selectVoList(lqw);
    }

    /**
     * 保存USD交易记录
     * @param payTxnDtlUsd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveUsdTxnDtl(PayTxnDtlUsd payTxnDtlUsd) {
        return payTxnDtlUsdMapper.insert(payTxnDtlUsd) > 0;
    }

    /**
     * 根据条件分页查询USD交易记录
     * @param bo
     * @return
     */
    @Override
    public Page<PayTxnDtlUsdVo> queryPageByParams(PayTxnDtlUsdBo bo) {
        PageQuery pageQuery = new PageQuery(bo.getPageSize(), bo.getPageNum());
        LambdaQueryWrapper<PayTxnDtlUsd> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StringUtils.isNotBlank(bo.getTxnType()), PayTxnDtlUsd::getTxnType, bo.getTxnType())
            .in(bo.getTxnTypeList() != null && !bo.getTxnTypeList().isEmpty(), PayTxnDtlUsd::getTxnType, bo.getTxnTypeList())
            .in(bo.getUserIds() != null && !bo.getTxnTypeList().isEmpty(), PayTxnDtlUsd::getUserId, bo.getUserIds())
            .ge(StringUtils.isNotBlank(bo.getStartDate()), PayTxnDtlUsd::getTxnTime, bo.getStartDate())
            .le(StringUtils.isNotBlank(bo.getEndDate()), PayTxnDtlUsd::getTxnTime, bo.getEndDate())
            .orderByDesc(PayTxnDtlUsd::getTxnTime);
        return payTxnDtlUsdMapper.selectVoPage(pageQuery.build(), lqw);
    }
}
