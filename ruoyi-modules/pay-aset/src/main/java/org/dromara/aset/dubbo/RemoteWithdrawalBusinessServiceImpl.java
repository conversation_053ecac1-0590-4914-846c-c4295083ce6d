package org.dromara.aset.dubbo;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.dromara.aset.api.RemoteWithdrawalBusinessService;
import org.dromara.aset.domain.bo.PayCoinTxnDtlBo;
import org.dromara.aset.domain.vo.PayCoinTxnDtlVo;
import org.dromara.aset.mapper.PayCoinBalanceMapper;
import org.dromara.aset.mapper.PayCoinTxnDtlMapper;
import org.dromara.aset.service.IPayCoinBalanceService;
import org.dromara.aset.service.IPayCoinTxnDtlService;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * 提款业务服务实现类
 *
 * <p>处理提款完成后的业务逻辑</p>
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>处理提款完成确认</li>
 *   <li>更新提款记录状态</li>
 *   <li>记录区块链交易信息</li>
 *   <li>支持事务回滚</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/27
 */
@Slf4j
@Service
@DubboService
@RequiredArgsConstructor
@SaIgnore
public class RemoteWithdrawalBusinessServiceImpl implements RemoteWithdrawalBusinessService {

    /**
     * 币种交易明细服务
     */
    private final IPayCoinTxnDtlService payCoinTxnDtlService;
    private final PayCoinTxnDtlMapper payCoinTxnDtlMapper;

    /**
     * 币种余额服务
     */
    private final IPayCoinBalanceService payCoinBalanceService;
    private final PayCoinBalanceMapper payCoinBalanceMapper;

    /**
     * 处理提款完成业务逻辑
     *
     * <p>当区块链转账确认完成后，更新相关业务状态</p>
     *
     * @param requestId       转账请求ID
     * @param transactionHash 区块链交易哈希
     * @param chainName       区块链名称
     * @param confirmations   确认数量
     * @return 是否处理成功
     */
    @Override
    @SaIgnore
    @Transactional(rollbackFor = Exception.class)
    public boolean processWithdrawalCompletion(String requestId, String transactionHash,
                                               String chainName, String coinType,
                                               Integer confirmations, BigDecimal amountChange,
                                               String tenantId
    ) {
        try {
            log.info("开始处理提款完成业务逻辑 - requestId: {}, transactionHash: {}, chainName: {}, confirmations: {}",
                requestId, transactionHash, chainName, confirmations);

            // 参数校验
            if (StringUtils.isBlank(requestId)) {
                log.error("转账请求ID不能为空");
                return false;
            }

            if (StringUtils.isBlank(transactionHash)) {
                log.error("区块链交易哈希不能为空");
                return false;
            }

            PayCoinTxnDtlVo payCoinTxnDtlVo = payCoinTxnDtlService.queryById(Long.parseLong(requestId));
            if (payCoinTxnDtlVo == null) {
                log.error("该退款请求不存在:{}", requestId);
                return false;
            }

            // 1. 处理币种交易明细状态更新（从冻结状态释放）
            PayCoinTxnDtlBo coinTxnDtlBo = new PayCoinTxnDtlBo();
            coinTxnDtlBo.setTxnId(payCoinTxnDtlVo.getTxnId());
            coinTxnDtlBo.setTxnStatus(1);

            boolean txnResult = switch (tenantId) {
                case "K99999999":
                    yield TenantHelper.ignore(() -> payCoinTxnDtlMapper.processRefundFromFrozenFundsForMeta(payCoinTxnDtlVo.getTxnId(), 1) > 0);
                case "H52410001":
                    yield TenantHelper.ignore(() -> payCoinTxnDtlMapper.processRefundFromFrozenFundsForBitago(payCoinTxnDtlVo.getTxnId(), 1) > 0);
                case "H52410002":
                    yield TenantHelper.ignore(() -> payCoinTxnDtlMapper.processRefundFromFrozenFundsForRokutel(payCoinTxnDtlVo.getTxnId(), 1) > 0);
                case "H52410003":
                    yield TenantHelper.ignore(() -> payCoinTxnDtlMapper.processRefundFromFrozenFundsForSwipex(payCoinTxnDtlVo.getTxnId(), 1) > 0);
                case "H52410004":
                    yield TenantHelper.ignore(() -> payCoinTxnDtlMapper.processRefundFromFrozenFundsForVoopay(payCoinTxnDtlVo.getTxnId(), 1) > 0);
                case "H52410005":
                    yield TenantHelper.ignore(() -> payCoinTxnDtlMapper.processRefundFromFrozenFundsForXcode(payCoinTxnDtlVo.getTxnId(), 1) > 0);
                case "H52410006":
                    yield TenantHelper.ignore(() -> payCoinTxnDtlMapper.processRefundFromFrozenFundsForZombie(payCoinTxnDtlVo.getTxnId(), 1) > 0);
                default:
                    throw new IllegalStateException("tenantId没有处理方法: " + tenantId);
            };
            if (!txnResult) {
                throw new RuntimeException("更新交易明细状态失败，请检查数据");
            }

            boolean balanceResult = switch (tenantId) {
                case "K99999999":
                    yield TenantHelper.ignore(() -> payCoinBalanceMapper.processRefundFromFrozenFundsForMeta(amountChange, payCoinTxnDtlVo.getUserId(), coinType) > 0);
                case "H52410001":
                    yield TenantHelper.ignore(() -> payCoinBalanceMapper.processRefundFromFrozenFundsForBitago(amountChange, payCoinTxnDtlVo.getUserId(), coinType) > 0);
                case "H52410002":
                    yield TenantHelper.ignore(() -> payCoinBalanceMapper.processRefundFromFrozenFundsForRokutel(amountChange, payCoinTxnDtlVo.getUserId(), coinType) > 0);
                case "H52410003":
                    yield TenantHelper.ignore(() -> payCoinBalanceMapper.processRefundFromFrozenFundsForSwipex(amountChange, payCoinTxnDtlVo.getUserId(), coinType) > 0);
                case "H52410004":
                    yield TenantHelper.ignore(() -> payCoinBalanceMapper.processRefundFromFrozenFundsForVoopay(amountChange, payCoinTxnDtlVo.getUserId(), coinType) > 0);
                case "H52410005":
                    yield TenantHelper.ignore(() -> payCoinBalanceMapper.processRefundFromFrozenFundsForXcode(amountChange, payCoinTxnDtlVo.getUserId(), coinType) > 0);
                case "H52410006":
                    yield TenantHelper.ignore(() -> payCoinBalanceMapper.processRefundFromFrozenFundsForZombie(amountChange, payCoinTxnDtlVo.getUserId(), coinType) > 0);
                default:
                    throw new IllegalStateException("tenantId没有处理方法: " + tenantId);
            };

            log.info("更新币种余额状态 - userId: {}, coinCode: {}, result: {}", payCoinTxnDtlVo.getUserId(), coinType, balanceResult);

            log.info("提款完成业务逻辑处理成功 - requestId: {}", requestId);
            return true;

        } catch (Exception e) {
            log.error("处理提款完成业务逻辑失败 - requestId: {}, error: {}", requestId, e.getMessage(), e);
            throw e;
        }
    }

}
