package org.dromara.aset.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.aset.domain.PayCouponGenCfg;
import org.dromara.aset.domain.bo.PayCouponGenCfgBo;
import org.dromara.aset.domain.vo.PayCouponGenCfgVo;

public interface PayCouponGenCfgService {

    /**
     * 根据条件分页查询优惠券配置列表
     * @param bo
     * @return
     */
    IPage<PayCouponGenCfgVo> queryPageByParams(PayCouponGenCfgBo bo);

    /**
     * 新增优惠券配置
     * @param payCouponGenCfg
     * @return
     */
    boolean save(PayCouponGenCfg payCouponGenCfg);

    /**
     * 修改优惠券配置
     * @param payCouponGenCfg
     * @return
     */
    boolean edit(PayCouponGenCfg payCouponGenCfg);

    /**
     * 根据配置ID删除优惠券配置
     * @param id
     * @return
     */
    boolean deleteById(Long id);
}
