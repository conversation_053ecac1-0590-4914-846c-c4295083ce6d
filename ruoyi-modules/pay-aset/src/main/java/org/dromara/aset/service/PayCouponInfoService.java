package org.dromara.aset.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.aset.domain.PayCouponInfo;
import org.dromara.aset.domain.bo.PayCouponInfoBo;
import org.dromara.aset.domain.vo.PayCouponInfoVo;

import java.util.List;

public interface PayCouponInfoService {

    /**
     * 根据条件查询优惠券信息列表
     * @param payCouponInfoBo
     * @return
     */
    List<PayCouponInfoVo> queryListByParams(PayCouponInfoBo payCouponInfoBo);

    /**
     * 根据条件查询分页优惠券信息列表
     * @param bo
     * @return
     */
    IPage<PayCouponInfoVo> queryPageByParams(PayCouponInfoBo bo);

    /**
     * 修改优惠券信息
     * @param bo
     */
    boolean updateCouponInfoByParams(PayCouponInfoBo bo);

    /**
     * 根据优惠券配置ID 保存优惠券信息
     * @param payCouponInfoBo
     * @return
     */
    boolean saveForCfg(PayCouponInfoBo payCouponInfoBo);

    /**
     * 批量保存
     * @param list
     * @return
     */
    boolean saveAll(List<PayCouponInfo> list);

    /**
     * 根据优惠券配置ID删除
     * @param id
     * @return
     */
    boolean deleteByCfgId(Long id);

    /**
     * 修改优惠券信息
     * @param payCouponInfo
     * @return
     */
    boolean updateCouponInfo(PayCouponInfo payCouponInfo);

    /**
     * 发放优惠券
     * @param bo
     * @return
     */
    boolean grantCoupon(PayCouponInfoBo bo);

    /**
     * 核销
     * @param bo
     * @return
     */
    boolean deleteCouponInfo(PayCouponInfoBo bo);
}
