package org.dromara.aset.domain.bo;

import com.baomidou.mybatisplus.annotation.TableId;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.aset.api.domain.bo.RemoteCoinBo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@AutoMappers({
    @AutoMapper(target = RemoteCoinBo.class),
})
public class PayCoinTxnDtlBo extends PageQuery {

    /**
     * 主键
     */
    private Long txnId;

    /**
     * 交易类型
     */
    private String txnCode;

    /**
     * 状态
     */
    private Integer txnStatus;

    /**
     * 邮箱
     */
    private String userName;

    /**
     * UID
     */
    private String nickName;

    /**
     * 开始时间
     */
    private String  startDate;

    /**
     * 结束时间
     */
    private String  endDate;

    /**
     * 用户ID列表
     */
    private List<Long> userIds;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 交易ID列表
     */
    private List<Long> txnIds;

    /**
     * 交易类型列表
     */
    private List<String> txnTypeList;

    private Integer recStatus;
    private Map<String, Object> params;

}
