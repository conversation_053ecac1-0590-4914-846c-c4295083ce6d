package org.dromara.aset.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import org.dromara.aset.domain.vo.PayTxnDtlCodeVo;

import java.time.LocalDateTime;

@Data
@AutoMappers({
    @AutoMapper(target = PayTxnDtlCodeVo.class)
})
@TableName("k_meta_txn_dtl_code")
public class PayTxnDtlCode {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 交易类型:见字典表code_txn_type
     */
    private String txnType;

    /**
     * 卡类型：01-银卡，02-金卡，03-黑金卡
     */
    private String codeType;

    /**
     * 交易商品：卡id
      */
    private String txnProduct;

    /**
     * 交易数量
     */
    private Integer txnNum;

    /**
     * 交易时间
     */
    private LocalDateTime txnTime;

    /**
     * From交易账户
     */
    private Long fromUserId;

    /**
     * To交易账户
     */
    private Long toUserId;

    /**
     * 交易状态:0-失败，1-成功
     */
    private String txnStatus;

    /**
     * 交易描述
     */
    private String txnDesc;

    /**
     * 銀卡剩余可用数量
     */
    private Integer silverCodeBalance;

    /**
     * 金卡剩余可用数量
     */
    private Integer goldenCodeBalance;

    /**
     * 黑金卡剩余可用数量
     */
    private Integer goldenblackCodeBalance;

    /**
     * 白金卡剩余可用数量
     */
    private Integer whiteCodeBalance;
}
