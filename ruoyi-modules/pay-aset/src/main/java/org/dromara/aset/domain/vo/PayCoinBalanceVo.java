package org.dromara.aset.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import org.dromara.aset.api.domain.bo.RemoteCoinBo;
import org.dromara.aset.api.domain.vo.RemoteCoinVo;
import org.dromara.aset.domain.PayCoinBalance;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/6/19
 */
@Data
@AutoMappers({
    @AutoMapper(target = RemoteCoinVo.class),
})
public class PayCoinBalanceVo {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 数字货币代码(USDT)
     */
    private String coinCode;

    /**
     * 钱包地址
     */
    private String walletAddress;

    /**
     * 钱包可用余额
     */
    private BigDecimal coinBalance;

    /**
     * 冻结金额
     */
    private BigDecimal freezeBalance;

    /**
     * 未归集余额(BEP20)
     */
    private BigDecimal uncltBep20CoinBalance;

    /**
     * 未归集余额(ERC20_ARB)
     */
    private BigDecimal uncltErc20ArbCoinBalance;

    /**
     * 未归集余额(ERC20_BASE)
     */
    private BigDecimal uncltErc20BaseCoinBalance;

    /**
     * 未归集余额
     */
    private BigDecimal uncltCoinBalance;

    /**
     * 是否限制充值到账(0-限制充值到账,1-未限制)
     */
    private Boolean canRechargeCoin;

    /**
     * 是否限制提现(0-限制提现,1-未限制)
     */
    private Boolean canWithdrawCoin;

    /**
     * gate充值
     */
    private BigDecimal gateRecharge;

    /**
     * 备注
     */
    private String remark;
}
