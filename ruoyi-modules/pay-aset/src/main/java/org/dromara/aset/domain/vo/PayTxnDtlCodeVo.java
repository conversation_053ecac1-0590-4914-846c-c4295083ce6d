package org.dromara.aset.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class PayTxnDtlCodeVo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * UID
     */
    private String nickName;

    /**
     * 交易类型:见字典表code_txn_type
     */
    private String txnType;

    /**
     * 卡类型：01-银卡，02-金卡，03-黑金卡
     */
    private String codeType;

    /**
     * 交易数量
     */
    private Integer txnNum;

    /**
     * 交易时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date txnTime;

    /**
     * From交易账户
     */
    private Long fromUserId;

    /**
     * From交易账户
     */
    private String fromUserName;

    /**
     * To交易账户
     */
    private Long toUserId;

    /**
     * To交易账户
     */
    private String toUserName;

    /**
     * 交易状态:0-失败，1-成功
     */
    private String txnStatus;

    /**
     * 交易描述
     */
    private String txnDesc;

    /**
     * 銀卡剩余可用数量
     */
    private Integer silverCodeBalance;

    /**
     * 金卡剩余可用数量
     */
    private Integer goldenCodeBalance;

    /**
     * 黑金卡剩余可用数量
     */
    private Integer goldenblackCodeBalance;

    /**
     * 交易类型标签
     */
    private String typeLabel;

    /**
     * 币种类型标签
     */
    private String codeLabel;

    /**
     * 状态标签
     */
    private String statusLabel;
}
