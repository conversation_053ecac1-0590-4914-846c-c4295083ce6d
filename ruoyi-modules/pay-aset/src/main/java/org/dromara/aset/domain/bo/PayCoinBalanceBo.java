package org.dromara.aset.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import org.dromara.aset.api.domain.bo.RemoteCoinBo;
import org.dromara.common.core.validate.coin.FundsOperation;

import java.math.BigDecimal;
import java.util.List;

@Data
@AutoMappers({
    @AutoMapper(target = RemoteCoinBo.class),
})
public class PayCoinBalanceBo {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { FundsOperation.class })
    private Long userId;

    /**
     * 币种
     */
    @NotBlank(message = "币种不能为空", groups = { FundsOperation.class })
    private String currency;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空", groups = { FundsOperation.class })
    @Positive(message = "金额必须大于0", groups = { FundsOperation.class })
    private BigDecimal amount;

    /**
     * 数字货币代码(USDT)
     */
    private String coinCode;

    /**
     * 用户ID列表
     */
    private List<Long> userIds;
}
