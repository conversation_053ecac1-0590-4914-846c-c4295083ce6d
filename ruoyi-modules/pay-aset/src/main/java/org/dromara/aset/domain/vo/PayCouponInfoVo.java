package org.dromara.aset.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.aset.domain.PayCouponInfo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/25
 */
@Data
@AutoMapper(target = PayCouponInfo.class)
public class PayCouponInfoVo {

    /**
     * 主键
     */

    private Long id;

    /**
     * 优惠券配置ID
     */
    private Long couponGenCfgId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 优惠券管理用户ID
     */
    private Long admUserId;

    /**
     * 优惠券管理用户名
     */
    private String admUserName;

    /**
     * 优惠券号码
     */
    private String couponNm;

    /**
     * D-折扣券 M-满减券
     */
    private String couponType;

    /**
     * NEW-开卡、TOPUP卡充值、NODE-节点购买
     */
    private String couponScenarios;

    /**
     * 可使用卡类型
     */
    private String couponCardType;

    /**
     * 可使用卡等级产品
     */
    private String couponCardlevel;

    /**
     * 可使用节点产品
     */
    private String couponNode;

    /**
     * 折扣货币代码
     */
    private String discountCoin;

    /**
     * 折扣比例
     */
    private BigDecimal discountRate;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmt;

    /**
     * 最高抵扣金额
     */
    private BigDecimal maxDiscountAmt;

    /**
     * 使用最低消费金额
     */
    private BigDecimal minSpendAmt;

    /**
     * 生效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date eftDate;

    /**
     * 失效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date expDate;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date  updateTime;

    /**
     * 使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date usedTime;

    /**
     * 优惠券使用用户ID
     */
    private Long useUserId;

    /**
     * 优惠券使用用户名
     */
    private  String useUserName;

    /**
     * 状态（0有效，1已使用, 2核销，3过期）
     */
    private String status;

    /**
     * 场景合集
     */
    private String[] couponScenariosList;

    /**
     * 可类型集合
     */
    private String[] couponCardTypeList;

    /**
     * 卡级别
     */
    private String[] couponCardlevelList;

    /**
     * 节点集合
     */
    private String[] couponNodeList;

    /**
     * 优惠券使用次数限制(0-无限使用)
     */
    private Integer couponUsageLimit;

    /**
     * 优惠券使用次数
     */
    private Integer couponUsageCnt;

    /**
     * 类型
     */
    private String typeDetail;

    /**
     * 用途
     */
    private String scenariosDetail;

    /**
     * 状态
     */
    private String statusDetail;

    /**
     * 可类型集合(tg)
     */

    private List<String> cardTypeList;

    /**
     * 卡级别(tg)
     */

    private List<String> cardlevelList;

    /**
     * 节点集合(tg)
     */
    private List<String> nodeList;

    /**
     * 金额
     */
    private String amount;
}
