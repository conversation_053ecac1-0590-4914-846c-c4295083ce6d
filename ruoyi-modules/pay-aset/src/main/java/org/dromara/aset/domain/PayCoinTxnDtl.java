package org.dromara.aset.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import org.dromara.aset.api.domain.bo.RemoteCoinTxnDtlBo;
import org.dromara.aset.api.domain.bo.RemoteTxnDtlUsdBo;
import org.dromara.aset.api.domain.vo.RemoteCoinTxnDtlVo;
import org.dromara.aset.domain.vo.PayCoinTxnDtlVo;
import org.dromara.aset.domain.vo.PayTxnDtlUsdVo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/6/26
 */
@Data
@TableName("k_meta_coin_txn_dtl")
@AutoMappers({
    @AutoMapper(target = PayCoinTxnDtlVo.class),
    @AutoMapper(target = RemoteCoinTxnDtlBo.class)
})
public class PayCoinTxnDtl {

    /**
     * 主键
     */
    @TableId("txn_id")
    private Long txnId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 0支出  1收入
     */
    private Integer recordType;

    /**
     * d1010充值|c1010提币|t1010转账
     */
    private String txnCode;

    /**
     * 链上交易号
     */
    private String receiptNo;

    /**
     * 交易钱包付款人
     */
    private String fromAddress;

    /**
     * 交易收款人
     */
    private String toAddress;

    /**
     * 交易货币(USDT)
     */
    private String txnCoin;

    /**
     * 交易状态:2-待审核，1-已完成，0-已拒绝
     */
    private Integer txnStatus;

    /**
     * 交易描述
     */
    private String txnDesc;

    /**
     * 交易金额
     */
    private BigDecimal txnAmount;

    /**
     * 手续费
     */
    private BigDecimal txnFee;

    /**
     * 交易时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date txnTime;

    /**
     * to交易对货币代码
     */
    private String toCoin;

    /**
     * to交换对货币金额
     */
    private BigDecimal toAmount;

    /**
     * 交易对汇率
     */
    private BigDecimal exchgRate;

    /**
     * 客户资产余额
     */
    private BigDecimal userBalance;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 币种网络
     */
    private String coinNet;

    /**
     * gate用户卡码费
     */
    private BigDecimal gateCodeFee;

    /**
     * 交易商品
     */
    private String txnProduct;

    /**
     * 订单总价格
     */
    private BigDecimal totalPrice;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmt;

    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * from交易对货币代码
     */
    private String fromCoin;

    /**
     * from交换对货币金额
     */
    private BigDecimal fromAmount;

    /**
     * 关联流水ID
     */
    private Long relaTxnid;
}
