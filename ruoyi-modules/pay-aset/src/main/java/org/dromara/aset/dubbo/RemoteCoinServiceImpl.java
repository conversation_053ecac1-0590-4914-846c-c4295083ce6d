package org.dromara.aset.dubbo;

import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.dromara.aset.api.RemoteCoinService;
import org.dromara.aset.api.domain.bo.RemoteCoinBo;
import org.dromara.aset.api.domain.bo.RemoteCoinTxnDtlBo;
import org.dromara.aset.api.domain.bo.RemoteTxnDtlUsdBo;
import org.dromara.aset.api.domain.vo.RemoteCoinTxnDtlVo;
import org.dromara.aset.api.domain.vo.RemoteCoinVo;
import org.dromara.aset.api.domain.vo.RemoteTxnDtlUsdVo;
import org.dromara.aset.domain.PayCoinTxnDtl;
import org.dromara.aset.domain.PayTxnDtlUsd;
import org.dromara.aset.domain.bo.PayCoinBalanceBo;
import org.dromara.aset.domain.bo.PayCoinTxnDtlBo;
import org.dromara.aset.domain.bo.PayTxnDtlUsdBo;
import org.dromara.aset.domain.vo.PayCoinBalanceVo;
import org.dromara.aset.domain.vo.PayCoinTxnDtlVo;
import org.dromara.aset.domain.vo.PayTxnDtlUsdVo;
import org.dromara.aset.service.IPayCoinBalanceService;
import org.dromara.aset.service.IPayCoinTxnDtlService;
import org.dromara.aset.service.PayTxnDtlUsdService;
import org.dromara.common.core.utils.MapstructUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/19
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteCoinServiceImpl implements RemoteCoinService {

    private final IPayCoinTxnDtlService payCoinTxnDtlService;
    private final PayTxnDtlUsdService payTxnDtlUsdService;
    private final IPayCoinBalanceService payCoinBalanceService;

    /**
     * 根据条件查询钱包列表
     * @param bo
     * @return
     */
    @Override
    public List<RemoteCoinVo> selectWalletListByParam(RemoteCoinBo bo) {
        PayCoinBalanceBo payCoinBalanceBo = MapstructUtils.convert(bo, PayCoinBalanceBo.class);
        List<PayCoinBalanceVo> list = payCoinBalanceService.selectWalletListByParam(payCoinBalanceBo);
        return MapstructUtils.convert(list, RemoteCoinVo.class);
    }

    /**
     * 根据条件查询币种交易记录
     * @param bo
     * @return
     */
    @Override
    public List<RemoteCoinTxnDtlVo> queryCoinTxnDtlListByParams(RemoteCoinBo bo) {
        PayCoinTxnDtlBo payCoinTxnDtlBo = MapstructUtils.convert(bo, PayCoinTxnDtlBo.class);
        List<PayCoinTxnDtlVo> payCoinTxnDtlVos = payCoinTxnDtlService.queryListByParams(payCoinTxnDtlBo);
        return MapstructUtils.convert(payCoinTxnDtlVos, RemoteCoinTxnDtlVo.class);
    }

    /**
     * 根据条件查询USD交易记录
     * @param bo
     * @return
     */
    @Override
    public List<RemoteTxnDtlUsdVo> queryTxnDtlUsdListByParams(RemoteCoinBo bo) {
        PayTxnDtlUsdBo payTxnDtlUsdBo = MapstructUtils.convert(bo, PayTxnDtlUsdBo.class);
        List<PayTxnDtlUsdVo> payTxnDtlUsdVos = payTxnDtlUsdService.queryListByParams(payTxnDtlUsdBo);
        return MapstructUtils.convert(payTxnDtlUsdVos, RemoteTxnDtlUsdVo.class);
    }

    /**
     * 增加钱包余额
     * @param bo
     * @return
     */
    @Override
    public boolean IncreaseCoinBalance(RemoteCoinBo bo) {
        PayCoinBalanceBo payCoinBalanceBo = new PayCoinBalanceBo();
        payCoinBalanceBo.setUserId(bo.getUserId());
        payCoinBalanceBo.setAmount(bo.getAmount());
        payCoinBalanceBo.setCurrency(bo.getCurrency());
        return payCoinBalanceService.IncreaseBalance(payCoinBalanceBo);
    }

    /**
     * 根据用户ID和币种查询钱包
     * @param bo
     * @return
     */
    @Override
    public RemoteCoinVo selectOneByUserIdAndCurrency(RemoteCoinBo bo) {
        PayCoinBalanceBo payCoinBalanceBo = new PayCoinBalanceBo();
        payCoinBalanceBo.setUserId(bo.getUserId());
        payCoinBalanceBo.setCurrency(bo.getCurrency());
        PayCoinBalanceVo payCoinBalanceVo = payCoinBalanceService.selectOneByUserIdAndCurrency(payCoinBalanceBo);
        return MapstructUtils.convert(payCoinBalanceVo, RemoteCoinVo.class);
    }

    /**
     * 保存USD交易记录
     * @param bo
     * @return
     */
    @Override
    public boolean saveUsdTxnDtl(RemoteTxnDtlUsdBo bo){
        PayTxnDtlUsd payTxnDtlUsd = MapstructUtils.convert(bo, PayTxnDtlUsd.class);
        return payTxnDtlUsdService.saveUsdTxnDtl(payTxnDtlUsd);
    }

    /**
     * 保存币种交易记录
     * @param coinTxnDtl
     * @return
     */
    @Override
    public boolean saveCoinTxnDtl(RemoteCoinTxnDtlBo coinTxnDtl) {
        PayCoinTxnDtl payCoinTxnDtl = MapstructUtils.convert(coinTxnDtl, PayCoinTxnDtl.class);
        return payCoinTxnDtlService.saveCoinTxnDtl(payCoinTxnDtl);
    }
}
