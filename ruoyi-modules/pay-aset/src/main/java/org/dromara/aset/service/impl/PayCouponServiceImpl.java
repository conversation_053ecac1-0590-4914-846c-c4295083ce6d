package org.dromara.aset.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.aset.domain.PayCouponGenCfg;
import org.dromara.aset.domain.PayCouponInfo;
import org.dromara.aset.domain.bo.PayCouponGenCfgBo;
import org.dromara.aset.domain.bo.PayCouponInfoBo;
import org.dromara.aset.domain.vo.PayCouponGenCfgVo;
import org.dromara.aset.domain.vo.PayCouponInfoVo;
import org.dromara.aset.mapper.PayCouponGenCfgMapper;
import org.dromara.aset.mapper.PayCouponInfoMapper;
import org.dromara.aset.service.PayCouponGenCfgService;
import org.dromara.aset.service.PayCouponInfoService;
import org.dromara.aset.service.PayCouponService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.enums.BusinessStatus;
import org.dromara.common.core.enums.CouponEnum;
import org.dromara.common.core.enums.DictType;
import org.dromara.common.core.exception.BusinessException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.MessageUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.api.RemoteConfigService;
import org.dromara.system.api.RemoteDictService;
import org.dromara.system.api.RemoteUserService;
import org.dromara.system.api.domain.vo.RemoteUserVo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/6/25
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PayCouponServiceImpl implements PayCouponService {

    private final PayCouponGenCfgService payCouponGenCfgService;
    private final PayCouponInfoService payCouponInfoService;
    private final PayCouponInfoMapper payCouponInfoMapper;

    @DubboReference
    private final RemoteConfigService remoteConfigService;
    @DubboReference
    private final RemoteUserService remoteUserService;
    @DubboReference
    private final RemoteDictService remoteDictService;


    /**
     * 查询优惠券配置列表
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<PayCouponGenCfgVo> queryGenCfgPageList(PayCouponGenCfgBo bo) {
        IPage<PayCouponGenCfgVo> page = payCouponGenCfgService.queryPageByParams(bo);
        List<PayCouponGenCfgVo> list = page.getRecords();
        // 填入创建者的名字
        List<Long> userIds = list.stream()
            .map(PayCouponGenCfgVo::getAdmUserId)
            .distinct()
            .toList();
        List<RemoteUserVo> userList = remoteUserService.selectListByIds(userIds);
        Map<Long, RemoteUserVo> userMap = userList.stream().collect(Collectors.toMap(RemoteUserVo::getUserId, Function.identity()));
        for (PayCouponGenCfgVo vo : list) {
            vo.setAdmUserName(userMap.get(vo.getAdmUserId()) != null ? userMap.get(vo.getAdmUserId()).getUserName() : "");

//            vo.setCouponScenariosList(getResult(vo.getCouponScenarios()));
            vo.setCouponCardTypeList(getResult(vo.getCouponCardType()));
            vo.setCouponCardlevelList(getResult(vo.getCouponCardlevel()));
            vo.setCouponNodeList(getResult(vo.getCouponNode()));
            if (vo.getDiscountRate() != null) {
                vo.setDiscountRate(vo.getDiscountRate().multiply(new BigDecimal("100")));
            }
            if(vo.getCouponUsageLimit() == 0){
                PayCouponInfoBo payCouponInfoBo = new PayCouponInfoBo();
                payCouponInfoBo.setCouponGenCfgId(vo.getId());
                List<PayCouponInfoVo> data = payCouponInfoService.queryListByParams(payCouponInfoBo);
                if(!data.isEmpty()){
                    vo.setCouponNm(data.get(0).getCouponNm());
                }
            }
        }
        return TableDataInfo.build(page);
    }

    private String[] getResult(String str) {
        String[] result = null;
        if (StringUtils.isNotEmpty(str)) {

            if (str.contains(",")) {
                result = str.split(",");
            } else {
                result = new String[1]; // 初始化数组
                result[0] = str; // 将单个元素赋值给数组
            }

        }
        return result;

    }

    /**
     * 查询优惠券信息列表
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<PayCouponInfoVo> queryCouponInfoPageList(PayCouponInfoBo bo) {
        IPage<PayCouponInfoVo> page = payCouponInfoService.queryPageByParams(bo);

        for (PayCouponInfoVo vo : page.getRecords()) {
            List<Long> userIds = new ArrayList<>();
            userIds.add(vo.getAdmUserId());
            userIds.add(vo.getUserId());
            userIds.add(vo.getUseUserId());
            List<RemoteUserVo> remoteUserVos = remoteUserService.selectListByIds(userIds);
            for (RemoteUserVo remoteUserVo : remoteUserVos) {
                if (remoteUserVo.getUserId().equals(vo.getAdmUserId())) {
                    vo.setAdmUserName(remoteUserVo.getUserName());
                } else if (remoteUserVo.getUserId().equals(vo.getUserId())) {
                    vo.setUserName(remoteUserVo.getUserName());
                } else if (remoteUserVo.getUserId().equals(vo.getUseUserId())) {
                    vo.setUseUserName(remoteUserVo.getUserName());
                }
            }

//            vo.setCouponScenariosList(getResult(vo.getCouponScenarios()));
            vo.setCouponCardTypeList(getResult(vo.getCouponCardType()));
            vo.setCouponCardlevelList(getResult(vo.getCouponCardlevel()));
            vo.setCouponNodeList(getResult(vo.getCouponNode()));

            //判断是否失效
            if (Objects.equals(vo.getStatus(), BusinessStatus.COUPON_VALID.getValue()) && vo.getExpDate().after(new Date())) {
                vo.setStatus(BusinessStatus.COUPON_EXPIRED.getValue());
                PayCouponInfoBo payCouponInfoBo = new PayCouponInfoBo();
                payCouponInfoBo.setId(vo.getId());
                payCouponInfoBo.setStatus(vo.getStatus());
                payCouponInfoService.updateCouponInfoByParams(bo);
            }
            if (vo.getDiscountRate() != null) {
                vo.setDiscountRate(vo.getDiscountRate().multiply(new BigDecimal("100")));
            }
            if (vo.getCouponType() != null) {
                vo.setTypeDetail(remoteDictService.selectDictLabel(DictType.ASET_COPN_TYPE.getValue(), vo.getCouponType()));
            }

            if (StringUtils.isNotEmpty(vo.getCouponScenarios())) {
                vo.setScenariosDetail(remoteDictService.selectDictLabel(DictType.ASET_COPN_SCEN.getValue(), vo.getCouponScenarios()));
            }

            if (vo.getStatus() != null) {
                vo.setStatusDetail(remoteDictService.selectDictLabel(DictType.ASET_COPN_STATUS.getValue(), vo.getStatus()));
            }
            if (vo.getCouponCardTypeList() != null && vo.getCouponCardTypeList().length > 0) {
                List<String> list = new ArrayList<>();
                for (String type : vo.getCouponCardTypeList()) {
                    String s = remoteDictService.selectDictLabel(DictType.CARD_TYPE.getValue(), type);
                    list.add(s);
                }
                vo.setCardTypeList(list);
            }
            if (vo.getCouponCardlevelList() != null && vo.getCouponCardlevelList().length > 0) {
                List<String> list = new ArrayList<>();
                for (String type : vo.getCouponCardlevelList()) {
                    String s = remoteDictService.selectDictLabel(DictType.CARD_LEVEL.getValue(), type);
                    list.add(s);
                }
                vo.setCardlevelList(list);
            }

            if (vo.getCouponNodeList() != null && vo.getCouponNodeList().length > 0) {
                List<String> list = new ArrayList<>();
                for (String type : vo.getCouponNodeList()) {
                    String s = remoteDictService.selectDictLabel(DictType.ACCT_NODE_TYPE.getValue(), type);
                    list.add(s);
                }
                vo.setNodeList(list);
            }

        }
        return TableDataInfo.build(page);
    }

    /**
     * 新增优惠券配置
     * @param bo
     * @return
     */
    @Override
    public boolean insertCouponConfig(PayCouponGenCfgBo bo) {
        //判断券码是否已经存在
        if (StringUtils.isNotEmpty(bo.getCouponNm())) {
            PayCouponInfo payCouponInfo = payCouponInfoMapper.selectOne(new LambdaQueryWrapper<PayCouponInfo>()
                .eq(PayCouponInfo::getCouponNm, bo.getCouponNm())
                .eq(PayCouponInfo::getStatus, BusinessStatus.COUPON_VALID.getValue()));
            if (payCouponInfo != null) {
                throw new BusinessException(MessageUtils.message("coupon.error20"));
            }
        }

        //判断开卡的时候折扣率不等大于
        if (!CouponEnum.COUPON_OPEN.getValue().equals(bo.getCouponScenarios())) {
            if (CouponEnum.COUPON_DISCOUNT.getValue().equals(bo.getCouponType())) {
                String couponRate = remoteConfigService.selectConfigByKey("coupon_rate");
                BigDecimal rate = new BigDecimal(couponRate);

                if (bo.getDiscountRate() != null && bo.getDiscountRate().compareTo(rate) > 0) {
                    throw new BusinessException(MessageUtils.message("coupon.error13", rate));
                }

            } else if (CouponEnum.COUPON_FULL_DISCOUNT.getValue().equals(bo.getCouponType())) {
                if (bo.getDiscountAmt().compareTo(bo.getMinSpendAmt()) >= 0) {
                    throw new BusinessException(MessageUtils.message("coupon.error14"));
                }
            }
        }
        if (bo.getDiscountRate() != null) {
            BigDecimal amount = bo.getDiscountRate().divide(new BigDecimal("100"));
            bo.setDiscountRate(amount);
        }

        // 保存优惠券配置
        PayCouponGenCfg payCouponGenCfg = new PayCouponGenCfg();
        MapstructUtils.convert(bo, payCouponGenCfg);
        if (payCouponGenCfg.getAdmUserId() == null) {
            payCouponGenCfg.setAdmUserId(LoginHelper.getUserId());
        }
//        if(metaCouponGenCfg.getCouponScenariosList()!=null){
//            metaCouponGenCfg.setCouponScenarios(String.join(",", metaCouponGenCfg.getCouponScenariosList()));
//        }
        if (payCouponGenCfg.getCouponCardTypeList() != null) {
            payCouponGenCfg.setCouponCardType(String.join(",", payCouponGenCfg.getCouponCardTypeList()));
        }
        if (payCouponGenCfg.getCouponCardlevelList() != null) {
            payCouponGenCfg.setCouponCardlevel(String.join(",", payCouponGenCfg.getCouponCardlevelList()));
        }
        if (payCouponGenCfg.getCouponNodeList() != null) {
            payCouponGenCfg.setCouponNode(String.join(",", payCouponGenCfg.getCouponNodeList()));
        }
        payCouponGenCfg.setCouponAva(payCouponGenCfg.getCouponTot());
        payCouponGenCfg.setCouponUsed(0);
        payCouponGenCfg.setCreateTime(new Date());
        payCouponGenCfg.setUpdateTime(new Date());
        payCouponGenCfgService.save(payCouponGenCfg);

        // 保存优惠券信息
        PayCouponInfoBo payCouponInfoBo = new PayCouponInfoBo();
        payCouponInfoBo.setCouponGenCfgId(payCouponGenCfg.getId());
        payCouponInfoService.saveForCfg(payCouponInfoBo);

        return true;
    }

    /**
     * 修改优惠券配置
     * @param bo
     * @return
     */
    @Override
    public boolean updateCouponConfig(PayCouponGenCfgBo bo) {
        //判断开卡的时候折扣率不能小于
        if (!CouponEnum.COUPON_OPEN.getValue().equals(bo.getCouponScenarios())) {
            if (CouponEnum.COUPON_DISCOUNT.getValue().equals(bo.getCouponType())) {
                String couponRate = remoteConfigService.selectConfigByKey("coupon_rate");
                BigDecimal rate = new BigDecimal(couponRate);

                if (bo.getDiscountRate() != null && bo.getDiscountRate().compareTo(rate) > 0) {
                    throw new BusinessException(MessageUtils.message("coupon.error13", rate));
                }

            } else if (CouponEnum.COUPON_FULL_DISCOUNT.getValue().equals(bo.getCouponType())) {
                if (bo.getDiscountAmt().compareTo(bo.getMinSpendAmt()) >= 0) {
                    throw new BusinessException(MessageUtils.message("coupon.error14"));
                }
            }
        }
        if (bo.getDiscountRate() != null) {
            BigDecimal amount = bo.getDiscountRate().divide(new BigDecimal("100"));
            bo.setDiscountRate(amount);
        }

        PayCouponGenCfg payCouponGenCfg = new PayCouponGenCfg();
        MapstructUtils.convert(bo, payCouponGenCfg);
        if (payCouponGenCfg.getCouponCardTypeList() != null) {
            payCouponGenCfg.setCouponCardType(String.join(",", payCouponGenCfg.getCouponCardTypeList()));
        }
        if (payCouponGenCfg.getCouponCardlevelList() != null) {
            payCouponGenCfg.setCouponCardlevel(String.join(",", payCouponGenCfg.getCouponCardlevelList()));
        }
        if (payCouponGenCfg.getCouponNodeList() != null) {
            payCouponGenCfg.setCouponNode(String.join(",", payCouponGenCfg.getCouponNodeList()));
        }
        payCouponGenCfg.setUpdateTime(new Date());

        payCouponGenCfgService.edit(payCouponGenCfg);

        PayCouponInfoBo payCouponInfoBo = new PayCouponInfoBo();
        payCouponInfoBo.setCouponGenCfgId(payCouponGenCfg.getId());
        List<PayCouponInfoVo> payCouponInfoVos = payCouponInfoService.queryListByParams(payCouponInfoBo);
        List<PayCouponInfo> list = new ArrayList<>();
        for (PayCouponInfoVo info : payCouponInfoVos) {
            PayCouponInfo payCouponInfo = new PayCouponInfo();
            MapstructUtils.convert(info, payCouponInfo);
            MapstructUtils.convert(payCouponGenCfg, payCouponInfo);
            payCouponInfo.setId(info.getId());
            info.setUpdateTime(new Date());
            list.add(payCouponInfo);
        }
        if (!payCouponInfoVos.isEmpty()) {
            payCouponInfoService.saveAll(list);
        }
        return true;
    }

    /**
     * 删除优惠券配置
     * @param id
     * @return
     */
    @Override
    public boolean deleteCouponConfig(Long id) {
        payCouponGenCfgService.deleteById(id);
        payCouponInfoService.deleteByCfgId(id);
        return true;
    }

    /**
     * 修改优惠券信息
     * @param bo
     * @return
     */
    @Override
    public boolean updateCouponInfo(PayCouponInfoBo bo) {
        if (bo.getDiscountRate() != null) {
            BigDecimal amount = bo.getDiscountRate().divide(new BigDecimal("100"));
            bo.setDiscountRate(amount);
        }
        PayCouponInfo payCouponInfo = new PayCouponInfo();
        MapstructUtils.convert(bo, payCouponInfo);
        return payCouponInfoService.updateCouponInfo(payCouponInfo);
    }

    /**
     * 发放优惠券
     * @param bo
     * @return
     */
    @Override
    public boolean grantCoupon(PayCouponInfoBo bo) {
        return payCouponInfoService.grantCoupon(bo);
    }

    /**
     * 核销
     * @param bo
     * @return
     */
    @Override
    public boolean deleteCouponInfo(PayCouponInfoBo bo) {
        return payCouponInfoService.deleteCouponInfo(bo);
    }
}
