package org.dromara.aset.service;

import org.dromara.aset.domain.bo.PayCouponGenCfgBo;
import org.dromara.aset.domain.bo.PayCouponInfoBo;
import org.dromara.aset.domain.vo.PayCouponGenCfgVo;
import org.dromara.aset.domain.vo.PayCouponInfoVo;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * <AUTHOR>
 * @Date 2025/6/25
 */

public interface PayCouponService {

    /**
     * 查询优惠券配置列表
     * @param bo
     * @return
     */
    TableDataInfo<PayCouponGenCfgVo> queryGenCfgPageList(PayCouponGenCfgBo bo);

    /**
     * 查询优惠券信息列表
     * @param bo
     * @return
     */
    TableDataInfo<PayCouponInfoVo> queryCouponInfoPageList(PayCouponInfoBo bo);

    /**
     * 新增优惠券配置
     * @param bo
     * @return
     */
    boolean insertCouponConfig(PayCouponGenCfgBo bo);

    /**
     * 修改优惠券配置
     * @param bo
     * @return
     */
    boolean updateCouponConfig(PayCouponGenCfgBo bo);

    /**
     * 删除优惠券配置
     * @param id
     * @return
     */
    boolean deleteCouponConfig(Long id);

    /**
     * 修改优惠券信息
     * @param bo
     * @return
     */
    boolean updateCouponInfo(PayCouponInfoBo bo);

    /**
     * 发放优惠券
     * @param bo
     * @return
     */
    boolean grantCoupon(PayCouponInfoBo bo);

    /**
     * 核销
     * @param bo
     * @return
     */
    boolean deleteCouponInfo(PayCouponInfoBo bo);
}
