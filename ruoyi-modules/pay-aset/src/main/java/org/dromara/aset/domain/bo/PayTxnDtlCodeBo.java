package org.dromara.aset.domain.bo;

import lombok.Data;
import org.dromara.common.core.enums.TxnType;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.List;

@Data
public class PayTxnDtlCodeBo extends PageQuery {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 交易类型
     */
    private String txnType;

    /**
     * 交易类型列表
     */
    private List<TxnType> txnTypes;

    /**
     * 交易商品：卡id
     */
    private String txnProduct;

    /**
     * 交易ID
     */
    private String transactionId;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 用户名
     */
    private String userName;

    /**
     * UID
     */
    private String nickName;

    /**
     * 交易状态
     */
    private String txnStatus;

    /**
     * 用户ID列表
     */
    private List<Long> userIds;
}
