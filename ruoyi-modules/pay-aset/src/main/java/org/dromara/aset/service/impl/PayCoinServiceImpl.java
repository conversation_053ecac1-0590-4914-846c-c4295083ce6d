package org.dromara.aset.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.aset.domain.bo.PayCoinTxnDtlBo;
import org.dromara.aset.domain.bo.PayTxnDtlCodeBo;
import org.dromara.aset.domain.bo.PayTxnDtlUsdBo;
import org.dromara.aset.domain.vo.PayCoinTxnDtlVo;
import org.dromara.aset.domain.vo.PayTxnDtlCodeVo;
import org.dromara.aset.domain.vo.PayTxnDtlUsdVo;
import org.dromara.aset.service.PayCoinService;
import org.dromara.aset.service.PayTxnDtlCodeService;
import org.dromara.aset.service.PayTxnDtlUsdService;
import org.dromara.common.core.enums.DictType;
import org.dromara.common.core.enums.TxnType;
import org.dromara.aset.service.IPayCoinTxnDtlService;
import org.dromara.aset.service.PayTxnDtlCodeService;
import org.dromara.aset.service.PayTxnDtlUsdService;
import org.dromara.common.core.enums.DictType;
import org.dromara.common.core.enums.TxnType;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.api.RemoteDictService;
import org.dromara.system.api.RemoteUserService;
import org.dromara.system.api.domain.bo.RemoteClientBo;
import org.dromara.system.api.domain.vo.RemoteUserVo;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/6/26
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PayCoinServiceImpl implements PayCoinService {

    private final PayTxnDtlCodeService payTxnDtlCodeService;
    private final PayTxnDtlUsdService payTxnDtlUsdService;
    private final IPayCoinTxnDtlService payCoinTxnDtlService;

    private final RemoteUserService remoteUserService;
    @DubboReference
    private final RemoteDictService remoteDictService;

    /**
     * 根据条件查询币种交易分页列表
     * ---条件需查询指定用户 -> 先查用户后分页查交易记录
     * ---条件不查询指定用户 -> 直接分页查交易记录
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<PayCoinTxnDtlVo> queryCoinTxnPageList(PayCoinTxnDtlBo bo) {
        PageQuery pageQuery = new PageQuery(bo.getPageSize(), bo.getPageNum());

        List<Long> userIds = new ArrayList<>();
        if (StringUtils.isNotBlank(bo.getUserName()) || StringUtils.isNotBlank(bo.getNickName())) {
            RemoteClientBo remoteClientBo = new RemoteClientBo();
            remoteClientBo.setNickName(bo.getNickName());
            remoteClientBo.setUserName(bo.getUserName());
            List<RemoteUserVo> remoteUserVos = remoteUserService.selectUserListByParam(remoteClientBo);
            for (RemoteUserVo vo : remoteUserVos) {
                userIds.add(vo.getUserId());
            }
        }

        bo.setUserIds(userIds);
        IPage<PayCoinTxnDtlVo> page = payCoinTxnDtlService.queryPageByParams(bo, pageQuery);
        List<PayCoinTxnDtlVo> records = page.getRecords();
        for (PayCoinTxnDtlVo record : records) {
            userIds.add(record.getUserId());
            if (StringUtils.isNumeric(record.getFromAddress())){
                userIds.add(Long.valueOf(record.getFromAddress()));
            }
            if (StringUtils.isNumeric(record.getToAddress())){
                userIds.add(Long.valueOf(record.getToAddress()));
            }
        }

        List<RemoteUserVo> remoteUserVos = remoteUserService.selectListByIds(userIds);

        Map<Long, RemoteUserVo> userMap = remoteUserVos.stream()
            .collect(Collectors.toMap(RemoteUserVo::getUserId, Function.identity()));
        Map<String, String> dictDataMap = remoteDictService.selectLocalizedDictDataByType(DictType.ASET_COIN_TXN_TYPE.getValue());

        for (PayCoinTxnDtlVo vo : records) {
            vo.setTxnCodeDetail(dictDataMap.getOrDefault(vo.getTxnCode(), ""));

            RemoteUserVo user = userMap.get(vo.getUserId());
            RemoteUserVo fromUser = null;
            RemoteUserVo toUser = null;

            if (StringUtils.isNumeric(vo.getFromAddress())){
                fromUser = userMap.get(Long.valueOf(vo.getFromAddress()));
            }
            if (StringUtils.isNumeric(vo.getToAddress())){
                toUser = userMap.get(Long.valueOf(vo.getToAddress()));
            }

            if (user != null){
                vo.setUserName(user.getUserName());
                vo.setNickName(user.getNickName());
            }
            if (fromUser != null){
                vo.setFromAddress(fromUser.getUserName());
            }
            if (toUser != null){
                vo.setToAddress(toUser.getUserName());
            }
        }
        return TableDataInfo.build(page);
    }

    /**
     * 获取开卡币明细列表
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<PayTxnDtlCodeVo> queryCodeTxnPageList(PayTxnDtlCodeBo bo) {
        Map<Long, RemoteUserVo> usersMap = new HashMap<>();
        List<Long> userIds = new ArrayList<>();

        // 如果存在条件查询用户名和昵称 -> 组装用户id -> 再去查询数据
        if (StringUtils.isNotBlank(bo.getUserName()) || StringUtils.isNotBlank(bo.getNickName())) {
            RemoteClientBo remoteClientBo = new RemoteClientBo();
            remoteClientBo.setNickName(bo.getNickName());
            remoteClientBo.setUserName(bo.getUserName());
            List<RemoteUserVo> userList = remoteUserService.selectUserListByParam(remoteClientBo);

            for (RemoteUserVo vo : userList) {
                usersMap.put(vo.getUserId(), vo);
                userIds.add(vo.getUserId());
            }
            bo.setUserIds(userIds);

            if (userIds.isEmpty()) return TableDataInfo.build(new ArrayList<>());

        }

        Page<PayTxnDtlCodeVo> page = payTxnDtlCodeService.queryPageByParams(bo);
        List<PayTxnDtlCodeVo> txnDtlUsdVos = page.getRecords();


        if (!txnDtlUsdVos.isEmpty()){
            userIds.clear();
            for (PayTxnDtlCodeVo txnDtlCodeVo : txnDtlUsdVos) {
                userIds.add(txnDtlCodeVo.getUserId());
                userIds.add(txnDtlCodeVo.getFromUserId());
                userIds.add(txnDtlCodeVo.getToUserId());
            }
            RemoteClientBo remoteClientBo = new RemoteClientBo();
            remoteClientBo.setIds(userIds);
            List<RemoteUserVo> userList = remoteUserService.selectUserListByParam(remoteClientBo);
            for (RemoteUserVo remoteUserVo : userList) {
                usersMap.put(remoteUserVo.getUserId(), remoteUserVo);
            }
        }

        for (PayTxnDtlCodeVo txnDtlCodeVo : txnDtlUsdVos) {
            RemoteUserVo user = usersMap.get(txnDtlCodeVo.getUserId());
            RemoteUserVo fromUser = usersMap.get(txnDtlCodeVo.getFromUserId());
            RemoteUserVo toUser = usersMap.get(txnDtlCodeVo.getToUserId());
            if (user != null) {
                txnDtlCodeVo.setUserName(user.getUserName());
                txnDtlCodeVo.setNickName(user.getNickName());
            }
            if (fromUser != null) {
                txnDtlCodeVo.setFromUserName(fromUser.getUserName());
            }
            if (toUser != null) {
                txnDtlCodeVo.setToUserName(toUser.getUserName());
            }

            if (StringUtils.isNotEmpty(txnDtlCodeVo.getCodeType())) {
                txnDtlCodeVo.setCodeLabel(remoteDictService.selectDictLabel(DictType.CARD_LEVEL.getValue(), txnDtlCodeVo.getCodeType()));
            }
            if (StringUtils.isNotEmpty(txnDtlCodeVo.getTxnType())) {
                if (TxnType.CARD_ACTIVATION.getValue().equals(txnDtlCodeVo.getTxnType())) {
                    txnDtlCodeVo.setToUserName("");
                    txnDtlCodeVo.setFromUserName("");
                }
                txnDtlCodeVo.setTypeLabel(remoteDictService.selectDictLabel(DictType.ASET_CODE_TXN_TYPE.getValue(), txnDtlCodeVo.getTxnType()));
            }
            if (txnDtlCodeVo.getTxnStatus() != null) {
                txnDtlCodeVo.setStatusLabel(remoteDictService.selectDictLabel(DictType.ASET_COIN_TXN_STATUS.getValue(), txnDtlCodeVo.getTxnStatus().toString()));
            }
        }

        return TableDataInfo.build(page);
    }
}
