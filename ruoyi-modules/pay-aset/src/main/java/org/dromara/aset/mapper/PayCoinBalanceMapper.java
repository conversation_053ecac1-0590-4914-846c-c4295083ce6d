package org.dromara.aset.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.dromara.aset.domain.PayCoinBalance;
import org.dromara.aset.domain.bo.PayCoinBalanceBo;
import org.dromara.aset.domain.vo.PayCoinBalanceVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/19
 */
@Mapper
public interface PayCoinBalanceMapper extends BaseMapperPlus<PayCoinBalance, PayCoinBalanceVo> {


    int processRefundFromFrozenFunds(PayCoinBalanceBo bo);

    // 7个不同表，需要基于tenant_id选择 - 统一使用与Meta相同的参数签名
    int processRefundFromFrozenFundsForBitago(BigDecimal amountChange, Long userId, String coinCode);
    int processRefundFromFrozenFundsForMeta(BigDecimal amountChange, Long userId, String coinCode);
    int processRefundFromFrozenFundsForRokutel(BigDecimal amountChange, Long userId, String coinCode);
    int processRefundFromFrozenFundsForSwipex(BigDecimal amountChange, Long userId, String coinCode);
    int processRefundFromFrozenFundsForVoopay(BigDecimal amountChange, Long userId, String coinCode);
    int processRefundFromFrozenFundsForXcode(BigDecimal amountChange, Long userId, String coinCode);
    int processRefundFromFrozenFundsForZombie(BigDecimal amountChange, Long userId, String coinCode);



}
