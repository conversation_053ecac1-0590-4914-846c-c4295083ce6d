package org.dromara.aset.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import org.dromara.aset.api.domain.bo.RemoteTxnDtlUsdBo;
import org.dromara.aset.domain.vo.PayTxnDtlUsdVo;
import org.dromara.common.log.event.OperLogEvent;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("k_meta_txn_dtl_usd")
@AutoMappers({
    @AutoMapper(target = PayTxnDtlUsdVo.class),
    @AutoMapper(target = RemoteTxnDtlUsdBo.class)
})
public class PayTxnDtlUsd {

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 交易类型:见字典表usd_txn_type
     */
    private String txnType;

    /**
     * 交易金额
     */
    private BigDecimal txnAmount;

    /**
     * 交易数量
     */
    private Long txnNum = 0l;

    /**
     * 交易商品
     */
    private String txnProduct = "";

    /**
     * 手续费
     */
    private BigDecimal txnFee;

    /**
     * 交易时间
     */
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime txnTime;

    /**
     * From交易账户
     */
    private Long fromUserId;

    /**
     * To交易账户
     */
    private Long toUserId;

    /**
     * 交易状态:0-失败，1-成功，2-处理中
     */
    private String txnStatus;

    /**
     * 交易描述
     */
    private String txnDesc;

    /**
     * 账户当前余额
     */
    private BigDecimal usdBalance;

    /**
     * 交易货币代码
     */
    private String fromCoin;

    /**
     * 交易货币金额
     */
    private BigDecimal fromAmount;

    /**
     * 兑换汇率
     */
    private BigDecimal exchgRate;

    /**
     * 关联的卡片交易id
     */
    private String relaTransactionid;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户所在城市
     */
    private String merchantCity;

    /**
     * 订单总价格
     */
    private BigDecimal totalPrice;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmt;

    /**
     * 优惠券id
     */
    private Long couponId;
}
