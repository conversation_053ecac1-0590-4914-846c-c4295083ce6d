package org.dromara.aset.service;

import org.dromara.aset.domain.bo.PayCoinBalanceBo;
import org.dromara.aset.domain.vo.PayCoinBalanceVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/19
 */

public interface IPayCoinBalanceService {

    boolean processRefundFromFrozenFunds(PayCoinBalanceBo bo);

    /**
     * 扣减余额
     * @param payCoinBalanceBo
     * @return
     */
    boolean deductBalance(PayCoinBalanceBo payCoinBalanceBo);

    /**
     * 增加余额
     * @param payCoinBalanceBo
     * @return
     */
    boolean IncreaseBalance(PayCoinBalanceBo payCoinBalanceBo);

    /**
     * 根据条件查询钱包列表
     * @param payCoinBalanceBo
     * @return
     */
    List<PayCoinBalanceVo> selectWalletListByParam(PayCoinBalanceBo payCoinBalanceBo);

    /**
     * 根据用户ID和币种查询钱包
     * @param payCoinBalanceBo
     * @return
     */
    PayCoinBalanceVo selectOneByUserIdAndCurrency(PayCoinBalanceBo payCoinBalanceBo);
}
