package org.dromara.aset.service;

import org.dromara.aset.domain.bo.PayCoinTxnDtlBo;
import org.dromara.aset.domain.bo.PayTxnDtlCodeBo;
import org.dromara.aset.domain.bo.PayTxnDtlUsdBo;
import org.dromara.aset.domain.vo.PayCoinTxnDtlVo;
import org.dromara.aset.domain.vo.PayTxnDtlCodeVo;
import org.dromara.aset.domain.vo.PayTxnDtlUsdVo;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * <AUTHOR>
 * @Date 2025/6/26
 */

public interface PayCoinService {

    /**
     * 根据条件查询币种交易分页列表
     * @param bo
     * @return
     */
    TableDataInfo<PayCoinTxnDtlVo> queryCoinTxnPageList(PayCoinTxnDtlBo bo);

    /**
     * 获取开卡币明细列表
     * @param bo
     * @return
     */
    TableDataInfo<PayTxnDtlCodeVo> queryCodeTxnPageList(PayTxnDtlCodeBo bo);
}
