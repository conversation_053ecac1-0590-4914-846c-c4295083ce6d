package org.dromara.aset.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.aset.domain.PayCouponGenCfg;
import org.dromara.aset.domain.PayCouponInfo;
import org.dromara.aset.domain.bo.PayCouponGenCfgBo;
import org.dromara.aset.domain.bo.PayCouponInfoBo;
import org.dromara.aset.domain.vo.PayCouponGenCfgVo;
import org.dromara.aset.mapper.PayCouponGenCfgMapper;
import org.dromara.aset.mapper.PayCouponInfoMapper;
import org.dromara.aset.service.PayCouponGenCfgService;
import org.dromara.aset.service.PayCouponInfoService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.enums.BusinessStatus;
import org.dromara.common.core.enums.CouponEnum;
import org.dromara.common.core.exception.BusinessException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.MessageUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.api.RemoteConfigService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

@Service
@RequiredArgsConstructor
@Slf4j
public class PayCouponGenCfgServiceImpl implements PayCouponGenCfgService {

    private final PayCouponGenCfgMapper payCouponGenCfgMapper;
    private final PayCouponInfoMapper payCouponInfoMapper;
    private final PayCouponInfoService payCouponInfoService;

    @DubboReference
    private final RemoteConfigService remoteConfigService;

    /**
     * 根据条件分页查询优惠券配置列表
     * @param bo
     * @return
     */
    @Override
    public IPage<PayCouponGenCfgVo> queryPageByParams(PayCouponGenCfgBo bo) {
        PageQuery pageQuery = new PageQuery(bo.getPageSize(), bo.getPageNum());

        // 只查询 查询者所创建的配置
        bo.setAdmUserId(LoginHelper.getUserId());
        LambdaQueryWrapper<PayCouponGenCfg> lqw = new LambdaQueryWrapper<>();
        lqw.eq(bo.getAdmUserId() != null, PayCouponGenCfg::getAdmUserId, bo.getAdmUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getCouponType()), PayCouponGenCfg::getCouponType, bo.getCouponType());
        lqw.eq(StringUtils.isNotBlank(bo.getCouponScenarios()), PayCouponGenCfg::getCouponScenarios, bo.getCouponScenarios());
        lqw.ge(StringUtils.isNotBlank(bo.getStartDate()), PayCouponGenCfg::getCreateTime, bo.getStartDate());
        lqw.le(StringUtils.isNotBlank(bo.getEndDate()), PayCouponGenCfg::getCreateTime, bo.getEndDate());
        lqw.like(StringUtils.isNotBlank(bo.getCouponCardType()), PayCouponGenCfg::getCouponCardType, bo.getCouponCardType());
        lqw.like(StringUtils.isNotBlank(bo.getCouponCardlevel()), PayCouponGenCfg::getCouponCardlevel, bo.getCouponCardlevel());
        lqw.like(StringUtils.isNotBlank(bo.getCouponNode()), PayCouponGenCfg::getCouponNode, bo.getCouponNode());
        lqw.orderByDesc(PayCouponGenCfg::getCreateTime);
        return payCouponGenCfgMapper.selectVoPage(pageQuery.build(), lqw);
    }

    /**
     * 新增优惠券配置
     * @param payCouponGenCfg
     * @return
     */
    @Override
    public boolean save(PayCouponGenCfg payCouponGenCfg) {
        return payCouponGenCfgMapper.insert(payCouponGenCfg) > 0;
    }

    /**
     * 修改优惠券配置
     * @param payCouponGenCfg
     * @return
     */
    @Override
    public boolean edit(PayCouponGenCfg payCouponGenCfg) {
        return payCouponGenCfgMapper.updateById(payCouponGenCfg) > 0;
    }

    /**
     * 根据配置ID删除优惠券配置
     * @param id
     * @return
     */
    @Override
    public boolean deleteById(Long id) {
        return payCouponGenCfgMapper.deleteById(id) > 0;
    }
}
