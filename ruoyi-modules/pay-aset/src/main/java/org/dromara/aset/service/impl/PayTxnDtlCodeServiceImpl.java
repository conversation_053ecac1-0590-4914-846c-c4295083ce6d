package org.dromara.aset.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.aset.domain.PayTxnDtlCode;
import org.dromara.aset.domain.bo.PayTxnDtlCodeBo;
import org.dromara.aset.domain.vo.PayTxnDtlCodeVo;
import org.dromara.aset.mapper.PayTxnDtlCodeMapper;
import org.dromara.aset.service.PayTxnDtlCodeService;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class PayTxnDtlCodeServiceImpl implements PayTxnDtlCodeService {

    private final PayTxnDtlCodeMapper payTxnDtlCodeMapper;


    /**
     * 根据条件查询分页数据
     * @param bo
     * @return
     */
    @Override
    public Page<PayTxnDtlCodeVo> queryPageByParams(PayTxnDtlCodeBo bo) {
        PageQuery pageQuery = new PageQuery(bo.getPageSize(), bo.getPageNum());
        LambdaQueryWrapper<PayTxnDtlCode> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StringUtils.isNotBlank(bo.getTxnType()), PayTxnDtlCode::getTxnType, bo.getTxnType())
            .in(bo.getTxnTypes() != null && !bo.getTxnTypes().isEmpty(), PayTxnDtlCode::getTxnType, bo.getTxnTypes())
            .in(bo.getUserIds() != null && !bo.getUserIds().isEmpty(), PayTxnDtlCode::getUserId, bo.getUserIds())
            .ge(StringUtils.isNotBlank(bo.getStartDate()), PayTxnDtlCode::getTxnTime, bo.getStartDate())
            .le(StringUtils.isNotBlank(bo.getEndDate()), PayTxnDtlCode::getTxnTime, bo.getEndDate())
            .orderByDesc(PayTxnDtlCode::getTxnTime);
        return payTxnDtlCodeMapper.selectVoPage(pageQuery.build(), lqw);
    }
}
