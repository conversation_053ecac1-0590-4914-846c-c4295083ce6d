package org.dromara.aset.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.aset.domain.PayTxnDtlUsd;
import org.dromara.aset.domain.bo.PayTxnDtlUsdBo;
import org.dromara.aset.domain.vo.PayTxnDtlUsdVo;

import java.util.List;

public interface PayTxnDtlUsdService {

    /**
     * 根据条件查询USD交易记录
     * @param bo
     * @return
     */
    List<PayTxnDtlUsdVo> queryListByParams(PayTxnDtlUsdBo bo);

    /**
     * 保存USD交易记录
     * @param payTxnDtlUsd
     * @return
     */
    boolean saveUsdTxnDtl(PayTxnDtlUsd payTxnDtlUsd);

    /**
     * 根据条件分页查询USD交易记录
     * @param bo
     * @return
     */
    Page<PayTxnDtlUsdVo> queryPageByParams(PayTxnDtlUsdBo bo);
}
