package org.dromara.aset.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.aset.domain.PayCouponGenCfg;
import org.dromara.aset.domain.PayCouponInfo;
import org.dromara.aset.domain.bo.PayCouponInfoBo;
import org.dromara.aset.domain.vo.PayCouponInfoVo;
import org.dromara.aset.mapper.PayCouponGenCfgMapper;
import org.dromara.aset.mapper.PayCouponInfoMapper;
import org.dromara.aset.service.PayCouponInfoService;
import org.dromara.common.core.enums.BusinessStatus;
import org.dromara.common.core.enums.CouponEnum;
import org.dromara.common.core.exception.BusinessException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.MessageUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.redis.utils.RandomStrUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Service
@RequiredArgsConstructor
@Slf4j
public class PayCouponInfoServiceImpl implements PayCouponInfoService {

    private final PayCouponInfoMapper payCouponInfoMapper;
    private final PayCouponGenCfgMapper payCouponGenCfgMapper;
    private final RandomStrUtils randomStrUtils;


    /**
     * 根据条件查询优惠券信息列表
     * @param bo
     * @return
     */
    @Override
    public List<PayCouponInfoVo> queryListByParams(PayCouponInfoBo bo) {
        return payCouponInfoMapper.selectVoList(new LambdaQueryWrapper<PayCouponInfo>()
            .eq(PayCouponInfo::getCouponGenCfgId, bo.getCouponGenCfgId()));
    }

    /**
     * 根据条件查询分页优惠券信息列表
     * @param bo
     * @return
     */
    @Override
    public IPage<PayCouponInfoVo> queryPageByParams(PayCouponInfoBo bo) {
        PageQuery pageQuery = new PageQuery(bo.getPageSize(), bo.getPageNum());

        LambdaQueryWrapper<PayCouponInfo> lqw = new LambdaQueryWrapper<>();
        lqw.eq(bo.getCouponGenCfgId() != null, PayCouponInfo::getCouponGenCfgId, bo.getCouponGenCfgId());
        lqw.eq(bo.getAdmUserId() != null, PayCouponInfo::getAdmUserId, bo.getAdmUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PayCouponInfo::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getCouponNm()), PayCouponInfo::getCouponNm, bo.getCouponNm());
        lqw.eq(StringUtils.isNotBlank(bo.getCouponType()), PayCouponInfo::getCouponType, bo.getCouponType());
        lqw.eq(StringUtils.isNotBlank(bo.getCouponScenarios()), PayCouponInfo::getCouponScenarios, bo.getCouponScenarios());
        lqw.and(StringUtils.isNotBlank(bo.getCouponCardType()), wrapper ->
            wrapper.like(PayCouponInfo::getCouponCardType, bo.getCouponCardType())
                .or()
                .isNull(PayCouponInfo::getCouponCardType)
                .or()
                .eq(PayCouponInfo::getCouponCardType, "")
        );
        lqw.and(StringUtils.isNotBlank(bo.getCouponCardlevel()), wrapper ->
            wrapper.like(PayCouponInfo::getCouponCardlevel, bo.getCouponCardlevel())
                .or()
                .isNull(PayCouponInfo::getCouponCardlevel)
                .or()
                .eq(PayCouponInfo::getCouponCardlevel, "")
        );
        lqw.and(StringUtils.isNotBlank(bo.getCouponNode()), wrapper ->
            wrapper.like(PayCouponInfo::getCouponNode, bo.getCouponNode())
                .or()
                .isNull(PayCouponInfo::getCouponNode)
                .or()
                .eq(PayCouponInfo::getCouponNode, "")
        );
        lqw.eq(StringUtils.isNotBlank(bo.getStartDate()), PayCouponInfo::getCreateTime, bo.getStartDate());
        lqw.eq(StringUtils.isNotBlank(bo.getEndDate()), PayCouponInfo::getCreateTime, bo.getEndDate());
        lqw.eq(StringUtils.isNotBlank(bo.getStartExpDate()), PayCouponInfo::getExpDate, bo.getStartExpDate());
        lqw.eq(StringUtils.isNotBlank(bo.getEndExpDate()), PayCouponInfo::getExpDate, bo.getEndExpDate());
        lqw.orderByDesc(PayCouponInfo::getExpDate);
        return payCouponInfoMapper.selectVoPage(pageQuery.build(), lqw);
    }

    /**
     * 修改优惠券信息
     * @param bo
     */
    @Override
    public boolean updateCouponInfoByParams(PayCouponInfoBo bo) {
        return payCouponInfoMapper.update(new LambdaUpdateWrapper<PayCouponInfo>()
            .set(PayCouponInfo::getStatus, bo.getStatus())
            .eq(PayCouponInfo::getId, bo.getId())) > 0;
    }

    /**
     * 根据优惠券配置ID 保存优惠券信息
     * @param payCouponInfoBo
     * @return
     */
    @Override
    public boolean saveForCfg(PayCouponInfoBo payCouponInfoBo) {
        List<PayCouponInfo> list = new ArrayList<>();
        PayCouponGenCfg payCouponGenCfg = payCouponGenCfgMapper.selectById(payCouponInfoBo.getCouponGenCfgId());
        for (int i = 0; i < payCouponGenCfg.getCouponTot(); i++) {
            PayCouponInfo payCouponInfo = new PayCouponInfo();
            BeanUtils.copyProperties(payCouponGenCfg, payCouponInfo, "id");
            payCouponInfo.setCouponGenCfgId(payCouponGenCfg.getId());
            if (payCouponGenCfg.getCouponUsageLimit() == 0) {
                payCouponInfo.setCouponNm(payCouponGenCfg.getCouponNm());
            } else {
                payCouponInfo.setCouponNm(randomStrUtils.generate(10));
            }
            payCouponInfo.setCouponUsageCnt(0);
            payCouponInfo.setStatus(BusinessStatus.COUPON_VALID.getValue());

            list.add(payCouponInfo);
        }
        return payCouponInfoMapper.insertBatch(list);
    }

    /**
     * 批量保存
     * @param list
     * @return
     */
    @Override
    public boolean saveAll(List<PayCouponInfo> list) {
        return payCouponInfoMapper.insertOrUpdateBatch(list);
    }

    /**
     * 根据优惠券配置ID删除
     * @param id
     * @return
     */
    @Override
    public boolean deleteByCfgId(Long id) {
        return payCouponInfoMapper.delete(new LambdaQueryWrapper<PayCouponInfo>()
            .eq(PayCouponInfo::getCouponGenCfgId, id)
            .eq(PayCouponInfo::getStatus, BusinessStatus.COUPON_WRITE_OFF.getValue())) > 0;
    }

    /**
     * 修改优惠券信息
     * @param payCouponInfo
     * @return
     */
    @Override
    public boolean updateCouponInfo(PayCouponInfo payCouponInfo) {
        return payCouponInfoMapper.updateById(payCouponInfo) > 0;
    }

    /**
     * 发放优惠券
     * @param bo
     * @return
     */
    @Override
    public boolean grantCoupon(PayCouponInfoBo bo) {
        PayCouponInfo info = payCouponInfoMapper.selectById(bo.getId());
        if (info == null) {
            throw new BusinessException(MessageUtils.message("coupon.error1"));
        }
        if (info.getUserId() != null) {
            throw new BusinessException(MessageUtils.message("coupon.error2"));
        }
        return payCouponInfoMapper.update(new LambdaUpdateWrapper<PayCouponInfo>()
            .set(PayCouponInfo::getUserId, bo.getUserId())
            .eq(PayCouponInfo::getId, bo.getId())) > 0;
    }

    /**
     * 核销
     * @param bo
     * @return
     */
    @Override
    public boolean deleteCouponInfo(PayCouponInfoBo bo) {
        return payCouponInfoMapper.update(new LambdaUpdateWrapper<PayCouponInfo>()
            .set(PayCouponInfo::getStatus, BusinessStatus.COUPON_WRITE_OFF.getValue())
            .eq(PayCouponInfo::getId, bo.getId())) > 0;
    }
}
