package org.dromara.aset.domain.bo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.DeleteGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.core.validate.coupon.GrantCoupon;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PayCouponInfoBo extends PageQuery {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class, GrantCoupon.class, DeleteGroup.class })
    private Long id;

    /**
     * 优惠券配置ID
     */
    private Long couponGenCfgId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { GrantCoupon.class })
    private Long userId;

    /**
     * 优惠券管理用户ID
     */
    private Long admUserId;

    /**
     * 优惠券号码
     */
    private String couponNm;

    /**
     * D-折扣券 M-满减券
     */
    @NotBlank(message = "优惠券类型不能为空", groups = { EditGroup.class })
    private String couponType;

    /**
     * NEW-开卡、TOPUP卡充值、NODE-节点购买
     */
    @NotBlank(message = "优惠券场景不能为空", groups = { EditGroup.class })
    private String couponScenarios;

    /**
     * 可使用卡类型
     */
    private String couponCardType;

    /**
     * 可使用卡等级产品
     */
    private String couponCardlevel;

    /**
     * 可使用节点产品
     */
    private String couponNode;

    /**
     * 折扣货币代码
     */
    private String discountCoin;

    /**
     * 折扣比例
     */
    private BigDecimal discountRate;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmt;

    /**
     * 最高抵扣金额
     */
    private BigDecimal maxDiscountAmt;

    /**
     * 使用最低消费金额
     */
    @NotNull(message = "最低消费金额不能为空", groups = { EditGroup.class })
    private BigDecimal minSpendAmt;

    /**
     * 生效日期
     */
    @NotNull(message = "生效日期不能为空", groups = { EditGroup.class })
    private Date eftDate;

    /**
     * 失效日期
     */
    @NotNull(message = "失效日期不能为空", groups = { EditGroup.class })
    private Date expDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 使用时间
     */
    private Date usedTime;

    /**
     * 使用用户id
     */
    private Long useUserId;

    /**
     * 状态（0有效，1已使用, 2核销，3过期）
     */
    private String status;

    /**
     * 优惠券使用次数限制(0-无限使用)
     */
    private Integer couponUsageLimit;

    /**
     * 优惠券使用次数
     */
    private Integer couponUsageCnt;

    /**
     * 来源
     */
    private String source;

    /**
     * 失效开始时间
     */
    private String startExpDate;

    /**
     * 失效结束时间
     */
    private String endExpDate;

    /**
     * 创建开始时间
     */
    private String startDate;
    /**
     * 创建结束时间
     */
    private String endDate;
}
