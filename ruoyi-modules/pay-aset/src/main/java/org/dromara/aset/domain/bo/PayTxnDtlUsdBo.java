package org.dromara.aset.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import org.dromara.aset.api.domain.bo.RemoteCoinBo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.List;

@Data
@AutoMappers({
    @AutoMapper(target = RemoteCoinBo.class),
})
public class PayTxnDtlUsdBo extends PageQuery {
    /**
     * 交易类型
     */
    private String txnCode;

    /**
     * 状态
     */
    private Integer txnStatus;

    /**
     * 邮箱
     */
    private String userName;

    /**
     * UID
     */
    private String nickName;

    /**
     * 开始时间
     */
    private String  startDate;

    /**
     * 结束时间
     */
    private String  endDate;

    /**
     * 用户ID列表
     */
    private List<Long> userIds;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 交易ID列表
     */
    private List<Long> txnIds;

    /**
     * 交易类型列表
     */
    private List<String> txnTypeList;

    /**
     * 交易类型
     */
    private String txnType;
}
