package org.dromara.aset.domain.bo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.DeleteGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PayCouponGenCfgBo extends PageQuery {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class, DeleteGroup.class})
    private Long id;

    /**
     * 优惠券管理用户ID
     */
    private Long admUserId;

    /**
     * D-折扣券 M-满减券
     */
    @NotBlank(message = "优惠券类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String couponType;

    /**
     * NEW-开卡、TOPUP卡充值、NODE-节点购买
     */
    @NotBlank(message = "优惠券场景不能为空", groups = { AddGroup.class, EditGroup.class })
    private String couponScenarios;

    /**
     * 可使用卡类型
     */
    private String couponCardType;

    /**
     * 可使用卡等级产品
     */
    private String couponCardlevel;

    /**
     * 可使用节点产品
     */
    private String couponNode;

    /**
     * 折扣货币代码
     */
    private String discountCoin;

    /**
     * 折扣比例
     */
    private BigDecimal discountRate;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmt;

    /**
     * 最高抵扣金额
     */
    private BigDecimal maxDiscountAmt;

    /**
     * 使用最低消费金额
     */
    @NotNull(message = "最低消费金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal minSpendAmt;

    /**
     * 生效日期
     */
    @NotNull(message = "生效日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date eftDate;

    /**
     * 失效日期
     */
    @NotNull(message = "失效日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date expDate;

    /**
     * 总共发放数量
     */
    @NotNull(message = "发放数量不能为空", groups = { AddGroup.class, EditGroup.class })
    @Min(value = 1, message = "发放数量最小为1")
    private Integer couponTot;

    /**
     * 已使用数量
     */
    private Integer couponUsed;

    /**
     * 剩余可用数量
     */
    private Integer couponAva;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 优惠券使用次数限制(0-无限使用)
     */
    @NotNull(message = "可使用次数不能为空", groups = { AddGroup.class, EditGroup.class })
    @Min(value = 1, message = "可使用次数最小为1")
    private Integer couponUsageLimit;

    /**
     * 创建开始时间
     */
    private String startDate;
    /**
     * 创建结束时间
     */
    private String endDate;

    /**
     * 优惠券码
     */
    private String couponNm;
}
