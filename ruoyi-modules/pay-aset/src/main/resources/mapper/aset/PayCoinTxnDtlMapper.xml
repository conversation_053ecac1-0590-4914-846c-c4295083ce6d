<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.aset.mapper.PayCoinTxnDtlMapper">

    <resultMap type="org.dromara.aset.domain.vo.PayCoinTxnDtlVo" id="payCoinBalanceResult">
    </resultMap>

    <select id="selectWithdrawalPage" resultType="org.dromara.aset.domain.vo.PayCoinTxnDtlVo">
        select * from k_meta_coin_txn_dtl c
        left join withdraw_process_rec w on c.txn_id = w.order_id
        ${ew.getCustomSqlSegment}
    </select>

    <update id="processRefundFromFrozenFunds"
            parameterType="org.dromara.aset.domain.bo.PayCoinTxnDtlBo">
        update k_meta_coin_txn_dtl
        set txn_status=1
        where txn_code = 'c1010'
          and txn_id = #{bo.txnId}
          and txn_status = 2;
    </update>

    <!-- 7个不同表，需要基于tenant_id选择 -->
    <update id="processRefundFromFrozenFundsForBitago">
        update kbitago_meta_coin_txn_dtl
        set txn_status=#{txnStatus}
        where txn_code = 'c1010'
          and txn_id = #{txnId}
          and txn_status = 2;
    </update>
    <update id="processRefundFromFrozenFundsForMeta">
        update kmeta_meta_coin_txn_dtl
        set txn_status=#{txnStatus}
        where txn_code = 'c1010'
          and txn_id = #{txnId}
          and txn_status = 2;
    </update>
    <update id="processRefundFromFrozenFundsForRokutel">
        update krokutel_meta_coin_txn_dtl
        set txn_status=#{txnStatus}
        where txn_code = 'c1010'
          and txn_id = #{txnId}
          and txn_status = 2;
    </update>
    <update id="processRefundFromFrozenFundsForSwipex">
        update kswipex_meta_coin_txn_dtl
        set txn_status=#{txnStatus}
        where txn_code = 'c1010'
          and txn_id = #{txnId}
          and txn_status = 2;
    </update>
    <update id="processRefundFromFrozenFundsForVoopay">
        update kvoopay_meta_coin_txn_dtl
        set txn_status=#{txnStatus}
        where txn_code = 'c1010'
          and txn_id = #{txnId}
          and txn_status = 2;
    </update>
    <update id="processRefundFromFrozenFundsForXcode">
        update kxcode_meta_coin_txn_dtl
        set txn_status=#{txnStatus}
        where txn_code = 'c1010'
          and txn_id = #{txnId}
          and txn_status = 2;
    </update>
    <update id="processRefundFromFrozenFundsForZombie">
        update kzombie_meta_coin_txn_dtl
        set txn_status=#{txnStatus}
        where txn_code = 'c1010'
          and txn_id = #{txnId}
          and txn_status = 2;
    </update>


</mapper>
