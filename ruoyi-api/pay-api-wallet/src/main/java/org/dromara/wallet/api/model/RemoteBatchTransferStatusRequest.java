package org.dromara.wallet.api.model;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 批量查询转账状态请求DTO
 * 用于Dubbo接口批量查询多个转账订单状态
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RemoteBatchTransferStatusRequest implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 请求ID列表
     * 最多支持100个请求ID的批量查询
     */
    @NotEmpty(message = "请求ID列表不能为空")
    @Size(max = 100, message = "批量查询最多支持100个请求ID")
    private List<String> requestIds;

    /**
     * 是否包含详细记录信息
     * true: 返回完整的转账记录详情
     * false: 只返回基本状态信息（提升性能）
     */
    @Builder.Default
    private Boolean includeDetails = true;

    /**
     * 是否只返回失败的记录
     * true: 只返回状态为失败(3)或超时(4)的记录
     * false: 返回所有记录
     */
    @Builder.Default
    private Boolean onlyFailures = false;

    /**
     * 是否只返回处理中的记录
     * true: 只返回状态为待确认(0)或确认中(1)的记录
     * false: 返回所有记录
     */
    @Builder.Default
    private Boolean onlyProcessing = false;
}
