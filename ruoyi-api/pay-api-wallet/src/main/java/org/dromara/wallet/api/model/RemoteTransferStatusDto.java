package org.dromara.wallet.api.model;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 远程转账状态查询结果DTO
 * 用于Dubbo接口返回转账状态信息
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RemoteTransferStatusDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 转账状态
     * -1: 订单不存在
     * 0: 待确认
     * 1: 确认中
     * 2: 已确认
     * 3: 确认失败
     * 4: 超时
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDescription;

    /**
     * 转账记录总数
     */
    private Integer totalRecords;

    /**
     * 已确认记录数
     */
    private Integer confirmedRecords;

    /**
     * 失败记录数
     */
    private Integer failedRecords;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 查询时间
     */
    private Date queryTime;

    /**
     * 转账记录详情列表
     */
    private List<TransferRecordDetail> records;

    /**
     * 转账记录详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TransferRecordDetail implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 记录ID
         */
        private Long id;

        /**
         * 交易哈希
         */
        private String transactionHash;

        /**
         * 区块链名称
         */
        private String chainName;

        /**
         * 确认状态
         */
        private Integer confirmationStatus;

        /**
         * 所需确认数
         */
        private Integer requiredConfirmations;

        /**
         * 实际确认数
         */
        private Integer actualConfirmations;

        /**
         * 区块高度
         */
        private Long blockHeight;

        /**
         * 确认开始时间
         */
        private Date startTime;

        /**
         * 确认结束时间
         */
        private Date endTime;

        /**
         * 确认耗时(毫秒)
         */
        private Long confirmationTimeMs;

        /**
         * 重试次数
         */
        private Integer retryCount;

        /**
         * 错误信息
         */
        private String errorMessage;

        /**
         * 转账类型
         */
        private String transferType;
    }
}
