package org.dromara.wallet.api;


import org.dromara.wallet.api.model.RemoteWalletDto;
import org.dromara.wallet.api.model.RemoteTransferStatusDto;
import org.dromara.wallet.api.model.RemoteBatchTransferStatusRequest;
import org.dromara.wallet.api.model.RemoteBatchTransferStatusResponse;

/**
 * 钱包服务
 *
 * <AUTHOR>
 */
public interface RemoteWalletService {

    /**
     * 提款
     */
     String withdraw(RemoteWalletDto dto);

    /**
     * 根据请求ID查询转账状态
     *
     * @param requestId 请求ID
     * @return 转账状态信息
     */
    RemoteTransferStatusDto queryTransferStatusByRequestId(String requestId);

    /**
     * 批量查询转账状态
     *
     * @param request 批量查询请求
     * @return 批量查询结果
     */
    RemoteBatchTransferStatusResponse batchQueryTransferStatus(RemoteBatchTransferStatusRequest request);

}
