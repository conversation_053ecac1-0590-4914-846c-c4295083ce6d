package org.dromara.wallet.api.model;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 批量查询转账状态响应DTO
 * 用于Dubbo接口返回批量查询结果
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RemoteBatchTransferStatusResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 查询是否成功
     */
    private Boolean success;

    /**
     * 错误信息（查询失败时）
     */
    private String errorMessage;

    /**
     * 查询时间
     */
    private Date queryTime;

    /**
     * 请求的总数量
     */
    private Integer totalRequested;

    /**
     * 找到的记录数量
     */
    private Integer totalFound;

    /**
     * 未找到的请求ID列表
     */
    private List<String> notFoundRequestIds;

    /**
     * 转账状态结果列表
     * Key: requestId
     * Value: 转账状态信息
     */
    private Map<String, RemoteTransferStatusDto> results;

    /**
     * 统计信息
     */
    private BatchStatistics statistics;

    /**
     * 批量查询统计信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 待确认数量
         */
        private Integer pendingCount;

        /**
         * 确认中数量
         */
        private Integer processingCount;

        /**
         * 已确认数量
         */
        private Integer confirmedCount;

        /**
         * 失败数量
         */
        private Integer failedCount;

        /**
         * 超时数量
         */
        private Integer timeoutCount;

        /**
         * 按区块链分组统计
         * Key: 区块链名称 (TRON/BSC/ETH/ARB/BASE/SOLANA)
         * Value: 该链的记录数量
         */
        private Map<String, Integer> chainStatistics;

        /**
         * 按转账类型分组统计
         * Key: 转账类型 (native/token/collection)
         * Value: 该类型的记录数量
         */
        private Map<String, Integer> typeStatistics;
    }

    /**
     * 创建成功响应
     */
    public static RemoteBatchTransferStatusResponse success(
            Map<String, RemoteTransferStatusDto> results,
            List<String> notFoundRequestIds,
            BatchStatistics statistics) {
        
        return RemoteBatchTransferStatusResponse.builder()
            .success(true)
            .queryTime(new Date())
            .totalRequested(results.size() + (notFoundRequestIds != null ? notFoundRequestIds.size() : 0))
            .totalFound(results.size())
            .notFoundRequestIds(notFoundRequestIds)
            .results(results)
            .statistics(statistics)
            .build();
    }

    /**
     * 创建失败响应
     */
    public static RemoteBatchTransferStatusResponse failure(String errorMessage) {
        return RemoteBatchTransferStatusResponse.builder()
            .success(false)
            .errorMessage(errorMessage)
            .queryTime(new Date())
            .totalRequested(0)
            .totalFound(0)
            .build();
    }
}
