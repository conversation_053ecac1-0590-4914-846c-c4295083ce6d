package org.dromara.aset.api;

import org.dromara.aset.api.domain.bo.RemoteCoinBo;
import org.dromara.aset.api.domain.bo.RemoteCoinTxnDtlBo;
import org.dromara.aset.api.domain.bo.RemoteTxnDtlUsdBo;
import org.dromara.aset.api.domain.vo.RemoteCoinTxnDtlVo;
import org.dromara.aset.api.domain.vo.RemoteCoinVo;
import org.dromara.aset.api.domain.vo.RemoteTxnDtlUsdVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/19
 */

public interface RemoteCoinService {

    /**
     * 根据条件查询钱包列表
     * @param bo
     * @return
     */
    List<RemoteCoinVo> selectWalletListByParam(RemoteCoinBo bo);

    /**
     * 根据条件查询币种交易记录
     * @param bo
     * @return
     */
    List<RemoteCoinTxnDtlVo> queryCoinTxnDtlListByParams(RemoteCoinBo bo);

    /**
     * 根据条件查询USD交易记录
     * @param bo
     * @return
     */
    List<RemoteTxnDtlUsdVo> queryTxnDtlUsdListByParams(RemoteCoinBo bo);

    /**
     * 增加钱包余额
     * @param bo
     * @return
     */
    boolean IncreaseCoinBalance(RemoteCoinBo bo);

    /**
     * 根据用户ID和币种查询钱包
     * @param bo
     * @return
     */
    RemoteCoinVo selectOneByUserIdAndCurrency(RemoteCoinBo bo);

    /**
     * 保存USD交易记录
     * @param bo
     * @return
     */
    boolean saveUsdTxnDtl(RemoteTxnDtlUsdBo bo);

    /**
     * 保存币种交易记录
     * @param coinTxnDtl
     * @return
     */
    boolean saveCoinTxnDtl(RemoteCoinTxnDtlBo coinTxnDtl);
}
