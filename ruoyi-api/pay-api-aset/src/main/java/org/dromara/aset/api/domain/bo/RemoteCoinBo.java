package org.dromara.aset.api.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import org.dromara.common.core.validate.coin.FundsOperation;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/19
 */
@Data
@AutoMappers({
    @AutoMapper(target = RemoteCoinBo.class),
})
public class RemoteCoinBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID列表
     */
    private List<Long> userIds;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 交易类型列表
     */
    private List<String> txnTypeList;

    /**
     * 交易ID列表
     */
    private List<Long> txnIds;

    /**
     * 币种
     */
    private String currency;

    /**
     * 金额
     */
    private BigDecimal amount;
}
