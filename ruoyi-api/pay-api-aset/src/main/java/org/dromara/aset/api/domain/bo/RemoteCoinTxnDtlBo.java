package org.dromara.aset.api.domain.bo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class RemoteCoinTxnDtlBo {

    /**
     * 主键
     */
    private Long txnId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 0 支出  1 收入
     */
    private Integer recordType;

    /**
     * d1010充值|c1010提币|t1010转账
     */
    private String txnCode;

    /**
     * 链上交易号
     */
    private String receiptNo;

    /**
     * 交易钱包付款人
     */
    private String fromAddress;

    /**
     * 交易收款人
     */
    private String toAddress;

    /**
     * 交易货币(USDT)
     */
    private String txnCoin;

    /**
     * 交易状态:0-待审核，1-已完成，2-已拒绝
     */
    private Integer txnStatus;

    /**
     * 交易描述
     */
    private String txnDesc;

    /**
     * 交易金额
     */
    private BigDecimal txnAmount;

    /**
     * 手续费
     */
    private BigDecimal txnFee;

    /**
     * 交易时间
     */
    private Date txnTime;

    /**
     * to交易对货币代码
     */
    private String toCoin;

    /**
     * to交换对货币金额
     */
    private BigDecimal toAmount;

    /**
     * 交易对汇率
     */
    private BigDecimal exchgRate;

    /**
     * 客户资产余额
     */
    private BigDecimal userBalance;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 币种网络
     */
    private String coinNet;

    /**
     * gate用户卡码费
     */
    private BigDecimal gateCodeFee;

    /**
     * 交易商品
     */
    private String txnProduct;

    /**
     * 订单总价格
     */
    private BigDecimal totalPrice;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmt;

    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * from交易对货币代码
     */
    private String fromCoin;

    /**
     * from交换对货币金额
     */
    private BigDecimal fromAmount;

    /**
     * 关联流水ID
     */
    private Long relaTxnid;
}
