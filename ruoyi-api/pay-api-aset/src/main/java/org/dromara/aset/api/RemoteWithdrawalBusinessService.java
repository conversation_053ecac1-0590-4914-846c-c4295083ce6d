package org.dromara.aset.api;

import java.math.BigDecimal;

/**
 * 提款业务服务接口
 *
 * <p>定义提款完成后的业务处理接口</p>
 *
 * <AUTHOR>
 * @date 2025/7/26
 */
public interface RemoteWithdrawalBusinessService {

    /**
     * 处理提款完成业务逻辑
     *
     * @param requestId       转账请求ID
     * @param transactionHash 区块链交易哈希
     * @param chainName       区块链名称
     * @param confirmations   确认数量
     * @return 是否处理成功
     */
    boolean processWithdrawalCompletion(String requestId, String transactionHash,
                                        String chainName, String coinType,
                                        Integer confirmations, BigDecimal amountChange,
                                        String tenantId);
}
