package org.dromara.aset.api.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

@Data
public class RemoteTxnDtlUsdVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 交易类型:见字典表usd_txn_type
     */
    private String txnType;

    /**
     * 交易金额
     */
    private BigDecimal txnAmount;

    /**
     * 交易数量
     */
    private BigInteger txnNum;

    /**
     * 交易商品
     */
    private String txnProduct;

    /**
     * 手续费
     */
    private BigDecimal txnFee;

    /**
     * 交易时间
     */
    private Date txnTime;

    /**
     * From交易账户
     */
    private Long fromUserId;

    /**
     * From交易账户
     */
    private String fromUserName;

    /**
     * To交易账户
     */
    private Long toUserId;

    /**
     * To交易账户
     */
    private String toUserName;

    /**
     * 交易状态:0-失败，1-成功，2-处理中
     */
    private String txnStatus;

    /**
     * 交易描述
     */
    private String txnDesc;

    /**
     * 账户当前余额
     */
    private BigDecimal usdBalance;

    /**
     * 交易货币代码
     */
    private String fromCoin;

    /**
     * 交易货币金额
     */
    private BigDecimal fromAmount;

    /**
     * 兑换汇率
     */
    private BigDecimal exchgRate;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户所在城市
     */
    private String merchantCity;

    /**
     * 卡类型：01-银卡，02-金卡，03-黑金卡
     */
    private String codeType;

    private String relaTransactionId;
}
