{"flowCode": "leave3", "flowName": "请假申请-并行网关", "category": "1", "version": "1", "formCustom": "N", "formPath": "/workflow/leaveEdit/index", "nodeList": [{"nodeType": 0, "nodeCode": "a80ecf9f-f465-4ae5-a429-e30ec5d0f957", "nodeName": "开始", "nodeRatio": 0.0, "coordinate": "380,220|380,220", "skipAnyNode": "N", "formCustom": "N", "skipList": [{"nowNodeCode": "a80ecf9f-f465-4ae5-a429-e30ec5d0f957", "nextNodeCode": "b7bbb571-06de-455c-8083-f83c07bf0b99", "skipType": "PASS", "coordinate": "400,220;470,220"}]}, {"nodeType": 1, "nodeCode": "b7bbb571-06de-455c-8083-f83c07bf0b99", "nodeName": "申请人", "nodeRatio": 0.0, "coordinate": "520,220|520,220", "skipAnyNode": "N", "formCustom": "N", "skipList": [{"nowNodeCode": "b7bbb571-06de-455c-8083-f83c07bf0b99", "nextNodeCode": "84d7ed24-bb44-4ba1-bf1f-e6f5092d3f0a", "skipType": "PASS", "coordinate": "570,220;655,220"}]}, {"nodeType": 4, "nodeCode": "84d7ed24-bb44-4ba1-bf1f-e6f5092d3f0a", "nodeRatio": 0.0, "coordinate": "680,220", "skipAnyNode": "N", "formCustom": "N", "skipList": [{"nowNodeCode": "84d7ed24-bb44-4ba1-bf1f-e6f5092d3f0a", "nextNodeCode": "4b7743cd-940c-431b-926f-e7b614fbf1fe", "skipType": "PASS", "coordinate": "680,195;680,140;750,140"}, {"nowNodeCode": "84d7ed24-bb44-4ba1-bf1f-e6f5092d3f0a", "nextNodeCode": "762cb975-37d8-4276-b6db-79a4c3606394", "skipType": "PASS", "coordinate": "680,245;680,300;750,300"}]}, {"nodeType": 1, "nodeCode": "4b7743cd-940c-431b-926f-e7b614fbf1fe", "nodeName": "市场部", "permissionFlag": "role:1", "nodeRatio": 0.0, "coordinate": "800,140|800,140", "skipAnyNode": "N", "formCustom": "N", "skipList": [{"nowNodeCode": "4b7743cd-940c-431b-926f-e7b614fbf1fe", "nextNodeCode": "b66b6563-f9fe-41cc-a782-f7837bb6f3d2", "skipType": "PASS", "coordinate": "850,140;920,140;920,195"}]}, {"nodeType": 4, "nodeCode": "b66b6563-f9fe-41cc-a782-f7837bb6f3d2", "nodeRatio": 0.0, "coordinate": "920,220", "skipAnyNode": "N", "formCustom": "N", "skipList": [{"nowNodeCode": "b66b6563-f9fe-41cc-a782-f7837bb6f3d2", "nextNodeCode": "23e7429e-2b47-4431-b93e-40db7c431ce6", "skipType": "PASS", "coordinate": "945,220;975,220;975,220;960,220;960,220;990,220"}]}, {"nodeType": 1, "nodeCode": "23e7429e-2b47-4431-b93e-40db7c431ce6", "nodeName": "CEO", "permissionFlag": "1", "nodeRatio": 0.0, "coordinate": "1040,220|1040,220", "skipAnyNode": "N", "formCustom": "N", "skipList": [{"nowNodeCode": "23e7429e-2b47-4431-b93e-40db7c431ce6", "nextNodeCode": "f5ace37f-5a5e-4e64-a6f6-913ab9a71cd1", "skipType": "PASS", "coordinate": "1090,220;1140,220"}]}, {"nodeType": 2, "nodeCode": "f5ace37f-5a5e-4e64-a6f6-913ab9a71cd1", "nodeName": "结束", "nodeRatio": 0.0, "coordinate": "1160,220|1160,220", "skipAnyNode": "N", "formCustom": "N"}, {"nodeType": 1, "nodeCode": "762cb975-37d8-4276-b6db-79a4c3606394", "nodeName": "综合部", "permissionFlag": "role:3@@role:4", "nodeRatio": 0.0, "coordinate": "800,300|800,300", "skipAnyNode": "N", "formCustom": "N", "skipList": [{"nowNodeCode": "762cb975-37d8-4276-b6db-79a4c3606394", "nextNodeCode": "b66b6563-f9fe-41cc-a782-f7837bb6f3d2", "skipType": "PASS", "coordinate": "850,300;920,300;920,245"}]}]}