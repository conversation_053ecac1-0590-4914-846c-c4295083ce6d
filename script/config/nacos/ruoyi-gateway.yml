# 安全配置
security:
  # 不校验白名单
  ignore:
    whites:
      - /auth/code
      - /auth/logout
      - /auth/login
      - /auth/binding/*
      - /auth/register
      - /auth/tenant/list
      - /resource/sms/code
      - /resource/sse/close
      - /*/v3/api-docs
      - /*/error
      - /csrf
      - /warm-flow-ui/**

spring:
  cloud:
    # 网关配置
    gateway:
      # 打印请求日志(自定义)
      requestLog: true
      discovery:
        locator:
          lowerCaseServiceId: true
          enabled: true
      routes:
        # 认证中心
        - id: ruoyi-auth
          uri: lb://ruoyi-auth
          predicates:
            - Path=/auth/**
          filters:
            - StripPrefix=1
        # 代码生成
        - id: ruoyi-gen
          uri: lb://ruoyi-gen
          predicates:
            - Path=/tool/**
          filters:
            - StripPrefix=1
        # 系统模块
        - id: ruoyi-system
          uri: lb://ruoyi-system
          predicates:
            - Path=/system/**,/monitor/**
          filters:
            - StripPrefix=1
        # 资源服务
        - id: ruoyi-resource
          uri: lb://ruoyi-resource
          predicates:
            - Path=/resource/**
          filters:
            - StripPrefix=1
        # workflow服务
        - id: ruoyi-workflow
          uri: lb://ruoyi-workflow
          predicates:
            - Path=/workflow/**
          filters:
            - StripPrefix=1
        # warm-flow服务
        - id: warm-flow
          uri: lb://ruoyi-workflow
          predicates:
            - Path=/warm-flow-ui/**,/warm-flow/**
        # 演示服务
        - id: ruoyi-demo
          uri: lb://ruoyi-demo
          predicates:
            - Path=/demo/**
          filters:
            - StripPrefix=1
        # MQ演示服务
        - id: ruoyi-test-mq
          uri: lb://ruoyi-test-mq
          predicates:
            - Path=/test-mq/**
          filters:
            - StripPrefix=1

    # sentinel 配置
    sentinel:
      filter:
        enabled: false
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            server-addr: ${spring.cloud.nacos.server-addr}
            dataId: sentinel-${spring.application.name}.json
            groupId: ${spring.cloud.nacos.config.group}
            username: ${spring.cloud.nacos.username}
            password: ${spring.cloud.nacos.password}
            namespace: ${spring.profiles.active}
            data-type: json
            rule-type: gw-flow
