spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${datasource.job.url}
    username: ${datasource.job.username}
    password: ${datasource.job.password}
    hikari:
      connection-timeout: 30000
      validation-timeout: 5000
      minimum-idle: 10
      maximum-pool-size: 20
      idle-timeout: 600000
      max-lifetime: 900000
      keepaliveTime: 30000
  cloud:
    nacos:
      discovery:
        metadata:
          # 解决 er 服务有 context-path 无法监控问题
          management.context-path: ${server.servlet.context-path}/actuator
          # 监控账号密码
          username: ruoyi
          userpassword: 123456

# snail-job 服务端配置
snail-job:
  # 拉取重试数据的每批次的大小
  retry-pull-page-size: 1000
  # 拉取重试数据的每批次的大小
  job-pull-page-size: 1000
  # 服务器端口
  server-port: 17888
  # 日志保存时间(单位: day)
  log-storage: 7
  rpc-type: grpc
